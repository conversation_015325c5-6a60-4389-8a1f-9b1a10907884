#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ultimate Co-founder Platform Startup Script
Comprehensive startup script that handles both frontend and backend
"""

import os
import sys
import subprocess
import time
import threading
import signal
from pathlib import Path

class UltimateCofounderStarter:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.backend_dir = self.project_root / 'backend'
        self.processes = []
        self.running = True
        
    def print_banner(self):
        """Print startup banner"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                Ultimate Co-founder Platform                  ║
║                    Startup Manager                           ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def check_prerequisites(self):
        """Check if all prerequisites are met"""
        print("Checking prerequisites...")
        
        # Check Python
        if sys.version_info < (3, 8):
            print("ERROR: Python 3.8+ required")
            return False
        print(f"✓ Python {sys.version.split()[0]}")
        
        # Check Node.js
        try:
            result = subprocess.run(['node', '--version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                version = result.stdout.strip()
                major_version = int(version[1:].split('.')[0])
                if major_version >= 16:
                    print(f"✓ Node.js {version}")
                else:
                    print(f"ERROR: Node.js 16+ required (found {version})")
                    return False
            else:
                print("ERROR: Node.js not found")
                return False
        except FileNotFoundError:
            print("ERROR: Node.js not found")
            return False
        
        # Check npm
        try:
            result = subprocess.run(['npm', '--version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✓ npm {result.stdout.strip()}")
            else:
                print("ERROR: npm not found")
                return False
        except FileNotFoundError:
            print("ERROR: npm not found")
            return False
        
        return True
    
    def setup_backend(self):
        """Setup and start backend"""
        print("\n--- Backend Setup ---")
        os.chdir(self.backend_dir)
        
        # Run backend startup script
        try:
            result = subprocess.run([sys.executable, 'start_backend.py'], 
                                  capture_output=True, text=True, timeout=60)
            if result.returncode != 0:
                print("Backend setup failed:")
                print(result.stderr)
                return False
            print("✓ Backend setup completed")
            return True
        except subprocess.TimeoutExpired:
            print("Backend setup timed out")
            return False
        except Exception as e:
            print(f"Backend setup error: {e}")
            return False
    
    def start_backend_server(self):
        """Start backend server in background"""
        print("Starting backend server...")
        os.chdir(self.backend_dir)
        
        try:
            process = subprocess.Popen([
                sys.executable, '-m', 'uvicorn', 
                'main:app', 
                '--host', '0.0.0.0', 
                '--port', '8000', 
                '--reload'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            self.processes.append(('backend', process))
            print("✓ Backend server started (PID: {})".format(process.pid))
            return True
        except Exception as e:
            print(f"Failed to start backend server: {e}")
            return False
    
    def wait_for_backend(self, timeout=30):
        """Wait for backend to be ready"""
        print("Waiting for backend to be ready...")
        import requests
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get('http://localhost:8000/health', timeout=5)
                if response.status_code == 200:
                    print("✓ Backend is ready")
                    return True
            except:
                pass
            time.sleep(2)
        
        print("Backend failed to start within timeout")
        return False
    
    def start_frontend_server(self):
        """Start frontend development server"""
        print("Starting frontend server...")
        os.chdir(self.project_root)
        
        try:
            process = subprocess.Popen([
                'npm', 'run', 'dev'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            self.processes.append(('frontend', process))
            print("✓ Frontend server started (PID: {})".format(process.pid))
            return True
        except Exception as e:
            print(f"Failed to start frontend server: {e}")
            return False
    
    def run_system_health_check(self):
        """Run comprehensive system health check"""
        print("\n--- System Health Check ---")
        os.chdir(self.backend_dir)
        
        try:
            result = subprocess.run([sys.executable, 'system_health_check.py'], 
                                  capture_output=True, text=True, timeout=120)
            
            print(result.stdout)
            if result.stderr:
                print("Warnings/Errors:")
                print(result.stderr)
            
            if result.returncode == 0:
                print("✓ System health check passed")
                return True
            else:
                print("✗ System health check failed")
                return False
        except subprocess.TimeoutExpired:
            print("Health check timed out")
            return False
        except Exception as e:
            print(f"Health check error: {e}")
            return False
    
    def monitor_processes(self):
        """Monitor running processes"""
        while self.running:
            for name, process in self.processes:
                if process.poll() is not None:
                    print(f"WARNING: {name} process stopped unexpectedly")
            time.sleep(5)
    
    def cleanup(self):
        """Clean up processes"""
        print("\nShutting down...")
        self.running = False
        
        for name, process in self.processes:
            if process.poll() is None:
                print(f"Stopping {name}...")
                process.terminate()
                try:
                    process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    process.kill()
        
        print("Cleanup completed")
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.cleanup()
        sys.exit(0)
    
    def run(self):
        """Main run method"""
        # Setup signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        self.print_banner()
        
        # Check prerequisites
        if not self.check_prerequisites():
            print("Prerequisites check failed. Please install required software.")
            return False
        
        # Setup backend (database, demo user, etc.)
        if not self.setup_backend():
            print("Backend setup failed")
            return False
        
        # Start backend server
        if not self.start_backend_server():
            print("Failed to start backend server")
            return False
        
        # Wait for backend to be ready
        if not self.wait_for_backend():
            print("Backend not ready")
            self.cleanup()
            return False
        
        # Run health check
        if not self.run_system_health_check():
            print("System health check failed")
            response = input("Continue anyway? (y/N): ").lower()
            if response != 'y':
                self.cleanup()
                return False
        
        # Start frontend server
        if not self.start_frontend_server():
            print("Failed to start frontend server")
            self.cleanup()
            return False
        
        print("\n" + "="*60)
        print("🚀 Ultimate Co-founder Platform is now running!")
        print("="*60)
        print("Frontend: http://localhost:5173")
        print("Backend:  http://localhost:8000")
        print("API Docs: http://localhost:8000/docs")
        print("\nDemo Login Credentials:")
        print("Email:    <EMAIL>")
        print("Password: demo123")
        print("\nPress Ctrl+C to stop all servers")
        print("="*60)
        
        # Start monitoring thread
        monitor_thread = threading.Thread(target=self.monitor_processes)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        # Keep main thread alive
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        
        self.cleanup()
        return True

def main():
    """Main function"""
    starter = UltimateCofounderStarter()
    success = starter.run()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
