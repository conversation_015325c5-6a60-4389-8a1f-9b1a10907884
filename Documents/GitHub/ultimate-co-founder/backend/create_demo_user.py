#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a demo user for testing the authentication system.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal, engine
from app.models.user import User
from app.api.routes.auth import get_password_hash
import uuid

def create_demo_user():
    """Create a demo user for testing purposes."""
    db = SessionLocal()
    
    try:
        # Check if demo user already exists and delete it
        existing_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if existing_user:
            print("Deleting existing demo user...")
            db.delete(existing_user)
            db.commit()
            print("Existing demo user deleted!")
        
        # Create demo user
        demo_user = User(
            id=str(uuid.uuid4()),
            email="<EMAIL>",
            name="Demo User",
            hashed_password=get_password_hash("demo123"),
            is_active=True
        )
        
        db.add(demo_user)
        db.commit()
        db.refresh(demo_user)
        
        print("Demo user created successfully!")
        print(f"Email: {demo_user.email}")
        print(f"Password: demo123")
        print(f"Name: {demo_user.name}")
        print(f"ID: {demo_user.id}")
        
    except Exception as e:
        print(f"Error creating demo user: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_demo_user()
