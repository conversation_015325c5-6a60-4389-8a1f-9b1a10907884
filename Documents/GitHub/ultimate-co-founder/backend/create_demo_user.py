#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced script to setup and validate the authentication system.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal, engine, Base
from app.models.user import User
from app.api.routes.auth import get_password_hash, authenticate_user, create_access_token
import uuid
from datetime import timed<PERSON><PERSON>

def setup_database():
    """Initialize database and create all tables."""
    try:
        print("Setting up database...")
        # Create all tables
        Base.metadata.create_all(bind=engine)
        print("Database tables created successfully!")
        return True
    except Exception as e:
        print(f"Database setup failed: {e}")
        return False

def create_demo_user():
    """Create a demo user for testing purposes."""
    db = SessionLocal()

    try:
        print("Setting up demo user...")

        # Check if demo user already exists and delete it
        existing_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if existing_user:
            print("Deleting existing demo user...")
            db.delete(existing_user)
            db.commit()
            print("Existing demo user deleted!")

        # Create demo user
        demo_user = User(
            id=str(uuid.uuid4()),
            email="<EMAIL>",
            name="Demo User",
            hashed_password=get_password_hash("demo123"),
            is_active=True
        )

        db.add(demo_user)
        db.commit()
        db.refresh(demo_user)

        print("Demo user created successfully!")
        print(f"Email: {demo_user.email}")
        print(f"Password: demo123")
        print(f"Name: {demo_user.name}")
        print(f"ID: {demo_user.id}")
        return True

    except Exception as e:
        print(f"Error creating demo user: {e}")
        db.rollback()
        return False
    finally:
        db.close()

def test_authentication():
    """Test the authentication system."""
    db = SessionLocal()

    try:
        print("Testing authentication system...")

        # Test user authentication
        user = authenticate_user(db, "<EMAIL>", "demo123")
        if not user:
            print("Authentication test failed!")
            return False

        print("User authentication successful!")

        # Test token creation
        access_token = create_access_token(
            data={"sub": user.id},
            expires_delta=timedelta(minutes=30)
        )

        if not access_token:
            print("Token creation failed!")
            return False

        print("Token creation successful!")
        print(f"Sample token: {access_token[:50]}...")

        return True

    except Exception as e:
        print(f"Authentication test failed: {e}")
        return False
    finally:
        db.close()

def validate_system():
    """Run complete system validation."""
    print("Starting Authentication System Setup & Validation")
    print("=" * 60)

    success_count = 0
    total_tests = 3

    # 1. Setup database
    if setup_database():
        success_count += 1

    # 2. Create demo user
    if create_demo_user():
        success_count += 1

    # 3. Test authentication
    if test_authentication():
        success_count += 1

    print("\n" + "=" * 60)
    print("AUTHENTICATION SYSTEM VALIDATION SUMMARY")
    print("=" * 60)

    print(f"Database Setup: {'PASS' if success_count >= 1 else 'FAIL'}")
    print(f"Demo User Creation: {'PASS' if success_count >= 2 else 'FAIL'}")
    print(f"Authentication Test: {'PASS' if success_count >= 3 else 'FAIL'}")

    print(f"\nResults: {success_count}/{total_tests} tests passed ({(success_count/total_tests)*100:.1f}%)")

    if success_count == total_tests:
        print("AUTHENTICATION SYSTEM READY!")
        print("You can now login with:")
        print("   Email: <EMAIL>")
        print("   Password: demo123")
        return True
    else:
        print("Authentication system setup incomplete!")
        return False

if __name__ == "__main__":
    success = validate_system()
    sys.exit(0 if success else 1)
