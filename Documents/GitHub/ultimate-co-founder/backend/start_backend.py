#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Backend Startup Script for Ultimate Co-founder <PERSON>
Handles dependency checking, environment setup, and server startup
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    print("Checking Python version...")
    if sys.version_info < (3, 8):
        print("ERROR: Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"Python version: {sys.version.split()[0]} - OK")
    return True

def check_virtual_environment():
    """Check if virtual environment is activated"""
    print("Checking virtual environment...")
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("Virtual environment: ACTIVE")
        return True
    else:
        print("WARNING: No virtual environment detected")
        print("It's recommended to use a virtual environment")
        response = input("Continue anyway? (y/N): ").lower()
        return response == 'y'

def check_dependencies():
    """Check if required dependencies are installed"""
    print("Checking dependencies...")
    required_packages = [
        'fastapi',
        'uvicorn',
        'sqlalchemy',
        'crewai',
        'langchain',
        'httpx',
        'python-jose',
        'passlib',
        'python-multipart'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"  {package}: OK")
        except ImportError:
            print(f"  {package}: MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("Installing missing packages...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_packages)
            print("Dependencies installed successfully!")
            return True
        except subprocess.CalledProcessError:
            print("ERROR: Failed to install dependencies")
            print("Please run: pip install -r requirements.txt")
            return False
    
    print("All dependencies: OK")
    return True

def check_environment_file():
    """Check if .env file exists and has required variables"""
    print("Checking environment configuration...")
    env_file = Path('.env')
    
    if not env_file.exists():
        print("WARNING: .env file not found")
        print("Creating basic .env file...")
        create_basic_env_file()
        return True
    
    # Check for critical environment variables
    required_vars = ['SECRET_KEY', 'DATABASE_URL']
    missing_vars = []
    
    with open(env_file, 'r') as f:
        env_content = f.read()
        for var in required_vars:
            if f"{var}=" not in env_content:
                missing_vars.append(var)
    
    if missing_vars:
        print(f"WARNING: Missing environment variables: {', '.join(missing_vars)}")
        print("Please check your .env file")
    
    print("Environment file: OK")
    return True

def create_basic_env_file():
    """Create a basic .env file with default values"""
    env_content = """# Database Configuration
DATABASE_URL=sqlite:///./ultimate_cofounder.db
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your_super_secret_key_change_in_production_min_32_chars_for_security
JWT_SECRET_KEY=your_jwt_secret_key_for_token_signing_change_in_production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# AI Services (Priority: Groq > OpenAI > Mock Mode)
GROQ_API_KEY=
OPENAI_API_KEY=
OPENAI_MODEL=gpt-4-turbo-preview

# Video/Voice Services
LIVEKIT_API_KEY=
LIVEKIT_API_SECRET=
LIVEKIT_URL=

# Video Creation Services
TOPVIEW_API_KEY=
TOPVIEW_UID=

# CrewAI Configuration
CREWAI_API_KEY=

# Third-party Integrations
COMPOSIO_API_KEY=
BOLT_API_KEY=

# Environment
ENVIRONMENT=development
DEBUG=True
FRONTEND_URL=http://localhost:5173
CORS_ORIGINS=["http://localhost:3000", "http://localhost:5173", "http://localhost:5174"]

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=Ultimate Startup Co-founder
"""
    
    with open('.env', 'w') as f:
        f.write(env_content)
    print("Basic .env file created!")

def setup_database():
    """Setup database and create demo user"""
    print("Setting up database and demo user...")
    try:
        # Run the demo user creation script
        result = subprocess.run([sys.executable, 'create_demo_user.py'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("Database setup: OK")
            return True
        else:
            print(f"Database setup failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"Database setup error: {e}")
        return False

def start_server():
    """Start the FastAPI server"""
    print("Starting Ultimate Co-founder backend server...")
    print("Server will be available at: http://localhost:8000")
    print("API documentation: http://localhost:8000/docs")
    print("Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        # Start the server using uvicorn
        subprocess.run([
            sys.executable, '-m', 'uvicorn', 
            'main:app', 
            '--host', '0.0.0.0', 
            '--port', '8000', 
            '--reload'
        ])
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Server error: {e}")

def main():
    """Main startup function"""
    print("Ultimate Co-founder Backend Startup")
    print("=" * 50)
    
    # Change to backend directory if not already there
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # Run all checks
    checks = [
        ("Python Version", check_python_version),
        ("Virtual Environment", check_virtual_environment),
        ("Dependencies", check_dependencies),
        ("Environment File", check_environment_file),
        ("Database Setup", setup_database),
    ]
    
    for check_name, check_func in checks:
        print(f"\n--- {check_name} ---")
        if not check_func():
            print(f"FAILED: {check_name}")
            print("Please fix the above issues before starting the server")
            sys.exit(1)
    
    print("\n" + "=" * 50)
    print("All checks passed! Starting server...")
    print("=" * 50)
    
    # Start the server
    start_server()

if __name__ == "__main__":
    main()
