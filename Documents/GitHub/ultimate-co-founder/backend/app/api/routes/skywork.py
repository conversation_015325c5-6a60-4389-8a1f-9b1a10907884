from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
import uuid
from datetime import datetime

router = APIRouter()

class GenerateDocumentRequest(BaseModel):
    type: str  # document, slide, sheet, podcast
    title: str
    prompt: str
    template: Optional[str] = "standard"
    additionalContext: Optional[str] = None

class SkyworkDocument(BaseModel):
    id: str
    type: str
    title: str
    content: str
    createdAt: str
    updatedAt: str
    status: str
    downloadUrl: Optional[str] = None
    previewUrl: Optional[str] = None

@router.post("/generate")
async def generate_document(request: GenerateDocumentRequest):
    """Generate a document, slide, sheet, or podcast using SkyworkAI"""
    try:
        # In a real implementation, this would call the SkyworkAI API
        # For now, we'll return a mock response
        document_id = f"{request.type}_{uuid.uuid4().hex[:8]}"
        now = datetime.now().isoformat()
        
        file_extension = {
            "document": "docx",
            "slide": "pptx",
            "sheet": "xlsx",
            "podcast": "mp3"
        }.get(request.type, "txt")
        
        filename = request.title.lower().replace(" ", "-")
        
        return SkyworkDocument(
            id=document_id,
            type=request.type,
            title=request.title,
            content=f"Generated content based on prompt: {request.prompt}",
            createdAt=now,
            updatedAt=now,
            status="generated",
            downloadUrl=f"https://github.com/SkyworkAI/{filename}.{file_extension}",
            previewUrl=f"https://github.com/SkyworkAI/{filename}-preview.html"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating document: {str(e)}")

@router.get("/documents")
async def list_documents():
    """List all documents generated with SkyworkAI"""
    try:
        # In a real implementation, this would fetch from a database
        # For now, we'll return mock data
        now = datetime.now().isoformat()
        yesterday = datetime.now().replace(day=datetime.now().day-1).isoformat()
        
        documents = [
            SkyworkDocument(
                id=f"doc_{uuid.uuid4().hex[:8]}",
                type="document",
                title="Business Plan",
                content="Comprehensive business plan for startup.",
                createdAt=yesterday,
                updatedAt=yesterday,
                status="generated",
                downloadUrl="https://github.com/SkyworkAI/business-plan.docx",
                previewUrl="https://github.com/SkyworkAI/business-plan-preview.html"
            ),
            SkyworkDocument(
                id=f"slide_{uuid.uuid4().hex[:8]}",
                type="slide",
                title="Investor Pitch Deck",
                content="Pitch deck for investor presentation.",
                createdAt=yesterday,
                updatedAt=yesterday,
                status="generated",
                downloadUrl="https://github.com/SkyworkAI/investor-pitch.pptx",
                previewUrl="https://github.com/SkyworkAI/investor-pitch-preview.html"
            ),
            SkyworkDocument(
                id=f"sheet_{uuid.uuid4().hex[:8]}",
                type="sheet",
                title="Financial Projections",
                content="Three-year financial projections.",
                createdAt=now,
                updatedAt=now,
                status="generated",
                downloadUrl="https://github.com/SkyworkAI/financial-projections.xlsx",
                previewUrl="https://github.com/SkyworkAI/financial-projections-preview.html"
            ),
            SkyworkDocument(
                id=f"podcast_{uuid.uuid4().hex[:8]}",
                type="podcast",
                title="Startup Journey Episode 1",
                content="First episode of startup journey podcast.",
                createdAt=now,
                updatedAt=now,
                status="generated",
                downloadUrl="https://github.com/SkyworkAI/startup-journey-ep1.mp3",
                previewUrl="https://github.com/SkyworkAI/startup-journey-ep1-preview.html"
            )
        ]
        
        return {"documents": documents}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing documents: {str(e)}")

@router.get("/documents/{document_id}")
async def get_document(document_id: str):
    """Get a specific document by ID"""
    try:
        # In a real implementation, this would fetch from a database
        # For now, we'll return a mock document
        doc_type = document_id.split("_")[0] if "_" in document_id else "document"
        
        file_extension = {
            "document": "docx",
            "doc": "docx",
            "slide": "pptx",
            "sheet": "xlsx",
            "podcast": "mp3"
        }.get(doc_type, "txt")
        
        return SkyworkDocument(
            id=document_id,
            type=doc_type,
            title=f"Sample {doc_type.capitalize()}",
            content=f"This is a sample {doc_type} generated by SkyworkAI.",
            createdAt=datetime.now().isoformat(),
            updatedAt=datetime.now().isoformat(),
            status="generated",
            downloadUrl=f"https://github.com/SkyworkAI/sample-{doc_type}.{file_extension}",
            previewUrl=f"https://github.com/SkyworkAI/sample-{doc_type}-preview.html"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting document: {str(e)}")