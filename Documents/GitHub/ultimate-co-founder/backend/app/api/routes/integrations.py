from fastapi import APIRouter, HTTPException
from typing import Dict, Any
from pydantic import BaseModel

from app.services.composio_service import composio_service

router = APIRouter()

class ConnectIntegrationRequest(BaseModel):
    config: Dict[str, Any] = {}

class ExecuteActionRequest(BaseModel):
    action: str
    parameters: Dict[str, Any] = {}

@router.get("/")
async def get_integrations():
    """Get all available integrations"""
    try:
        integrations = await composio_service.get_integrations()
        return {"integrations": integrations}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting integrations: {str(e)}")

@router.post("/{integration_id}/connect")
async def connect_integration(integration_id: str, request: ConnectIntegrationRequest):
    """Connect an integration"""
    try:
        integration = await composio_service.connect_integration(integration_id, request.config)
        return {"message": f"Integration {integration_id} connected", "integration": integration}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error connecting integration: {str(e)}")

@router.post("/{integration_id}/disconnect")
async def disconnect_integration(integration_id: str):
    """Disconnect an integration"""
    try:
        await composio_service.disconnect_integration(integration_id)
        return {"message": f"Integration {integration_id} disconnected"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error disconnecting integration: {str(e)}")

@router.post("/{integration_id}/execute")
async def execute_action(integration_id: str, request: ExecuteActionRequest):
    """Execute an action on an integration"""
    try:
        result = await composio_service.execute_action(
            integration_id=integration_id,
            action=request.action,
            parameters=request.parameters
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error executing action: {str(e)}")

@router.post("/{integration_id}/sync")
async def sync_integration(integration_id: str):
    """Sync an integration"""
    try:
        result = await composio_service.sync_integration(integration_id)
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error syncing integration: {str(e)}")

@router.get("/{integration_id}")
async def get_integration(integration_id: str):
    """Get specific integration details"""
    integrations = await composio_service.get_integrations()
    integration = next((i for i in integrations if i["id"] == integration_id), None)
    
    if not integration:
        raise HTTPException(status_code=404, detail=f"Integration {integration_id} not found")
    
    return integration