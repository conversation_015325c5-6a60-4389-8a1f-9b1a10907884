#!/usr/bin/env python3
"""
TopView.ai Integration Test Suite
Tests all TopView.ai API endpoints and functionality
"""

import asyncio
import json
import sys
import time
from typing import Dict, Any
import httpx
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:8000"
TEST_USER = {
    "username": "<EMAIL>",
    "password": "demo123"
}

class TopViewIntegrationTester:
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=30.0)
        self.auth_token = None
        self.test_results = []
        
    async def authenticate(self) -> bool:
        """Authenticate and get JWT token"""
        try:
            response = await self.client.post(
                f"{BASE_URL}/api/v1/auth/login",
                data=TEST_USER,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            
            if response.status_code == 200:
                data = response.json()
                self.auth_token = data.get("access_token")
                print(f"✅ Authentication successful")
                return True
            else:
                print(f"❌ Authentication failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False
    
    def get_auth_headers(self) -> Dict[str, str]:
        """Get authorization headers"""
        return {
            "Authorization": f"Bearer {self.auth_token}",
            "Content-Type": "application/json"
        }
    
    async def test_topview_health(self) -> bool:
        """Test TopView.ai service health"""
        try:
            response = await self.client.get(
                f"{BASE_URL}/api/v1/topview/health",
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ TopView.ai Health Check: {data.get('status', 'Unknown')}")
                print(f"   Mock Mode: {data.get('mock_mode', 'Unknown')}")
                return True
            else:
                print(f"❌ Health check failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Health check error: {e}")
            return False
    
    async def test_url_to_video(self) -> bool:
        """Test URL to Video conversion"""
        try:
            test_data = {
                "url": "https://example.com",
                "duration": 30,
                "aspectRatio": "16:9",
                "voiceId": "default",
                "avatarId": "avatar_001",
                "title": "Test Video",
                "description": "Test video description"
            }
            
            response = await self.client.post(
                f"{BASE_URL}/api/v1/topview/url-to-video",
                json=test_data,
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ URL to Video: Task ID {data.get('task_id', 'Unknown')}")
                return True
            else:
                print(f"❌ URL to Video failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ URL to Video error: {e}")
            return False
    
    async def test_video_avatar(self) -> bool:
        """Test Video Avatar creation"""
        try:
            test_data = {
                "script": "Welcome to our amazing product demonstration!",
                "avatarId": "avatar_002",
                "voiceId": "default",
                "backgroundType": "solid",
                "backgroundColor": "#ffffff",
                "duration": 30
            }
            
            response = await self.client.post(
                f"{BASE_URL}/api/v1/topview/video-avatar",
                json=test_data,
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Video Avatar: Task ID {data.get('task_id', 'Unknown')}")
                return True
            else:
                print(f"❌ Video Avatar failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Video Avatar error: {e}")
            return False
    
    async def test_product_avatar(self) -> bool:
        """Test Product Avatar creation"""
        try:
            test_data = {
                "productImageUrl": "https://example.com/product.jpg",
                "avatarCategory": "general",
                "ethnicity": "mixed",
                "gender": "mixed",
                "script": "This amazing product will revolutionize your workflow!"
            }
            
            response = await self.client.post(
                f"{BASE_URL}/api/v1/topview/product-avatar",
                json=test_data,
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Product Avatar: Task ID {data.get('task_id', 'Unknown')}")
                return True
            else:
                print(f"❌ Product Avatar failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Product Avatar error: {e}")
            return False
    
    async def test_materials_to_video(self) -> bool:
        """Test Materials to Video conversion"""
        try:
            test_data = {
                "materials": ["https://example.com/image1.jpg", "https://example.com/image2.jpg"],
                "script": "Creating amazing videos from your materials",
                "videoType": "marketing",
                "duration": 30,
                "voiceId": "default",
                "title": "Test Materials Video"
            }
            
            response = await self.client.post(
                f"{BASE_URL}/api/v1/topview/materials-to-video",
                json=test_data,
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Materials to Video: Task ID {data.get('task_id', 'Unknown')}")
                return True
            else:
                print(f"❌ Materials to Video failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Materials to Video error: {e}")
            return False
    
    async def test_image_to_video(self) -> bool:
        """Test Image to Video conversion"""
        try:
            test_data = {
                "imageUrl": "https://example.com/static-image.jpg",
                "duration": 5,
                "motionStrength": "medium"
            }
            
            response = await self.client.post(
                f"{BASE_URL}/api/v1/topview/image-to-video",
                json=test_data,
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Image to Video: Task ID {data.get('task_id', 'Unknown')}")
                return True
            else:
                print(f"❌ Image to Video failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Image to Video error: {e}")
            return False
    
    async def test_task_status(self) -> bool:
        """Test task status checking"""
        try:
            # Use a mock task ID for testing
            test_task_id = "test_task_12345"
            
            response = await self.client.get(
                f"{BASE_URL}/api/v1/topview/task/{test_task_id}",
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Task Status: {data.get('status', 'Unknown')}")
                return True
            else:
                print(f"❌ Task Status failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Task Status error: {e}")
            return False
    
    async def run_all_tests(self):
        """Run all TopView.ai integration tests"""
        print("🚀 Starting TopView.ai Integration Test Suite")
        print("=" * 60)
        
        # Authentication
        if not await self.authenticate():
            print("❌ Cannot proceed without authentication")
            return False
        
        # Test suite
        tests = [
            ("Health Check", self.test_topview_health),
            ("URL to Video", self.test_url_to_video),
            ("Video Avatar", self.test_video_avatar),
            ("Product Avatar", self.test_product_avatar),
            ("Materials to Video", self.test_materials_to_video),
            ("Image to Video", self.test_image_to_video),
            ("Task Status", self.test_task_status),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🧪 Testing: {test_name}")
            try:
                if await test_func():
                    passed += 1
                    self.test_results.append({"test": test_name, "status": "PASS"})
                else:
                    self.test_results.append({"test": test_name, "status": "FAIL"})
            except Exception as e:
                print(f"❌ {test_name} crashed: {e}")
                self.test_results.append({"test": test_name, "status": "ERROR", "error": str(e)})
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        for result in self.test_results:
            status_icon = "✅" if result["status"] == "PASS" else "❌"
            print(f"{status_icon} {result['test']}: {result['status']}")
            if "error" in result:
                print(f"   Error: {result['error']}")
        
        print(f"\n📈 Results: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
        
        if passed == total:
            print("🎉 All TopView.ai integration tests PASSED!")
            return True
        else:
            print("⚠️  Some TopView.ai integration tests FAILED!")
            return False
    
    async def close(self):
        """Clean up resources"""
        await self.client.aclose()

async def main():
    """Main test runner"""
    tester = TopViewIntegrationTester()
    try:
        success = await tester.run_all_tests()
        return 0 if success else 1
    finally:
        await tester.close()

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
