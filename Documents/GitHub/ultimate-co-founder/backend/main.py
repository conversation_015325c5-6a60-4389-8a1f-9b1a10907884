from fastapi import FastAP<PERSON>, HTTPException, Depends, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from contextlib import asynccontextmanager
import uvicorn
import os
from dotenv import load_dotenv

from app.core.config import settings
from app.api.routes import agents, orchestrator, livekit, integrations, auth, bolt, health, skywork, topview
from app.core.database import init_db, SessionLocal
from app.services.websocket_manager import WebSocketManager
from app.models.user import User
from app.api.routes.auth import get_password_hash
import uuid

load_dotenv()

# WebSocket manager for real-time communication
websocket_manager = WebSocketManager()

def create_demo_user_if_not_exists():
    """Create demo user if it doesn't exist"""
    db = SessionLocal()
    try:
        # Check if demo user exists
        existing_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if not existing_user:
            # Create demo user
            demo_user = User(
                id=str(uuid.uuid4()),
                email="<EMAIL>",
                name="Demo User",
                hashed_password=get_password_hash("demo123"),
                is_active=True
            )
            db.add(demo_user)
            db.commit()
            print("✅ Demo user created successfully!")
        else:
            print("✅ Demo user already exists")
    except Exception as e:
        print(f"❌ Error creating demo user: {e}")
        db.rollback()
    finally:
        db.close()

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    await init_db()
    create_demo_user_if_not_exists()
    print("🚀 Ultimate Co-founder Backend Started")
    yield
    # Shutdown
    print("👋 Ultimate Co-founder Backend Shutting Down")

app = FastAPI(
    title="Ultimate Startup Co-founder API",
    description="AI-powered startup co-founder platform with CrewAI, LangChain, LiveKit, and Composio integrations",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[settings.FRONTEND_URL, "http://localhost:3000", "http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(agents.router, prefix="/api/v1/agents", tags=["agents"])
app.include_router(orchestrator.router, prefix="/api/v1/orchestrator", tags=["orchestrator"])
app.include_router(livekit.router, prefix="/api/v1/livekit", tags=["livekit"])
app.include_router(integrations.router, prefix="/api/v1/integrations", tags=["integrations"])
app.include_router(auth.router, prefix="/api/v1/auth", tags=["auth"])
app.include_router(bolt.router, prefix="/api/v1/bolt", tags=["bolt"])
app.include_router(health.router, prefix="/api/v1/health", tags=["health"])
app.include_router(skywork.router, prefix="/api/v1/skywork", tags=["skywork"])
app.include_router(topview.router, prefix="/api/v1/topview", tags=["topview"])

@app.get("/")
async def root():
    return {
        "message": "Ultimate Startup Co-founder API",
        "version": "1.0.0",
        "status": "running",
        "agents": ["strategic", "product", "technical", "operations", "marketing"]
    }

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": "2025-06-25T13:30:00Z"}

@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    await websocket_manager.connect(websocket, client_id)
    try:
        while True:
            data = await websocket.receive_text()
            await websocket_manager.send_personal_message(f"Echo: {data}", client_id)
    except WebSocketDisconnect:
        websocket_manager.disconnect(client_id)

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )