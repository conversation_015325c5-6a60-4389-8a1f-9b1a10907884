import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current directory.
  // Set the third parameter to '' to load all env regardless of the `VITE_` prefix.
  const env = loadEnv(mode, process.cwd(), '');
  
  // Ensure we have a valid API URL, handling null, undefined, empty string, or literal 'null'
  const getValidApiUrl = () => {
    const apiUrl = env.VITE_API_URL;
    // Check for invalid values and return a proper default URL
    if (!apiUrl || 
        apiUrl === 'null' || 
        apiUrl === 'undefined' || 
        apiUrl.trim() === '') {
      return 'http://127.0.0.1:8000';
    }
    return apiUrl;
  };
  
  const targetUrl = getValidApiUrl();
  console.log(`Using API URL: ${targetUrl}`);
  
  return {
    plugins: [react()],
    optimizeDeps: {
      exclude: ['lucide-react'],
      include: ['prop-types'],
    },
    server: {
      proxy: {
        '/api': {
          target: targetUrl.replace('localhost', '127.0.0.1'),
          changeOrigin: true,
          secure: false,
        },
      },
      host: true,
    },
    build: {
      sourcemap: true,
      rollupOptions: {
        output: {
          manualChunks: {
            react: ['react', 'react-dom'],
            router: ['react-router-dom'],
            ui: ['framer-motion', 'lucide-react', 'react-hot-toast'],
            livekit: ['livekit-client', '@livekit/components-react', '@livekit/components-core'],
          }
        }
      }
    }
  };
});