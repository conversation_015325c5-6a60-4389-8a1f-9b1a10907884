#!/usr/bin/env python3
"""
Comprehensive LiveKit test to identify the exact issue with live sessions.
This script tests the complete flow from authentication to session creation.
"""

import requests
import json
import time
from datetime import datetime

# Configuration
FRONTEND_URL = "http://localhost:5173"
BACKEND_URL = "http://localhost:8000"

def test_authentication():
    """Test authentication flow"""
    print("🔐 Testing Authentication Flow")
    print("=" * 50)
    
    try:
        # Test login
        login_data = {
            "username": "<EMAIL>",
            "password": "demo123"
        }
        
        print(f"📡 Attempting login with: {login_data['username']}")
        
        response = requests.post(
            f"{BACKEND_URL}/api/v1/auth/login",
            data=login_data,  # OAuth2PasswordRequestForm expects form data
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if response.status_code == 200:
            auth_data = response.json()
            print(f"✅ Login successful!")
            print(f"   Token type: {auth_data.get('token_type')}")
            print(f"   Access token: {auth_data.get('access_token')[:20]}...")
            return auth_data.get('access_token')
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Authentication test failed: {e}")
        return None

def test_authenticated_session_creation(token):
    """Test session creation with authentication"""
    print("\n🎥 Testing Authenticated Session Creation")
    print("=" * 50)
    
    if not token:
        print("❌ No authentication token available")
        return False
    
    try:
        session_data = {
            "agent_ids": ["strategic", "product"],
            "session_type": "video"
        }
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}"
        }
        
        print(f"📡 Creating authenticated session...")
        print(f"   Agents: {session_data['agent_ids']}")
        print(f"   Type: {session_data['session_type']}")
        
        response = requests.post(
            f"{BACKEND_URL}/api/v1/livekit/sessions",
            json=session_data,
            headers=headers
        )
        
        if response.status_code == 200:
            session = response.json()
            print(f"✅ Authenticated session created!")
            print(f"   Session ID: {session['id']}")
            print(f"   Room Name: {session['room_name']}")
            print(f"   Token: {session['token'][:20]}...")
            print(f"   URL: {session['url']}")
            print(f"   Participants: {len(session['participants'])}")
            return session
        else:
            print(f"❌ Authenticated session creation failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Authenticated session test failed: {e}")
        return None

def test_frontend_proxy():
    """Test frontend proxy configuration"""
    print("\n🌐 Testing Frontend Proxy Configuration")
    print("=" * 50)
    
    try:
        # Test if frontend can proxy to backend
        response = requests.get(f"{FRONTEND_URL}/api/v1/livekit/")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Frontend proxy working!")
            print(f"   Status: {data['status']}")
            print(f"   Active sessions: {data['active_sessions']}")
            return True
        else:
            print(f"❌ Frontend proxy failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Frontend proxy test failed: {e}")
        return False

def test_cors_configuration():
    """Test CORS configuration"""
    print("\n🔗 Testing CORS Configuration")
    print("=" * 50)
    
    try:
        # Test preflight request
        headers = {
            "Origin": FRONTEND_URL,
            "Access-Control-Request-Method": "POST",
            "Access-Control-Request-Headers": "Content-Type,Authorization"
        }
        
        response = requests.options(
            f"{BACKEND_URL}/api/v1/livekit/sessions",
            headers=headers
        )
        
        print(f"📡 CORS preflight response: {response.status_code}")
        
        cors_headers = {
            "Access-Control-Allow-Origin": response.headers.get("Access-Control-Allow-Origin"),
            "Access-Control-Allow-Methods": response.headers.get("Access-Control-Allow-Methods"),
            "Access-Control-Allow-Headers": response.headers.get("Access-Control-Allow-Headers"),
        }
        
        print(f"   CORS headers: {cors_headers}")
        
        if response.status_code in [200, 204]:
            print(f"✅ CORS configuration looks good!")
            return True
        else:
            print(f"❌ CORS configuration issue")
            return False
            
    except Exception as e:
        print(f"❌ CORS test failed: {e}")
        return False

def test_livekit_service_health():
    """Test LiveKit service health"""
    print("\n🏥 Testing LiveKit Service Health")
    print("=" * 50)
    
    try:
        response = requests.get(f"{BACKEND_URL}/api/v1/livekit/health")
        
        if response.status_code == 200:
            health = response.json()
            print(f"✅ LiveKit service healthy!")
            print(f"   Health data: {health}")
            return True
        else:
            print(f"❌ LiveKit service health check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ LiveKit health test failed: {e}")
        return False

def simulate_frontend_flow(token):
    """Simulate the exact frontend flow"""
    print("\n🎬 Simulating Frontend Flow")
    print("=" * 50)
    
    if not token:
        print("❌ No authentication token for frontend simulation")
        return False
    
    try:
        # Step 1: Create session (like frontend does)
        print("📡 Step 1: Creating session via frontend proxy...")
        
        session_data = {
            "agent_ids": ["strategic", "product", "technical"],
            "session_type": "video"
        }
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}",
            "Origin": FRONTEND_URL
        }
        
        # Use frontend URL to test proxy
        response = requests.post(
            f"{FRONTEND_URL}/api/v1/livekit/sessions",
            json=session_data,
            headers=headers
        )
        
        if response.status_code == 200:
            session = response.json()
            print(f"✅ Frontend session creation successful!")
            print(f"   Session ID: {session['id']}")
            
            # Step 2: Test session retrieval
            print("📡 Step 2: Retrieving session...")
            get_response = requests.get(
                f"{FRONTEND_URL}/api/v1/livekit/sessions/{session['id']}",
                headers=headers
            )
            
            if get_response.status_code == 200:
                print(f"✅ Session retrieval successful!")
                return True
            else:
                print(f"❌ Session retrieval failed: {get_response.status_code}")
                return False
        else:
            print(f"❌ Frontend session creation failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Frontend flow simulation failed: {e}")
        return False

def main():
    """Run comprehensive LiveKit diagnostics"""
    print("🚀 Ultimate Co-founder LiveKit Diagnostic Suite")
    print("=" * 70)
    print(f"⏰ Diagnostic started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test sequence
    tests = []
    
    # 1. Authentication
    token = test_authentication()
    tests.append(("Authentication", token is not None))
    
    # 2. Frontend proxy
    proxy_result = test_frontend_proxy()
    tests.append(("Frontend Proxy", proxy_result))
    
    # 3. CORS configuration
    cors_result = test_cors_configuration()
    tests.append(("CORS Configuration", cors_result))
    
    # 4. LiveKit service health
    health_result = test_livekit_service_health()
    tests.append(("LiveKit Service Health", health_result))
    
    # 5. Authenticated session creation
    if token:
        auth_session = test_authenticated_session_creation(token)
        tests.append(("Authenticated Session Creation", auth_session is not None))
        
        # 6. Frontend flow simulation
        frontend_result = simulate_frontend_flow(token)
        tests.append(("Frontend Flow Simulation", frontend_result))
    else:
        tests.append(("Authenticated Session Creation", False))
        tests.append(("Frontend Flow Simulation", False))
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 DIAGNOSTIC SUMMARY")
    print("=" * 70)
    
    passed = sum(1 for _, result in tests if result)
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
    
    print(f"\nTotal Tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 ALL DIAGNOSTICS PASSED - LiveKit should work correctly!")
        print("💡 If users still report errors, the issue might be:")
        print("   - Browser-specific (try different browsers)")
        print("   - Network/firewall related")
        print("   - WebRTC permissions")
        print("   - Ad blockers or browser extensions")
    else:
        print(f"\n⚠️ {total - passed} diagnostic(s) failed")
        print("🔧 Check the failed tests above to identify the issue")
        
        if not token:
            print("\n🔑 AUTHENTICATION ISSUE DETECTED:")
            print("   - Check if demo user exists in database")
            print("   - Verify login credentials")
            print("   - Check JWT configuration")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
