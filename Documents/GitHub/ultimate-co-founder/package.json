{"name": "ultimate-startup-cofounder", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@livekit/components-react": "^2.0.0", "@livekit/components-core": "^0.11.0", "@livekit/components-styles": "^1.0.12", "livekit-client": "^2.5.7", "framer-motion": "^11.0.0", "zustand": "^4.4.0", "socket.io-client": "^4.7.0", "axios": "^1.6.0", "react-hot-toast": "^2.4.0", "react-syntax-highlighter": "^15.5.0", "@types/react-syntax-highlighter": "^15.5.0", "langchain": "^0.1.0", "@langchain/core": "^0.1.0", "@langchain/openai": "^0.1.1", "openai": "^4.47.1", "ws": "^8.18.0", "uuid": "^9.0.0", "react-router-dom": "^6.22.3", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-csv": "^2.2.2", "@types/react-csv": "^1.1.10", "prop-types": "^15.8.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/ws": "^8.5.0", "@types/uuid": "^9.0.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}