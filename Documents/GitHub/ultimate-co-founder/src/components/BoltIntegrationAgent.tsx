import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Zap, 
  Code2, 
  Download, 
  ExternalLink,
  Play,
  Pause,
  Monitor,
  Share2,
  FileCode,
  CheckCircle,
  AlertCircle,
  Clock,
  Upload,
  Video,
  Settings,
  Globe,
  Key,
  Loader2
} from 'lucide-react';

interface BoltSession {
  sessionId: string;
  liveKitUrl: string;
  isScreenSharing: boolean;
  isRecording: boolean;
  duration: number;
  status: 'idle' | 'connecting' | 'active' | 'processing' | 'complete' | 'error';
}

interface BoltProject {
  projectId: string;
  name: string;
  status: 'creating' | 'building' | 'ready' | 'error';
  downloadUrl?: string;
  previewUrl?: string;
  files: Array<{
    path: string;
    content: string;
    size: number;
  }>;
}

interface ScaffoldData {
  name: string;
  description: string;
  techStack: string;
  files: Array<{
    path: string;
    content: string;
  }>;
  dependencies: string[];
  scripts: Record<string, string>;
}

export const BoltIntegrationAgent: React.FC = () => {
  const [currentMode, setCurrentMode] = useState<'screen-share' | 'api'>('screen-share');
  const [currentStep, setCurrentStep] = useState<'intro' | 'session' | 'processing' | 'complete'>('intro');
  const [boltSession, setBoltSession] = useState<BoltSession>({
    sessionId: '',
    liveKitUrl: '',
    isScreenSharing: false,
    isRecording: false,
    duration: 0,
    status: 'idle'
  });
  const [boltProject, setBoltProject] = useState<BoltProject | null>(null);
  const [scaffoldData, setScaffoldData] = useState<ScaffoldData | null>(null);
  const [apiKey, setApiKey] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);

  // Mock scaffold data from Technical Agent
  const mockScaffoldData: ScaffoldData = {
    name: 'ultimate-restaurant-manager',
    description: 'AI-powered restaurant management platform with reservation system and digital menu',
    techStack: 'React + Node.js + PostgreSQL',
    files: [
      {
        path: 'package.json',
        content: `{
  "name": "ultimate-restaurant-manager",
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview"
  },
  "dependencies": {
    "react": "^18.3.1",
    "react-dom": "^18.3.1",
    "typescript": "^5.5.3",
    "vite": "^5.4.2",
    "@vitejs/plugin-react": "^4.3.1",
    "tailwindcss": "^3.4.1",
    "lucide-react": "^0.344.0",
    "framer-motion": "^11.0.0"
  }
}`
      },
      {
        path: 'src/App.tsx',
        content: `import React from 'react';
import { Dashboard } from './components/Dashboard';
import { ReservationSystem } from './components/ReservationSystem';
import { MenuManager } from './components/MenuManager';

function App() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Dashboard />
      <ReservationSystem />
      <MenuManager />
    </div>
  );
}

export default App;`
      },
      {
        path: 'src/components/Dashboard.tsx',
        content: `import React from 'react';
import { BarChart3, Users, Calendar, DollarSign } from 'lucide-react';

export const Dashboard: React.FC = () => {
  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">Restaurant Dashboard</h1>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Today's Revenue</p>
              <p className="text-2xl font-bold text-gray-900">$2,847</p>
            </div>
            <DollarSign className="w-8 h-8 text-green-500" />
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Reservations</p>
              <p className="text-2xl font-bold text-gray-900">47</p>
            </div>
            <Calendar className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Tables</p>
              <p className="text-2xl font-bold text-gray-900">23/30</p>
            </div>
            <Users className="w-8 h-8 text-purple-500" />
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg. Rating</p>
              <p className="text-2xl font-bold text-gray-900">4.8</p>
            </div>
            <BarChart3 className="w-8 h-8 text-yellow-500" />
          </div>
        </div>
      </div>
    </div>
  );
};`
      },
      {
        path: 'README.md',
        content: `# Ultimate Restaurant Manager

AI-powered restaurant management platform with intelligent reservation system and dynamic digital menu management.

## Features

- **Smart Reservation System**: AI-powered table management with real-time availability
- **Digital Menu Management**: Dynamic menu updates with ingredient tracking
- **Analytics Dashboard**: Real-time insights on operations and performance
- **Staff Scheduling**: Automated scheduling with availability management
- **Customer Feedback Hub**: Integrated review management and response system

## Tech Stack

- **Frontend**: React 18 + TypeScript + Tailwind CSS
- **Backend**: Node.js + Express + PostgreSQL
- **AI Integration**: CrewAI + LangChain
- **Real-time**: LiveKit for video/voice features
- **Integrations**: Composio for third-party connections

## Quick Start

\`\`\`bash
npm install
npm run dev
\`\`\`

## Architecture

\`\`\`
Client (React) → API Gateway → Microservices → Database (PostgreSQL)
                    ↓
            LiveKit (Video/Voice) → AI Agents (CrewAI)
                    ↓
            Composio (Integrations) → External APIs
\`\`\`

Generated by Technical Co-founder Agent | Ultimate Startup Co-founder Platform`
      }
    ],
    dependencies: ['react', 'typescript', 'tailwindcss', 'lucide-react', 'framer-motion'],
    scripts: {
      'dev': 'vite',
      'build': 'vite build',
      'preview': 'vite preview'
    }
  };

  useEffect(() => {
    setScaffoldData(mockScaffoldData);
  }, []);

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const startScreenShareSession = async () => {
    setCurrentStep('session');
    setBoltSession({
      sessionId: `bolt-session-${Date.now()}`,
      liveKitUrl: 'wss://bolt-integration.livekit.cloud',
      isScreenSharing: true,
      isRecording: true,
      duration: 0,
      status: 'connecting'
    });

    addLog('Initializing LiveKit screen share session...');
    
    // Simulate connection process
    setTimeout(() => {
      setBoltSession(prev => ({ ...prev, status: 'active' }));
      addLog('Connected to LiveKit session');
      addLog('Starting screen share to bolt.new');
      startScreenShareWorkflow();
    }, 2000);

    // Start duration timer
    const timer = setInterval(() => {
      setBoltSession(prev => ({
        ...prev,
        duration: prev.duration + 1
      }));
    }, 1000);

    // Auto-complete after demo
    setTimeout(() => {
      clearInterval(timer);
      completeScreenShareWorkflow();
    }, 30000); // 30 seconds demo
  };

  const startScreenShareWorkflow = async () => {
    const steps = [
      'Navigating to bolt.new...',
      'Creating new project...',
      'Pasting scaffold code into editor...',
      'Configuring project settings...',
      'Installing dependencies...',
      'Running build process...',
      'Generating preview...',
      'Preparing download package...'
    ];

    for (let i = 0; i < steps.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 2000));
      addLog(steps[i]);
      
      if (i === 3) {
        setBoltSession(prev => ({ ...prev, status: 'processing' }));
        setCurrentStep('processing');
      }
    }
  };

  const completeScreenShareWorkflow = () => {
    setBoltSession(prev => ({ 
      ...prev, 
      status: 'complete',
      isScreenSharing: false,
      isRecording: false
    }));

    setBoltProject({
      projectId: `bolt-${Date.now()}`,
      name: scaffoldData?.name || 'untitled-project',
      status: 'ready',
      downloadUrl: 'https://bolt.new/downloads/ultimate-restaurant-manager.zip',
      previewUrl: 'https://ultimate-restaurant-manager.bolt.new',
      files: scaffoldData?.files || []
    });

    addLog('✅ Project successfully created on bolt.new');
    addLog('✅ Screen recording saved');
    addLog('✅ Download package ready');
    setCurrentStep('complete');
  };

  const startApiWorkflow = async () => {
    if (!apiKey.trim()) {
      addLog('❌ API key required');
      return;
    }

    setIsProcessing(true);
    setCurrentStep('processing');
    addLog('🔑 Authenticating with Bolt.new API...');

    try {
      // Simulate API authentication
      await new Promise(resolve => setTimeout(resolve, 1000));
      addLog('✅ Authentication successful');

      // Simulate project creation
      addLog('📦 Creating project via API...');
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const projectId = `api-project-${Date.now()}`;
      addLog(`✅ Project created: ${projectId}`);

      // Simulate file upload
      addLog('📁 Uploading scaffold files...');
      await new Promise(resolve => setTimeout(resolve, 1500));
      addLog(`✅ Uploaded ${scaffoldData?.files.length || 0} files`);

      // Simulate build process
      addLog('🔨 Building project...');
      await new Promise(resolve => setTimeout(resolve, 3000));
      addLog('✅ Build completed successfully');

      // Simulate download preparation
      addLog('📦 Preparing download package...');
      await new Promise(resolve => setTimeout(resolve, 1000));

      setBoltProject({
        projectId,
        name: scaffoldData?.name || 'untitled-project',
        status: 'ready',
        downloadUrl: `https://api.bolt.new/projects/${projectId}/download`,
        previewUrl: `https://${projectId}.bolt.new`,
        files: scaffoldData?.files || []
      });

      addLog('✅ MVP scaffold deployed successfully');
      setCurrentStep('complete');
    } catch (error) {
      addLog('❌ API workflow failed');
      setBoltProject(prev => prev ? { ...prev, status: 'error' } : null);
    } finally {
      setIsProcessing(false);
    }
  };

  const downloadProject = () => {
    if (boltProject?.downloadUrl) {
      window.open(boltProject.downloadUrl, '_blank');
      addLog('📥 Download initiated');
    }
  };

  const openPreview = () => {
    if (boltProject?.previewUrl) {
      window.open(boltProject.previewUrl, '_blank');
      addLog('🌐 Preview opened in new tab');
    }
  };

  const resetWorkflow = () => {
    setCurrentStep('intro');
    setBoltSession({
      sessionId: '',
      liveKitUrl: '',
      isScreenSharing: false,
      isRecording: false,
      duration: 0,
      status: 'idle'
    });
    setBoltProject(null);
    setLogs([]);
    setIsProcessing(false);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center">
        <div className="w-20 h-20 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <Zap className="w-10 h-10 text-white" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Bolt.new Integration Agent
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          Deploy MVP scaffold to Bolt.new with screen share or API
        </p>
      </div>

      {/* Mode Selection */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
        <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Integration Mode</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            onClick={() => setCurrentMode('screen-share')}
            className={`p-4 rounded-lg border-2 transition-all ${
              currentMode === 'screen-share'
                ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20'
                : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
            }`}
          >
            <div className="flex items-center gap-3 mb-2">
              <Monitor className="w-6 h-6 text-orange-500" />
              <h4 className="font-medium text-gray-900 dark:text-white">Screen Share Mode</h4>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 text-left">
              Use Puppeteer + LiveKit to automate bolt.new via screen sharing
            </p>
            <div className="mt-3 flex items-center gap-2">
              <span className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 text-xs rounded-full">
                Available Now
              </span>
            </div>
          </button>

          <button
            onClick={() => setCurrentMode('api')}
            className={`p-4 rounded-lg border-2 transition-all ${
              currentMode === 'api'
                ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20'
                : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
            }`}
          >
            <div className="flex items-center gap-3 mb-2">
              <Key className="w-6 h-6 text-orange-500" />
              <h4 className="font-medium text-gray-900 dark:text-white">API Mode</h4>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 text-left">
              Direct integration via Bolt.new API for seamless deployment
            </p>
            <div className="mt-3 flex items-center gap-2">
              <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs rounded-full">
                Future Release
              </span>
            </div>
          </button>
        </div>
      </div>

      {/* Progress Steps */}
      <div className="flex items-center justify-center space-x-4 mb-8">
        {[
          { id: 'intro', label: 'Setup', icon: Settings },
          { id: 'session', label: currentMode === 'api' ? 'API Call' : 'Screen Share', icon: currentMode === 'api' ? Key : Monitor },
          { id: 'processing', label: 'Processing', icon: Loader2 },
          { id: 'complete', label: 'Complete', icon: CheckCircle }
        ].map((step, index) => (
          <div key={step.id} className="flex items-center">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
              currentStep === step.id 
                ? 'bg-orange-600 text-white' 
                : index < ['intro', 'session', 'processing', 'complete'].indexOf(currentStep)
                ? 'bg-green-500 text-white'
                : 'bg-gray-200 dark:bg-gray-700 text-gray-500'
            }`}>
              <step.icon size={16} />
            </div>
            <span className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">
              {step.label}
            </span>
            {index < 3 && (
              <div className={`w-8 h-0.5 mx-4 ${
                index < ['intro', 'session', 'processing', 'complete'].indexOf(currentStep)
                  ? 'bg-green-500'
                  : 'bg-gray-200 dark:bg-gray-700'
              }`} />
            )}
          </div>
        ))}
      </div>

      {/* Step Content */}
      <AnimatePresence mode="wait">
        {currentStep === 'intro' && (
          <motion.div
            key="intro"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {/* Scaffold Preview */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">MVP Scaffold Ready</h3>
              {scaffoldData && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <h4 className="font-medium text-gray-900 dark:text-white mb-2">Project</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{scaffoldData.name}</p>
                    </div>
                    <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <h4 className="font-medium text-gray-900 dark:text-white mb-2">Tech Stack</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{scaffoldData.techStack}</p>
                    </div>
                    <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <h4 className="font-medium text-gray-900 dark:text-white mb-2">Files</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{scaffoldData.files.length} files</p>
                    </div>
                  </div>

                  <div className="border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div className="p-3 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-700">
                      <h4 className="font-medium text-gray-900 dark:text-white">File Structure</h4>
                    </div>
                    <div className="p-4 max-h-40 overflow-y-auto">
                      {scaffoldData.files.map((file, index) => (
                        <div key={index} className="flex items-center gap-2 py-1">
                          <FileCode size={14} className="text-gray-500" />
                          <span className="text-sm text-gray-700 dark:text-gray-300">{file.path}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* API Key Input (for API mode) */}
            {currentMode === 'api' && (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">API Configuration</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Bolt.new API Key
                    </label>
                    <input
                      type="password"
                      value={apiKey}
                      onChange={(e) => setApiKey(e.target.value)}
                      placeholder="Enter your Bolt.new API key..."
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      API key will be available when Bolt.new releases their public API
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Start Button */}
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={currentMode === 'screen-share' ? startScreenShareSession : startApiWorkflow}
              disabled={currentMode === 'api' && !apiKey.trim()}
              className="w-full flex items-center justify-center gap-3 px-6 py-4 bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 disabled:from-gray-400 disabled:to-gray-500 text-white rounded-lg font-semibold text-lg transition-all disabled:cursor-not-allowed"
            >
              {currentMode === 'screen-share' ? (
                <>
                  <Share2 size={20} />
                  Start LiveKit Screen Share
                </>
              ) : (
                <>
                  <Key size={20} />
                  Deploy via API
                </>
              )}
            </motion.button>
          </motion.div>
        )}

        {currentStep === 'session' && currentMode === 'screen-share' && (
          <motion.div
            key="session"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg"
          >
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                <Monitor className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                LiveKit Screen Share Active
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Duration: {Math.floor(boltSession.duration / 60)}:{(boltSession.duration % 60).toString().padStart(2, '0')}
              </p>
            </div>

            {/* Screen Share Preview */}
            <div className="bg-gray-900 rounded-lg p-6 mb-6 aspect-video relative">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-white">
                  <Globe className="w-16 h-16 mx-auto mb-4 opacity-50" />
                  <p className="text-lg mb-2">bolt.new</p>
                  <p className="text-sm opacity-75">Screen sharing in progress</p>
                </div>
              </div>
              
              {/* Recording Indicator */}
              <div className="absolute top-4 left-4 flex items-center gap-2 bg-red-500 text-white px-3 py-1 rounded-full text-sm">
                <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
                REC
              </div>

              {/* Status Indicator */}
              <div className="absolute top-4 right-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm">
                {boltSession.status === 'connecting' ? 'Connecting...' :
                 boltSession.status === 'active' ? 'Live' :
                 boltSession.status === 'processing' ? 'Processing...' : 'Complete'}
              </div>
            </div>

            {/* Live Activity Log */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6 max-h-40 overflow-y-auto">
              <h3 className="font-medium text-gray-900 dark:text-white mb-2">Live Activity</h3>
              <div className="space-y-1">
                {logs.map((log, index) => (
                  <p key={index} className="text-sm text-gray-600 dark:text-gray-400 font-mono">
                    {log}
                  </p>
                ))}
              </div>
            </div>
          </motion.div>
        )}

        {currentStep === 'processing' && (
          <motion.div
            key="processing"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg text-center"
          >
            <div className="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <Loader2 className="w-8 h-8 text-white animate-spin" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              {currentMode === 'screen-share' ? 'Processing Screen Share' : 'API Deployment in Progress'}
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {currentMode === 'screen-share' 
                ? 'Automating bolt.new interface and generating project files...'
                : 'Uploading scaffold and building project via Bolt.new API...'
              }
            </p>

            {/* Live Activity Log */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 max-h-60 overflow-y-auto">
              <div className="space-y-1">
                {logs.map((log, index) => (
                  <p key={index} className="text-sm text-gray-600 dark:text-gray-400 font-mono text-left">
                    {log}
                  </p>
                ))}
              </div>
            </div>
          </motion.div>
        )}

        {currentStep === 'complete' && boltProject && (
          <motion.div
            key="complete"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="text-center">
              <div className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-10 h-10 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                MVP Successfully Deployed
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Your project is ready on bolt.new with full source code
              </p>
            </div>

            {/* Project Details */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Project Details</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Project ID</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 font-mono">{boltProject.projectId}</p>
                </div>
                <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Status</h4>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    <span className="text-sm text-green-600 capitalize">{boltProject.status}</span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-4">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={downloadProject}
                  className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white rounded-lg font-medium transition-all"
                >
                  <Download size={16} />
                  Download ZIP
                </motion.button>
                <button
                  onClick={openPreview}
                  className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg font-medium transition-colors"
                >
                  <ExternalLink size={16} />
                  Open Preview
                </button>
              </div>
            </div>

            {/* Session Summary */}
            {currentMode === 'screen-share' && (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Session Summary</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                    <Video className="w-8 h-8 text-orange-500 mx-auto mb-2" />
                    <p className="text-lg font-bold text-gray-900 dark:text-white">
                      {Math.floor(boltSession.duration / 60)}:{(boltSession.duration % 60).toString().padStart(2, '0')}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Screen Share</p>
                  </div>
                  <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <FileCode className="w-8 h-8 text-green-500 mx-auto mb-2" />
                    <p className="text-lg font-bold text-gray-900 dark:text-white">{boltProject.files.length}</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Files Created</p>
                  </div>
                  <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <Globe className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                    <p className="text-lg font-bold text-gray-900 dark:text-white">Live</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Preview Ready</p>
                  </div>
                </div>
              </div>
            )}

            {/* Orchestrator Message */}
            <div className="bg-gradient-to-r from-orange-500 to-red-600 rounded-xl p-6 text-white">
              <h3 className="text-lg font-bold mb-2">Message to Orchestrator</h3>
              <p className="text-orange-100 mb-4">
                MVP code ready: <a href={boltProject.downloadUrl} className="underline font-medium">Download ZIP</a>
                {currentMode === 'screen-share' && (
                  <>, demo: <a href={boltSession.liveKitUrl} className="underline font-medium">LiveKit recording</a></>
                )}
              </p>
              <div className="flex items-center gap-2 text-sm text-orange-200">
                <CheckCircle size={16} />
                Ready for next phase of development
              </div>
            </div>

            {/* Reset Button */}
            <button
              onClick={resetWorkflow}
              className="w-full px-6 py-3 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium transition-colors"
            >
              Deploy Another Project
            </button>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};