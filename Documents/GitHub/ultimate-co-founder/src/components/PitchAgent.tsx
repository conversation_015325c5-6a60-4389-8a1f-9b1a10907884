import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Presentation, 
  Users, 
  Target, 
  TrendingUp,
  Rocket,
  DollarSign,
  Download,
  FileText,
  Play,
  Pause,
  SkipForward,
  SkipBack,
  Lightbulb,
  MessageSquare,
  Clock,
  CheckCircle,
  Star,
  Globe,
  Zap,
  BarChart3,
  Shield,
  Trophy,
  ArrowRight,
  Eye,
  Mic,
  Video
} from 'lucide-react';

interface PitchSlide {
  id: number;
  title: string;
  content: string[];
  speakerNotes: string[];
  visualElements: string[];
  keyMetrics?: {
    label: string;
    value: string;
    highlight?: boolean;
  }[];
  duration: number; // seconds
}

interface InvestorMemo {
  summary: string;
  keyPoints: string[];
  traction: string[];
  ask: {
    amount: string;
    use: string[];
    timeline: string;
  };
  nextSteps: string[];
  attachments: string[];
}

interface YCTip {
  category: 'storytelling' | 'brevity' | 'qa' | 'delivery';
  title: string;
  description: string;
  example?: string;
}

export const PitchAgent: React.FC = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPresenting, setIsPresenting] = useState(false);
  const [presentationTime, setPresentationTime] = useState(0);
  const [slides, setSlides] = useState<PitchSlide[]>([]);
  const [investorMemo, setInvestorMemo] = useState<InvestorMemo | null>(null);
  const [ycTips, setYcTips] = useState<YCTip[]>([]);
  const [currentView, setCurrentView] = useState<'deck' | 'memo' | 'tips'>('deck');
  const [showSpeakerNotes, setShowSpeakerNotes] = useState(true);

  useEffect(() => {
    initializePitchContent();
  }, []);

  const initializePitchContent = () => {
    // YC-Style Pitch Deck Slides
    const pitchSlides: PitchSlide[] = [
      {
        id: 1,
        title: 'Problem',
        content: [
          'Startups struggle with fragmented business expertise',
          '73% of startups fail due to lack of strategic guidance',
          'Founders wear too many hats without domain expertise',
          'Traditional consultants are expensive and not always available'
        ],
        speakerNotes: [
          'Start with a relatable problem that resonates with investors',
          'Use the statistic about startup failure rates to establish urgency',
          'Paint a picture of the overwhelmed founder trying to do everything',
          'Mention that this affects both first-time and experienced entrepreneurs'
        ],
        visualElements: [
          'Frustrated founder at computer with multiple browser tabs',
          'Statistics showing startup failure rates',
          'Icons representing different business functions'
        ],
        duration: 60
      },
      {
        id: 2,
        title: 'Solution',
        content: [
          'Ultimate Startup Co-founder: 5 AI agents providing specialized expertise',
          'Strategic, Product, Technical, Operations, and Marketing co-founders',
          'Real-time video/voice collaboration via LiveKit',
          'Integrated with Bolt.new for instant MVP deployment'
        ],
        speakerNotes: [
          'Present the solution as a complete AI co-founder team',
          'Emphasize the specialization - each agent is an expert in their domain',
          'Highlight the real-time collaboration aspect - not just chatbots',
          'Mention the integration with Bolt.new as a unique differentiator'
        ],
        visualElements: [
          'Dashboard showing 5 AI co-founder agents',
          'LiveKit video call interface',
          'Bolt.new integration demo'
        ],
        duration: 90
      },
      {
        id: 3,
        title: 'Why Now',
        content: [
          'AI capabilities have reached human-level expertise in specialized domains',
          'Remote work normalized video-first collaboration',
          'No-code/low-code movement democratizing development',
          'Startup ecosystem growing 15% annually with $300B+ invested in 2024'
        ],
        speakerNotes: [
          'Explain the convergence of AI advancement and market readiness',
          'COVID accelerated acceptance of video-based business interactions',
          'Tools like Bolt.new make technical implementation accessible',
          'Growing startup ecosystem creates expanding market opportunity'
        ],
        visualElements: [
          'Timeline showing AI capability progression',
          'Remote work adoption statistics',
          'Startup funding growth chart'
        ],
        duration: 75
      },
      {
        id: 4,
        title: 'Market Size',
        content: [
          'TAM: $2.3T - Global Software Market',
          'SAM: $180B - AI-Powered Business Tools',
          'SOM: $2.1B - AI Co-founder & Business Intelligence Tools',
          'Growing 25% annually with increasing AI adoption'
        ],
        speakerNotes: [
          'Start with TAM to show the massive opportunity',
          'SAM represents the addressable market for AI business tools',
          'SOM is our realistic target market - still huge at $2.1B',
          'Emphasize the growth rate and AI adoption trends'
        ],
        visualElements: [
          'Concentric circles showing TAM/SAM/SOM',
          'Market growth projection chart',
          'AI adoption curve by business function'
        ],
        keyMetrics: [
          { label: 'TAM', value: '$2.3T', highlight: true },
          { label: 'SAM', value: '$180B', highlight: true },
          { label: 'SOM', value: '$2.1B', highlight: true },
          { label: 'Growth Rate', value: '25%', highlight: false }
        ],
        duration: 90
      },
      {
        id: 5,
        title: 'Product Demo',
        content: [
          'Live demonstration of 5-agent coordination',
          'Strategic Agent: Market analysis with voice briefing',
          'Product Agent: User interviews and feature prioritization',
          'Technical Agent: Architecture and MVP generation',
          'Operations Agent: Legal, financial, and hiring plans',
          'Marketing Agent: Brand positioning and growth strategy'
        ],
        speakerNotes: [
          'This is the most important slide - show, don\'t tell',
          'Walk through a real startup idea being analyzed',
          'Highlight the speed and quality of insights generated',
          'Show the LiveKit video interactions and Bolt.new deployment',
          'Emphasize the comprehensive nature - covers all startup needs'
        ],
        visualElements: [
          'Live product demonstration',
          'Agent coordination workflow',
          'Real-time report generation',
          'Bolt.new deployment process'
        ],
        duration: 180
      },
      {
        id: 6,
        title: 'Business Model',
        content: [
          'SaaS subscription tiers: $29, $99, $299/month',
          'Usage-based billing for overages',
          'Enterprise white-label packages',
          'Revenue sharing with Bolt.new (15%), LiveKit (8%), Composio (12%)'
        ],
        speakerNotes: [
          'Clear, simple pricing model that scales with customer growth',
          'Multiple revenue streams reduce risk and increase LTV',
          'Enterprise tier provides high-margin expansion opportunity',
          'Strategic partnerships create win-win revenue sharing'
        ],
        visualElements: [
          'Pricing tier comparison table',
          'Revenue stream breakdown',
          'Partnership revenue model'
        ],
        keyMetrics: [
          { label: 'Starter', value: '$29/mo', highlight: false },
          { label: 'Professional', value: '$99/mo', highlight: true },
          { label: 'Enterprise', value: '$299/mo', highlight: false },
          { label: 'Partner Revenue', value: '35%', highlight: false }
        ],
        duration: 75
      },
      {
        id: 7,
        title: 'Competition',
        content: [
          'Direct: Harvey AI (legal), Anthropic Claude (general)',
          'Indirect: Jasper AI (content), Copy.ai (writing), Notion AI (productivity)',
          'Our advantage: Multi-agent specialization + real-time collaboration',
          'First-mover in comprehensive AI co-founder space'
        ],
        speakerNotes: [
          'Acknowledge competition but highlight our unique positioning',
          'Most competitors focus on single functions, we provide complete team',
          'Real-time video/voice collaboration is a key differentiator',
          'Being first in the AI co-founder category gives us significant advantage'
        ],
        visualElements: [
          'Competitive landscape matrix',
          'Feature comparison chart',
          'Market positioning diagram'
        ],
        duration: 60
      },
      {
        id: 8,
        title: 'Go-to-Market',
        content: [
          'Phase 1: Beta with 50 startups, accelerator partnerships',
          'Phase 2: Product Hunt launch, content marketing, thought leadership',
          'Phase 3: Enterprise sales, international expansion',
          'Key channels: Y Combinator, Techstars, startup communities'
        ],
        speakerNotes: [
          'Phased approach reduces risk and validates product-market fit',
          'Accelerator partnerships provide access to target customers',
          'Product Hunt launch creates viral marketing opportunity',
          'Enterprise sales provide high-value customer acquisition'
        ],
        visualElements: [
          'GTM timeline and phases',
          'Customer acquisition channels',
          'Partnership ecosystem map'
        ],
        duration: 75
      },
      {
        id: 9,
        title: 'Team',
        content: [
          'CEO: 10+ years building AI products, 2 successful exits',
          'CTO: Former Google AI, led teams of 50+ engineers',
          'Head of Product: Ex-Figma, designed tools used by 10M+ users',
          'Advisors: YC partners, StackBlitz founders, LiveKit team'
        ],
        speakerNotes: [
          'Highlight relevant experience and track record',
          'Emphasize AI expertise and startup experience',
          'Show that the team has built products at scale',
          'Strong advisor network provides credibility and connections'
        ],
        visualElements: [
          'Team photos and backgrounds',
          'Previous company logos',
          'Advisor network diagram'
        ],
        duration: 60
      },
      {
        id: 10,
        title: 'Traction & Milestones',
        content: [
          'MVP completed with all 5 agents functional',
          '50 beta users with 85% weekly retention',
          'Partnerships signed with StackBlitz, LiveKit, Composio',
          'Pre-seed funding: $500K from angels and accelerator'
        ],
        speakerNotes: [
          'Show concrete progress and validation',
          'High retention rate indicates strong product-market fit',
          'Strategic partnerships validate the business model',
          'Previous funding shows investor confidence'
        ],
        visualElements: [
          'Traction metrics dashboard',
          'User growth chart',
          'Partnership logos'
        ],
        keyMetrics: [
          { label: 'Beta Users', value: '50', highlight: false },
          { label: 'Retention', value: '85%', highlight: true },
          { label: 'Partnerships', value: '3', highlight: false },
          { label: 'Pre-seed', value: '$500K', highlight: false }
        ],
        duration: 75
      },
      {
        id: 11,
        title: 'Financials',
        content: [
          'Year 1: $675K revenue, 675 customers',
          'Year 2: $1.8M revenue, 1,675 customers',
          'Year 3: $3.2M revenue, 2,950 customers',
          'Path to profitability by Month 18'
        ],
        speakerNotes: [
          'Conservative projections based on SaaS benchmarks',
          'Show clear path to profitability',
          'Customer growth aligns with market expansion',
          'Revenue projections account for churn and expansion'
        ],
        visualElements: [
          'Revenue growth chart',
          'Customer acquisition projections',
          'Unit economics breakdown'
        ],
        keyMetrics: [
          { label: 'Year 1 Revenue', value: '$675K', highlight: false },
          { label: 'Year 2 Revenue', value: '$1.8M', highlight: true },
          { label: 'Year 3 Revenue', value: '$3.2M', highlight: true },
          { label: 'Profitability', value: 'Month 18', highlight: false }
        ],
        duration: 90
      },
      {
        id: 12,
        title: 'The Ask',
        content: [
          'Raising $2M Series A to accelerate growth',
          '60% Engineering & Product Development',
          '25% Sales & Marketing',
          '15% Operations & Legal',
          '18-month runway to profitability and Series B readiness'
        ],
        speakerNotes: [
          'Clear ask amount with specific use of funds',
          'Majority of funds going to product development shows focus',
          'Sales and marketing investment will drive growth',
          'Operations investment ensures scalable foundation',
          '18-month runway provides sufficient time to hit milestones'
        ],
        visualElements: [
          'Funding use breakdown pie chart',
          'Milestone timeline',
          'Series B preparation roadmap'
        ],
        keyMetrics: [
          { label: 'Funding Ask', value: '$2M', highlight: true },
          { label: 'Engineering', value: '60%', highlight: false },
          { label: 'Sales & Marketing', value: '25%', highlight: false },
          { label: 'Runway', value: '18 months', highlight: true }
        ],
        duration: 90
      }
    ];

    // Investor Memo
    const memo: InvestorMemo = {
      summary: 'Ultimate Startup Co-founder is the first comprehensive AI co-founder platform featuring 5 specialized agents (Strategic, Product, Technical, Operations, Marketing) that provide real-time video/voice collaboration to help startups build and scale faster. With partnerships with StackBlitz, LiveKit, and Composio, we\'re positioned to capture a significant portion of the $2.1B AI business tools market.',
      keyPoints: [
        'First-mover advantage in AI co-founder category with comprehensive 5-agent system',
        'Real-time video/voice collaboration differentiates from chatbot competitors',
        'Strategic partnerships with StackBlitz (Bolt.new), LiveKit, and Composio create ecosystem',
        'Clear path to $3.2M ARR by Year 3 with 85% gross margins',
        'Experienced team with AI expertise and successful startup exits'
      ],
      traction: [
        '50 beta users with 85% weekly retention rate',
        'MVP completed with all 5 agents functional and integrated',
        'Strategic partnerships signed with key technology providers',
        '$500K pre-seed funding from angels and accelerator',
        'Product Hunt launch planned for Q1 2025'
      ],
      ask: {
        amount: '$2M Series A',
        use: [
          '60% Engineering & Product Development - Scale AI agents and add enterprise features',
          '25% Sales & Marketing - Customer acquisition and brand building',
          '15% Operations & Legal - Compliance, security, and operational infrastructure'
        ],
        timeline: '18-month runway to profitability and Series B readiness'
      },
      nextSteps: [
        'Schedule follow-up meeting to discuss partnership opportunities',
        'Provide access to beta platform for hands-on evaluation',
        'Share detailed financial model and customer acquisition metrics',
        'Introduce to key customers and strategic partners',
        'Review term sheet and investment timeline'
      ],
      attachments: [
        'Financial Model & Projections (Excel)',
        'Product Demo Video (10 minutes)',
        'Customer Reference Letters',
        'Technical Architecture Documentation',
        'Competitive Analysis Deep Dive'
      ]
    };

    // Y Combinator Tips
    const tips: YCTip[] = [
      {
        category: 'storytelling',
        title: 'Start with the Problem, Not the Solution',
        description: 'Lead with a problem that resonates emotionally with investors. Make them feel the pain before presenting your solution.',
        example: 'Instead of "We built an AI platform," start with "73% of startups fail because founders lack specialized business expertise."'
      },
      {
        category: 'storytelling',
        title: 'Use the "Before and After" Narrative',
        description: 'Paint a clear picture of the world before your solution and how dramatically it improves after.',
        example: 'Before: Founders spend weeks researching market strategy. After: Get comprehensive market analysis in 2 minutes.'
      },
      {
        category: 'brevity',
        title: 'One Key Point Per Slide',
        description: 'Each slide should make exactly one key point. If you have multiple points, use multiple slides.',
        example: 'Problem slide focuses only on the problem. Solution slide focuses only on the solution.'
      },
      {
        category: 'brevity',
        title: 'Use the 10-20-30 Rule',
        description: '10 slides maximum, 20 minutes maximum, 30-point font minimum. Forces clarity and prevents information overload.',
        example: 'Our 12-slide deck can be presented in 15 minutes with 5 minutes for questions.'
      },
      {
        category: 'qa',
        title: 'Prepare for the "Why Now?" Question',
        description: 'Investors always ask why this solution is possible now but wasn\'t 5 years ago. Have a compelling answer.',
        example: 'AI capabilities, remote work adoption, and no-code tools have converged to make this possible now.'
      },
      {
        category: 'qa',
        title: 'Know Your Unit Economics Cold',
        description: 'Be able to explain customer acquisition cost, lifetime value, and payback period without hesitation.',
        example: 'CAC: $150, LTV: $2,400, Payback: 6 months, LTV/CAC ratio: 16x'
      },
      {
        category: 'delivery',
        title: 'Practice the Demo Until It\'s Flawless',
        description: 'Your product demo is the most important part. Practice until you can do it perfectly under pressure.',
        example: 'Rehearse the demo 50+ times. Have backup plans for technical failures.'
      },
      {
        category: 'delivery',
        title: 'Tell Stories, Not Statistics',
        description: 'Investors remember stories better than numbers. Use customer stories to illustrate your points.',
        example: 'Instead of "85% retention," say "Sarah launched her startup in 2 weeks using our platform and now can\'t imagine building without it."'
      }
    ];

    setSlides(pitchSlides);
    setInvestorMemo(memo);
    setYcTips(tips);
  };

  const startPresentation = () => {
    setIsPresenting(true);
    setPresentationTime(0);
    
    const timer = setInterval(() => {
      setPresentationTime(prev => prev + 1);
    }, 1000);

    // Auto-advance slides based on duration
    setTimeout(() => {
      clearInterval(timer);
      setIsPresenting(false);
    }, slides.reduce((total, slide) => total + slide.duration, 0) * 1000);
  };

  const nextSlide = () => {
    if (currentSlide < slides.length - 1) {
      setCurrentSlide(currentSlide + 1);
    }
  };

  const prevSlide = () => {
    if (currentSlide > 0) {
      setCurrentSlide(currentSlide - 1);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const generatePitchDeck = () => {
    return `# Ultimate Startup Co-founder - Investor Pitch Deck

## Slide Deck Outline (12 Slides)

${slides.map(slide => `
### Slide ${slide.id}: ${slide.title}

**Content:**
${slide.content.map(point => `- ${point}`).join('\n')}

**Speaker Notes:**
${slide.speakerNotes.map(note => `- ${note}`).join('\n')}

**Visual Elements:**
${slide.visualElements.map(element => `- ${element}`).join('\n')}

${slide.keyMetrics ? `**Key Metrics:**
${slide.keyMetrics.map(metric => `- ${metric.label}: ${metric.value}${metric.highlight ? ' (HIGHLIGHT)' : ''}`).join('\n')}` : ''}

**Recommended Duration:** ${slide.duration} seconds
`).join('')}

## Presentation Flow & Timing

**Total Presentation Time:** ${slides.reduce((total, slide) => total + slide.duration, 0) / 60} minutes
**Recommended Q&A Time:** 5-10 minutes
**Total Meeting Duration:** 20-25 minutes

## Key Messaging Framework

1. **Hook:** 73% of startups fail due to lack of specialized expertise
2. **Solution:** First comprehensive AI co-founder team with real-time collaboration
3. **Market:** $2.1B addressable market growing 25% annually
4. **Traction:** 50 beta users, 85% retention, strategic partnerships
5. **Ask:** $2M Series A for 18-month runway to profitability

## Presentation Best Practices

- Start with the problem to create emotional connection
- Use the product demo as the centerpiece (3 minutes)
- Keep slides visual with minimal text
- Practice transitions between slides
- Prepare for technical demo backup plans
- End with clear ask and next steps

---
*Generated by PitchAgent | Y Combinator Best Practices*`;
  };

  const generateInvestorMemo = () => {
    if (!investorMemo) return '';

    return `# Ultimate Startup Co-founder - Investor Memo

## Executive Summary

${investorMemo.summary}

## Key Investment Highlights

${investorMemo.keyPoints.map(point => `- ${point}`).join('\n')}

## Traction & Validation

${investorMemo.traction.map(item => `- ${item}`).join('\n')}

## Investment Details

**Funding Round:** ${investorMemo.ask.amount}
**Use of Funds:**
${investorMemo.ask.use.map(use => `- ${use}`).join('\n')}

**Timeline:** ${investorMemo.ask.timeline}

## Next Steps

${investorMemo.nextSteps.map(step => `1. ${step}`).join('\n')}

## Supporting Materials

${investorMemo.attachments.map(attachment => `- ${attachment}`).join('\n')}

---

**Contact Information:**
- CEO: <EMAIL>
- Deck: https://pitch.ultimate-cofounder.com
- Demo: https://demo.ultimate-cofounder.com

*This memo summarizes our Series A opportunity. Please reach out with any questions or to schedule a follow-up meeting.*`;
  };

  const downloadPitchDeck = () => {
    const deck = generatePitchDeck();
    const blob = new Blob([deck], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'ultimate-cofounder-pitch-deck.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const downloadInvestorMemo = () => {
    const memo = generateInvestorMemo();
    const blob = new Blob([memo], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'investor-memo.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center">
        <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <Presentation className="w-10 h-10 text-white" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          PitchAgent
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          Y Combinator-style investor pitch preparation
        </p>
      </div>

      {/* Navigation */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
        <div className="flex gap-4">
          {[
            { id: 'deck', label: 'Pitch Deck', icon: Presentation },
            { id: 'memo', label: 'Investor Memo', icon: FileText },
            { id: 'tips', label: 'YC Tips', icon: Lightbulb }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setCurrentView(tab.id as any)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                currentView === tab.id
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              <tab.icon size={16} />
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <AnimatePresence mode="wait">
        {currentView === 'deck' && (
          <motion.div
            key="deck"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {/* Presentation Controls */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                  12-Slide Investor Pitch Deck
                </h3>
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <label className="text-sm text-gray-600 dark:text-gray-400">
                      Speaker Notes:
                    </label>
                    <button
                      onClick={() => setShowSpeakerNotes(!showSpeakerNotes)}
                      className={`px-3 py-1 text-xs rounded-full transition-colors ${
                        showSpeakerNotes
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                      }`}
                    >
                      {showSpeakerNotes ? 'ON' : 'OFF'}
                    </button>
                  </div>
                  {isPresenting && (
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      Time: {formatTime(presentationTime)}
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <button
                    onClick={prevSlide}
                    disabled={currentSlide === 0}
                    className="p-2 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                  >
                    <SkipBack size={16} />
                  </button>
                  <button
                    onClick={isPresenting ? () => setIsPresenting(false) : startPresentation}
                    className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                      isPresenting
                        ? 'bg-red-500 hover:bg-red-600 text-white'
                        : 'bg-blue-500 hover:bg-blue-600 text-white'
                    }`}
                  >
                    {isPresenting ? <Pause size={16} /> : <Play size={16} />}
                    {isPresenting ? 'Stop' : 'Start'} Presentation
                  </button>
                  <button
                    onClick={nextSlide}
                    disabled={currentSlide === slides.length - 1}
                    className="p-2 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                  >
                    <SkipForward size={16} />
                  </button>
                </div>

                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Slide {currentSlide + 1} of {slides.length}
                </div>
              </div>
            </div>

            {/* Current Slide */}
            {slides[currentSlide] && (
              <motion.div
                key={currentSlide}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg"
              >
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Slide Content */}
                  <div>
                    <div className="flex items-center gap-3 mb-6">
                      <div className="w-10 h-10 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold">
                        {slides[currentSlide].id}
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                        {slides[currentSlide].title}
                      </h2>
                    </div>

                    <div className="space-y-4 mb-6">
                      {slides[currentSlide].content.map((point, index) => (
                        <div key={index} className="flex items-start gap-3">
                          <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                          <p className="text-gray-700 dark:text-gray-300">{point}</p>
                        </div>
                      ))}
                    </div>

                    {/* Key Metrics */}
                    {slides[currentSlide].keyMetrics && (
                      <div className="grid grid-cols-2 gap-4 mb-6">
                        {slides[currentSlide].keyMetrics!.map((metric, index) => (
                          <div
                            key={index}
                            className={`text-center p-4 rounded-lg ${
                              metric.highlight
                                ? 'bg-blue-50 dark:bg-blue-900/20 border-2 border-blue-500'
                                : 'bg-gray-50 dark:bg-gray-700'
                            }`}
                          >
                            <p className={`text-2xl font-bold ${
                              metric.highlight ? 'text-blue-600 dark:text-blue-400' : 'text-gray-900 dark:text-white'
                            }`}>
                              {metric.value}
                            </p>
                            <p className="text-sm text-gray-600 dark:text-gray-400">{metric.label}</p>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Visual Elements */}
                    <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                      <h4 className="font-medium text-gray-900 dark:text-white mb-2">Visual Elements</h4>
                      <div className="space-y-1">
                        {slides[currentSlide].visualElements.map((element, index) => (
                          <div key={index} className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                            <Eye size={12} />
                            {element}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Speaker Notes */}
                  {showSpeakerNotes && (
                    <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-6">
                      <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-4 flex items-center gap-2">
                        <Mic size={16} />
                        Speaker Notes
                      </h4>
                      <div className="space-y-3">
                        {slides[currentSlide].speakerNotes.map((note, index) => (
                          <div key={index} className="flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-yellow-500 rounded-full mt-2 flex-shrink-0" />
                            <p className="text-sm text-yellow-700 dark:text-yellow-300">{note}</p>
                          </div>
                        ))}
                      </div>
                      <div className="mt-4 pt-4 border-t border-yellow-200 dark:border-yellow-800">
                        <div className="flex items-center gap-2 text-sm text-yellow-600 dark:text-yellow-400">
                          <Clock size={12} />
                          Recommended duration: {slides[currentSlide].duration} seconds
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </motion.div>
            )}

            {/* Slide Navigation */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
              <h4 className="font-medium text-gray-900 dark:text-white mb-4">Slide Overview</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2">
                {slides.map((slide, index) => (
                  <button
                    key={slide.id}
                    onClick={() => setCurrentSlide(index)}
                    className={`p-3 text-left rounded-lg transition-colors ${
                      currentSlide === index
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'
                    }`}
                  >
                    <div className="text-xs font-medium mb-1">Slide {slide.id}</div>
                    <div className="text-xs opacity-75">{slide.title}</div>
                  </button>
                ))}
              </div>
            </div>
          </motion.div>
        )}

        {currentView === 'memo' && investorMemo && (
          <motion.div
            key="memo"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">One-Page Investor Memo</h2>
              <p className="text-gray-600 dark:text-gray-400">Concise summary for investor follow-up</p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
              {/* Executive Summary */}
              <div className="mb-8">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Executive Summary</h3>
                <p className="text-gray-700 dark:text-gray-300 leading-relaxed">{investorMemo.summary}</p>
              </div>

              {/* Key Points */}
              <div className="mb-8">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Key Investment Highlights</h3>
                <div className="space-y-3">
                  {investorMemo.keyPoints.map((point, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <Star className="w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0" />
                      <p className="text-gray-700 dark:text-gray-300">{point}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Traction */}
              <div className="mb-8">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Traction & Validation</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {investorMemo.traction.map((item, index) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <p className="text-sm text-gray-700 dark:text-gray-300">{item}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* The Ask */}
              <div className="mb-8 p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <h3 className="text-lg font-bold text-blue-900 dark:text-blue-200 mb-4">Investment Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-2">Funding Round</h4>
                    <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{investorMemo.ask.amount}</p>
                    <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">{investorMemo.ask.timeline}</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-2">Use of Funds</h4>
                    <div className="space-y-1">
                      {investorMemo.ask.use.map((use, index) => (
                        <p key={index} className="text-sm text-blue-700 dark:text-blue-300">{use}</p>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Next Steps */}
              <div className="mb-8">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Next Steps</h3>
                <div className="space-y-2">
                  {investorMemo.nextSteps.map((step, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <div className="w-6 h-6 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                        {index + 1}
                      </div>
                      <p className="text-gray-700 dark:text-gray-300">{step}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Attachments */}
              <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Supporting Materials</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {investorMemo.attachments.map((attachment, index) => (
                    <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <FileText className="w-5 h-5 text-gray-500" />
                      <span className="text-sm text-gray-700 dark:text-gray-300">{attachment}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {currentView === 'tips' && (
          <motion.div
            key="tips"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Y Combinator Pitch Tips</h2>
              <p className="text-gray-600 dark:text-gray-400">Best practices for storytelling, brevity, and Q&A handling</p>
            </div>

            {/* Tips by Category */}
            {['storytelling', 'brevity', 'qa', 'delivery'].map((category) => (
              <div key={category} className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4 capitalize flex items-center gap-2">
                  {category === 'storytelling' && <MessageSquare className="w-5 h-5 text-purple-500" />}
                  {category === 'brevity' && <Clock className="w-5 h-5 text-blue-500" />}
                  {category === 'qa' && <Users className="w-5 h-5 text-green-500" />}
                  {category === 'delivery' && <Presentation className="w-5 h-5 text-orange-500" />}
                  {category} Tips
                </h3>
                
                <div className="space-y-4">
                  {ycTips.filter(tip => tip.category === category).map((tip, index) => (
                    <div key={index} className="border-l-4 border-gray-200 dark:border-gray-700 pl-4">
                      <h4 className="font-medium text-gray-900 dark:text-white mb-2">{tip.title}</h4>
                      <p className="text-gray-600 dark:text-gray-400 mb-3">{tip.description}</p>
                      {tip.example && (
                        <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <p className="text-sm text-gray-700 dark:text-gray-300">
                            <strong>Example:</strong> {tip.example}
                          </p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ))}

            {/* YC Resources */}
            <div className="bg-gradient-to-r from-orange-500 to-red-600 rounded-xl p-6 text-white">
              <h3 className="text-lg font-bold mb-4">Additional YC Resources</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium">Essential Reading</h4>
                  <ul className="text-sm space-y-1 opacity-90">
                    <li>• How to Build Your Seed Deck (YC)</li>
                    <li>• The Art of the Demo (YC)</li>
                    <li>• Fundraising Mistakes (YC)</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">Practice Tools</h4>
                  <ul className="text-sm space-y-1 opacity-90">
                    <li>• YC Demo Day Videos</li>
                    <li>• Pitch Practice Groups</li>
                    <li>• Investor Q&A Simulations</li>
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Download Actions */}
      <div className="flex gap-4">
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={downloadPitchDeck}
          className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-lg font-medium transition-all"
        >
          <Download size={16} />
          Download Pitch Deck
        </motion.button>
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={downloadInvestorMemo}
          className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white rounded-lg font-medium transition-all"
        >
          <FileText size={16} />
          Download Investor Memo
        </motion.button>
      </div>
    </div>
  );
};