import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FileText, 
  Presentation, 
  Table, 
  Mic, 
  Download, 
  ExternalLink, 
  Plus,
  Loader2,
  CheckCircle,
  AlertCircle,
  Search,
  Filter,
  Calendar,
  Clock,
  X,
  RefreshCw
} from 'lucide-react';
import { skyworkAI, SkyworkDocument, GenerateDocumentRequest } from '../services/skywork-ai';
import toast from 'react-hot-toast';

export const SkyworkAIPanel: React.FC = () => {
  const [documents, setDocuments] = useState<SkyworkDocument[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showGenerateModal, setShowGenerateModal] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<SkyworkDocument | null>(null);
  const [showDocumentModal, setShowDocumentModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Form state for document generation
  const [generateForm, setGenerateForm] = useState<GenerateDocumentRequest>({
    type: 'document',
    title: '',
    prompt: '',
    template: 'standard',
    additionalContext: ''
  });

  useEffect(() => {
    loadDocuments();
  }, []);

  const loadDocuments = async () => {
    setIsLoading(true);
    try {
      const docs = await skyworkAI.listDocuments();
      setDocuments(docs);
    } catch (error) {
      console.error('Error loading documents:', error);
      toast.error('Failed to load documents');
    } finally {
      setIsLoading(false);
    }
  };

  const refreshDocuments = async () => {
    setIsRefreshing(true);
    try {
      await loadDocuments();
      toast.success('Documents refreshed');
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleGenerateDocument = async () => {
    if (!generateForm.title || !generateForm.prompt) {
      toast.error('Title and prompt are required');
      return;
    }

    setIsLoading(true);
    try {
      const newDocument = await skyworkAI.generateDocument(generateForm);
      setDocuments(prev => [newDocument, ...prev]);
      setShowGenerateModal(false);
      toast.success(`${getDocumentTypeName(generateForm.type)} generated successfully`);
      
      // Reset form
      setGenerateForm({
        type: 'document',
        title: '',
        prompt: '',
        template: 'standard',
        additionalContext: ''
      });
    } catch (error) {
      console.error('Error generating document:', error);
      toast.error('Failed to generate document');
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewDocument = (document: SkyworkDocument) => {
    setSelectedDocument(document);
    setShowDocumentModal(true);
  };

  const getDocumentTypeName = (type: string): string => {
    switch (type) {
      case 'document':
        return 'Document';
      case 'slide':
        return 'Presentation';
      case 'sheet':
        return 'Spreadsheet';
      case 'podcast':
        return 'Podcast';
      default:
        return 'File';
    }
  };

  const getDocumentTypeIcon = (type: string) => {
    switch (type) {
      case 'document':
        return <FileText className="w-5 h-5 text-blue-500" />;
      case 'slide':
        return <Presentation className="w-5 h-5 text-orange-500" />;
      case 'sheet':
        return <Table className="w-5 h-5 text-green-500" />;
      case 'podcast':
        return <Mic className="w-5 h-5 text-purple-500" />;
      default:
        return <FileText className="w-5 h-5 text-gray-500" />;
    }
  };

  const filteredDocuments = documents
    .filter(doc => doc.title.toLowerCase().includes(searchQuery.toLowerCase()))
    .filter(doc => !filterType || doc.type === filterType);

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="text-center">
        <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <FileText className="w-8 h-8 text-white" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          SkyworkAI Documents
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Generate professional documents, slides, sheets, and podcasts
        </p>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search documents..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>
        <div className="flex gap-2">
          <select
            value={filterType || ''}
            onChange={(e) => setFilterType(e.target.value || null)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="">All Types</option>
            <option value="document">Documents</option>
            <option value="slide">Presentations</option>
            <option value="sheet">Spreadsheets</option>
            <option value="podcast">Podcasts</option>
          </select>
          <button
            onClick={refreshDocuments}
            className="p-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
            disabled={isRefreshing}
          >
            {isRefreshing ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <RefreshCw className="w-5 h-5" />
            )}
          </button>
        </div>
      </div>

      {/* Create New Button */}
      <button
        onClick={() => setShowGenerateModal(true)}
        className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white rounded-lg font-medium transition-colors"
      >
        <Plus className="w-5 h-5" />
        Generate New Content
      </button>

      {/* Documents List */}
      {isLoading && documents.length === 0 ? (
        <div className="text-center py-12">
          <Loader2 className="w-8 h-8 text-indigo-500 mx-auto mb-4 animate-spin" />
          <p className="text-gray-600 dark:text-gray-400">Loading documents...</p>
        </div>
      ) : filteredDocuments.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No documents found</h4>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {searchQuery || filterType
              ? 'Try adjusting your search or filters'
              : 'Generate your first document to get started'}
          </p>
          {(searchQuery || filterType) && (
            <button
              onClick={() => {
                setSearchQuery('');
                setFilterType(null);
              }}
              className="text-indigo-600 dark:text-indigo-400 hover:underline"
            >
              Clear filters
            </button>
          )}
        </div>
      ) : (
        <div className="space-y-3">
          {filteredDocuments.map((document) => (
            <div
              key={document.id}
              className="bg-white dark:bg-gray-700 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => handleViewDocument(document)}
            >
              <div className="flex items-start gap-3">
                <div className="mt-1">
                  {getDocumentTypeIcon(document.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-gray-900 dark:text-white truncate">{document.title}</h4>
                  <div className="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400 mt-1">
                    <span className="flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      {new Date(document.createdAt).toLocaleDateString()}
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {new Date(document.createdAt).toLocaleTimeString()}
                    </span>
                    <span className="px-2 py-0.5 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-700 dark:text-indigo-300 rounded-full capitalize">
                      {document.type}
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-1">
                  {document.status === 'generated' ? (
                    <CheckCircle className="w-4 h-4 text-green-500" />
                  ) : document.status === 'error' ? (
                    <AlertCircle className="w-4 h-4 text-red-500" />
                  ) : (
                    <Loader2 className="w-4 h-4 text-indigo-500 animate-spin" />
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Generate Document Modal */}
      <AnimatePresence>
        {showGenerateModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="bg-white dark:bg-gray-800 rounded-lg max-w-lg w-full"
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">Generate New Content</h3>
                  <button 
                    onClick={() => setShowGenerateModal(false)}
                    className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Content Type
                    </label>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                      {[
                        { value: 'document', label: 'Document', icon: FileText },
                        { value: 'slide', label: 'Presentation', icon: Presentation },
                        { value: 'sheet', label: 'Spreadsheet', icon: Table },
                        { value: 'podcast', label: 'Podcast', icon: Mic }
                      ].map((type) => (
                        <button
                          key={type.value}
                          type="button"
                          onClick={() => setGenerateForm(prev => ({ ...prev, type: type.value as any }))}
                          className={`p-3 rounded-lg text-center transition-colors ${
                            generateForm.type === type.value
                              ? 'bg-indigo-100 dark:bg-indigo-900/30 border-2 border-indigo-500'
                              : 'bg-gray-50 dark:bg-gray-700 border-2 border-transparent hover:bg-gray-100 dark:hover:bg-gray-600'
                          }`}
                        >
                          <type.icon className={`w-6 h-6 mx-auto mb-1 ${
                            generateForm.type === type.value
                              ? 'text-indigo-600 dark:text-indigo-400'
                              : 'text-gray-500 dark:text-gray-400'
                          }`} />
                          <span className={`text-sm ${
                            generateForm.type === type.value
                              ? 'font-medium text-indigo-700 dark:text-indigo-300'
                              : 'text-gray-700 dark:text-gray-300'
                          }`}>
                            {type.label}
                          </span>
                        </button>
                      ))}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Title
                    </label>
                    <input
                      type="text"
                      value={generateForm.title}
                      onChange={(e) => setGenerateForm(prev => ({ ...prev, title: e.target.value }))}
                      placeholder={`Enter ${getDocumentTypeName(generateForm.type).toLowerCase()} title`}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Prompt
                    </label>
                    <textarea
                      value={generateForm.prompt}
                      onChange={(e) => setGenerateForm(prev => ({ ...prev, prompt: e.target.value }))}
                      placeholder={`Describe what you want in your ${getDocumentTypeName(generateForm.type).toLowerCase()}`}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      rows={4}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Template
                    </label>
                    <select
                      value={generateForm.template}
                      onChange={(e) => setGenerateForm(prev => ({ ...prev, template: e.target.value }))}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="standard">Standard</option>
                      <option value="professional">Professional</option>
                      <option value="creative">Creative</option>
                      <option value="minimal">Minimal</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Additional Context (Optional)
                    </label>
                    <textarea
                      value={generateForm.additionalContext}
                      onChange={(e) => setGenerateForm(prev => ({ ...prev, additionalContext: e.target.value }))}
                      placeholder="Add any additional details or context"
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      rows={2}
                    />
                  </div>
                </div>

                <div className="mt-6 flex gap-3">
                  <button
                    onClick={handleGenerateDocument}
                    disabled={isLoading || !generateForm.title || !generateForm.prompt}
                    className="flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-500 text-white rounded-lg font-medium transition-colors disabled:cursor-not-allowed"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="w-5 h-5 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Plus className="w-5 h-5" />
                        Generate {getDocumentTypeName(generateForm.type)}
                      </>
                    )}
                  </button>
                  <button
                    onClick={() => setShowGenerateModal(false)}
                    className="px-4 py-3 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </motion.div>
          </div>
        )}
      </AnimatePresence>

      {/* Document Details Modal */}
      <AnimatePresence>
        {showDocumentModal && selectedDocument && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full"
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    {getDocumentTypeIcon(selectedDocument.type)}
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white">{selectedDocument.title}</h3>
                  </div>
                  <button 
                    onClick={() => setShowDocumentModal(false)}
                    className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>

                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Content Preview</h4>
                  <p className="text-gray-600 dark:text-gray-400 text-sm whitespace-pre-line">
                    {selectedDocument.content}
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Type</p>
                    <p className="font-medium text-gray-900 dark:text-white capitalize">{selectedDocument.type}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Created</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {new Date(selectedDocument.createdAt).toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Status</p>
                    <div className="flex items-center gap-2">
                      {selectedDocument.status === 'generated' ? (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      ) : selectedDocument.status === 'error' ? (
                        <AlertCircle className="w-4 h-4 text-red-500" />
                      ) : (
                        <Loader2 className="w-4 h-4 text-indigo-500 animate-spin" />
                      )}
                      <span className="font-medium text-gray-900 dark:text-white capitalize">
                        {selectedDocument.status}
                      </span>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Last Updated</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {new Date(selectedDocument.updatedAt).toLocaleString()}
                    </p>
                  </div>
                </div>

                <div className="flex gap-3">
                  {selectedDocument.downloadUrl && (
                    <a
                      href={selectedDocument.downloadUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white rounded-lg font-medium transition-colors"
                    >
                      <Download className="w-5 h-5" />
                      Download
                    </a>
                  )}
                  {selectedDocument.previewUrl && (
                    <a
                      href={selectedDocument.previewUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium transition-colors"
                    >
                      <ExternalLink className="w-5 h-5" />
                      Preview
                    </a>
                  )}
                </div>
              </div>
            </motion.div>
          </div>
        )}
      </AnimatePresence>
    </div>
  );
};