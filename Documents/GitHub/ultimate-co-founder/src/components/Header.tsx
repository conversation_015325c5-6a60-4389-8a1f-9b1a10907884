import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Zap, ArrowLeft, User, LogOut } from 'lucide-react';
import { useAuth } from './AuthProvider';
import { ThemeToggle } from './ThemeToggle';

export const Header: React.FC = () => {
  const { user, logout, isAuthenticated } = useAuth();
  const location = useLocation();

  // Get the current page title based on the path
  const getPageTitle = () => {
    const path = location.pathname;
    if (path.includes('/dashboard')) return 'Dashboard';
    if (path.includes('/ai-cofounders')) return 'AI Co-founders';
    if (path.includes('/data-room')) return 'Data Room';
    if (path.includes('/integrations')) return 'Integrations';
    if (path.includes('/pricing')) return 'Pricing';
    if (path.includes('/resources')) return 'Resources';
    if (path.includes('/company')) return 'Company';
    if (path.includes('/about')) return 'About';
    if (path.includes('/contact')) return 'Contact';
    if (path.includes('/careers')) return 'Careers';
    if (path.includes('/settings')) return 'Settings';
    if (path.includes('/documents')) return 'Documents';
    if (path.includes('/health-check')) return 'System Health Check';
    return '';
  };

  const pageTitle = getPageTitle();

  return (
    <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            <Link to="/" className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                <Zap className="w-5 h-5 text-white" />
              </div>
              <span className="font-bold text-gray-900 dark:text-white">AI Co-Founder</span>
            </Link>
            {pageTitle && (
              <>
                <span className="mx-4 text-gray-300 dark:text-gray-600">|</span>
                <h1 className="text-xl font-bold text-gray-900 dark:text-white">{pageTitle}</h1>
              </>
            )}
          </div>
          
          <div className="flex items-center gap-4">
            <div className="hidden md:flex items-center gap-8">
              <Link to="/dashboard" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">AI Co-founders</Link>
              <Link to="/data-room" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">Data Room</Link>
              <Link to="/integrations" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">Integrations</Link>
              <Link to="/pricing" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">Pricing</Link>
              <Link to="/documents" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">Documents</Link>
            </div>
            
            <ThemeToggle />
            
            {isAuthenticated ? (
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                  <User className="w-4 h-4" />
                  {user?.name}
                </div>
                <Link to="/dashboard" className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                  Dashboard
                </Link>
                <button
                  onClick={logout}
                  className="p-2 text-gray-600 hover:text-gray-900 transition-colors"
                  title="Logout"
                >
                  <LogOut className="w-4 h-4" />
                </button>
              </div>
            ) : (
              <button 
                onClick={() => window.location.href = '/auth'}
                className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
              >
                Sign In
              </button>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};