import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Play, Video, Mic, Monitor, Users, CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { useAIOrchestrator } from '../hooks/useAIOrchestrator';
import { liveKit } from '../services/livekit';
import toast from 'react-hot-toast';

export const LiveKitTest: React.FC = () => {
  const { startLiveKitSession } = useAIOrchestrator();
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});
  const [currentSession, setCurrentSession] = useState<any>(null);

  const runTest = async (testName: string, testFn: () => Promise<boolean>) => {
    setIsLoading(true);
    try {
      console.log(`🧪 Running test: ${testName}`);
      const result = await testFn();
      setTestResults(prev => ({ ...prev, [testName]: result }));
      
      if (result) {
        toast.success(`✅ ${testName} passed`);
      } else {
        toast.error(`❌ ${testName} failed`);
      }
      
      return result;
    } catch (error) {
      console.error(`❌ Test ${testName} failed:`, error);
      setTestResults(prev => ({ ...prev, [testName]: false }));
      toast.error(`❌ ${testName} failed: ${error}`);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const testSessionCreation = async (): Promise<boolean> => {
    try {
      const session = await liveKit.createSession(['strategic', 'product'], 'video');
      console.log('✅ Session created:', session);
      setCurrentSession(session);
      return !!session && !!session.id;
    } catch (error) {
      console.error('❌ Session creation failed:', error);
      return false;
    }
  };

  const testOrchestratorIntegration = async (): Promise<boolean> => {
    try {
      const session = await startLiveKitSession(['strategic'], 'video');
      console.log('✅ Orchestrator session created:', session);
      return !!session && !!session.id;
    } catch (error) {
      console.error('❌ Orchestrator integration failed:', error);
      return false;
    }
  };

  const testRoomConnection = async (): Promise<boolean> => {
    if (!currentSession) {
      console.log('⚠️ No session available for room connection test');
      return false;
    }
    
    try {
      const room = await liveKit.connectToRoom(currentSession);
      console.log('✅ Room connection successful:', room);
      return !!room;
    } catch (error) {
      console.error('❌ Room connection failed:', error);
      return false;
    }
  };

  const testVideoSession = async (): Promise<boolean> => {
    try {
      const session = await startLiveKitSession(['strategic', 'product'], 'video');
      console.log('✅ Video session started:', session);
      return !!session;
    } catch (error) {
      console.error('❌ Video session failed:', error);
      return false;
    }
  };

  const testVoiceSession = async (): Promise<boolean> => {
    try {
      const session = await startLiveKitSession(['technical'], 'voice');
      console.log('✅ Voice session started:', session);
      return !!session;
    } catch (error) {
      console.error('❌ Voice session failed:', error);
      return false;
    }
  };

  const testScreenShare = async (): Promise<boolean> => {
    try {
      const session = await startLiveKitSession(['operations'], 'screen-share');
      console.log('✅ Screen share session started:', session);
      return !!session;
    } catch (error) {
      console.error('❌ Screen share failed:', error);
      return false;
    }
  };

  const runAllTests = async () => {
    console.log('🚀 Running all LiveKit tests...');
    
    await runTest('Session Creation', testSessionCreation);
    await runTest('Orchestrator Integration', testOrchestratorIntegration);
    await runTest('Room Connection', testRoomConnection);
    await runTest('Video Session', testVideoSession);
    await runTest('Voice Session', testVoiceSession);
    await runTest('Screen Share', testScreenShare);
    
    console.log('✅ All tests completed!');
  };

  const getTestIcon = (testName: string) => {
    if (!(testName in testResults)) return <Loader2 className="w-4 h-4 text-gray-400" />;
    return testResults[testName] ? 
      <CheckCircle className="w-4 h-4 text-green-500" /> : 
      <XCircle className="w-4 h-4 text-red-500" />;
  };

  const tests = [
    { name: 'Session Creation', icon: <Users className="w-4 h-4" /> },
    { name: 'Orchestrator Integration', icon: <Play className="w-4 h-4" /> },
    { name: 'Room Connection', icon: <Video className="w-4 h-4" /> },
    { name: 'Video Session', icon: <Video className="w-4 h-4" /> },
    { name: 'Voice Session', icon: <Mic className="w-4 h-4" /> },
    { name: 'Screen Share', icon: <Monitor className="w-4 h-4" /> },
  ];

  return (
    <div className="max-w-4xl mx-auto p-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8"
      >
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            LiveKit Integration Test
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Test the LiveKit functionality to ensure live sessions work correctly
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
          {tests.map((test) => (
            <div
              key={test.name}
              className="flex items-center gap-3 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
            >
              {test.icon}
              <span className="flex-1 text-gray-900 dark:text-white">{test.name}</span>
              {getTestIcon(test.name)}
            </div>
          ))}
        </div>

        <div className="flex gap-4 justify-center">
          <button
            onClick={runAllTests}
            disabled={isLoading}
            className="flex items-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors"
          >
            {isLoading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Play className="w-4 h-4" />
            )}
            Run All Tests
          </button>
        </div>

        {currentSession && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            className="mt-8 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg"
          >
            <h3 className="font-medium text-green-800 dark:text-green-200 mb-2">
              Current Session
            </h3>
            <div className="text-sm text-green-700 dark:text-green-300">
              <p>ID: {currentSession.id}</p>
              <p>Room: {currentSession.room_name}</p>
              <p>Type: {currentSession.session_type}</p>
              <p>Status: {currentSession.status}</p>
              <p>Participants: {currentSession.participants?.length || 0}</p>
            </div>
          </motion.div>
        )}
      </motion.div>
    </div>
  );
};
