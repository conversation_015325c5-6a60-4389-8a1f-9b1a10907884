import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Video, 
  VideoOff, 
  Mic, 
  MicOff, 
  Phone, 
  PhoneOff, 
  Monitor,
  MonitorOff,
  Users,
  MessageSquare,
  Volume2,
  VolumeX
} from 'lucide-react';
import { liveKit, LiveKitSession, LiveKitMessage } from '../services/livekit';
import { tavus, TavusAvatar } from '../services/tavus';
import toast from 'react-hot-toast';
import { TavusAvatarView } from './TavusAvatarView';

// Agent-specific background images
const agentBackgrounds = {
  strategic: 'https://images.pexels.com/photos/3184338/pexels-photo-3184338.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
  product: 'https://images.pexels.com/photos/3182812/pexels-photo-3182812.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
  technical: 'https://images.pexels.com/photos/3183150/pexels-photo-3183150.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
  operations: 'https://images.pexels.com/photos/3184292/pexels-photo-3184292.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
  marketing: 'https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
};

interface LiveKitRoomProps {
  session: LiveKitSession;
  onEnd: () => void;
}

export const LiveKitRoom: React.FC<LiveKitRoomProps> = ({ session, onEnd }) => {
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [messages, setMessages] = useState<LiveKitMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [agentAvatars, setAgentAvatars] = useState<Record<string, TavusAvatar>>({});
  const [isAudioMuted, setIsAudioMuted] = useState(true);
  const [activeAgentId, setActiveAgentId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasPermissions, setHasPermissions] = useState<boolean | null>(null);
  const [showChat, setShowChat] = useState(false);

  useEffect(() => {
    const handleMessage = (message: LiveKitMessage) => {
      setMessages(prev => [...prev, message]);
      
      // If message is from an agent, set that agent as active
      if (message.participant_id !== 'user' && message.participant_id !== 'system') {
        setActiveAgentId(message.participant_id);
      }
    };

    liveKit.onMessage(handleMessage);

    // Check device permissions
    checkDevicePermissions();

    // Load agent avatars
    loadAgentAvatars();

    return () => {
      liveKit.offMessage(handleMessage);
    };
  }, []);

  const checkDevicePermissions = async () => {
    try {
      // Request camera permission
      const cameraStream = await navigator.mediaDevices.getUserMedia({ video: true });
      
      // Request microphone permission
      const micStream = await navigator.mediaDevices.getUserMedia({ audio: true });
      
      // Stop the streams immediately after getting permissions
      cameraStream.getTracks().forEach(track => track.stop());
      micStream.getTracks().forEach(track => track.stop());
      
      setHasPermissions(true);
    } catch (error) {
      console.error('Error checking device permissions:', error);
      setHasPermissions(false);
    }
  };

  const loadAgentAvatars = async () => {
    setIsLoading(true);
    try {
      const avatars: Record<string, TavusAvatar> = {};
      
      // Load avatars for all agents in the session
      for (const participant of session.participants) {
        if (participant.role === 'agent') {
          const avatar = await tavus.getAgentAvatar(participant.id);
          avatars[participant.id] = avatar;
        }
      }
      
      setAgentAvatars(avatars);
      
      // Set the first agent as active by default
      if (Object.keys(avatars).length > 0 && !activeAgentId) {
        setActiveAgentId(Object.keys(avatars)[0]);
      }
    } catch (error) {
      console.error('Error loading agent avatars:', error);
      toast.error('Failed to load agent avatars');
    } finally {
      setIsLoading(false);
    }
  };

  const toggleVideo = async () => {
    try {
      if (isVideoEnabled) {
        await liveKit.disableCamera();
      } else {
        await liveKit.enableCamera();
      }
      setIsVideoEnabled(!isVideoEnabled);
    } catch (error) {
      console.error('Error toggling video:', error);
      toast.error('Failed to toggle video');
    }
  };

  const toggleAudio = async () => {
    try {
      if (isAudioEnabled) {
        await liveKit.disableMicrophone();
      } else {
        await liveKit.enableMicrophone();
      }
      setIsAudioEnabled(!isAudioEnabled);
    } catch (error) {
      console.error('Error toggling audio:', error);
      toast.error('Failed to toggle audio');
    }
  };

  const toggleScreenShare = async () => {
    try {
      if (isScreenSharing) {
        await liveKit.stopScreenShare();
      } else {
        await liveKit.startScreenShare();
      }
      setIsScreenSharing(!isScreenSharing);
    } catch (error) {
      console.error('Error toggling screen share:', error);
      toast.error('Failed to toggle screen share');
    }
  };

  const toggleAudioMute = () => {
    setIsAudioMuted(!isAudioMuted);
  };

  const sendMessage = async () => {
    if (!newMessage.trim()) return;

    try {
      await liveKit.sendMessage(newMessage);
      setNewMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
    }
  };

  const handleEndSession = async () => {
    try {
      await liveKit.endSession(session.id);
      onEnd();
      toast.success('Session ended successfully');
    } catch (error) {
      console.error('Error ending session:', error);
      toast.error('Failed to end session');
    }
  };

  const getActiveAgentAvatar = () => {
    if (!activeAgentId || !agentAvatars[activeAgentId]) {
      // Return the first agent if no active agent
      const firstAgentId = Object.keys(agentAvatars)[0];
      return firstAgentId ? agentAvatars[firstAgentId] : null;
    }
    return agentAvatars[activeAgentId];
  };

  const activeAvatar = getActiveAgentAvatar();
  const activeAgentBackground = activeAgentId ? agentBackgrounds[activeAgentId as keyof typeof agentBackgrounds] : null;

  // If permissions are denied, show the permissions modal
  if (hasPermissions === false) {
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full p-6">
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
              <VideoOff className="w-8 h-8 text-red-600 dark:text-red-400" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
              Camera and Microphone Access Required
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Please allow access to your camera and microphone to start a video call with your AI co-founders.
            </p>
          </div>
          <div className="space-y-4">
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">How to enable permissions:</h4>
              <ol className="list-decimal list-inside space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <li>Click the camera/lock icon in your browser's address bar</li>
                <li>Select "Allow" for both camera and microphone</li>
                <li>Refresh the page and try again</li>
              </ol>
            </div>
            <div className="flex gap-3">
              <button
                onClick={onEnd}
                className="flex-1 px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={async () => {
                  await checkDevicePermissions();
                }}
                className="flex-1 px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-gradient-to-br from-gray-900 to-gray-800 z-50 flex flex-col"
    >
      {/* Header */}
      <div className="bg-black/30 backdrop-blur-sm p-4 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
            <Zap className="w-5 h-5 text-white" />
          </div>
          <h2 className="text-white text-lg font-semibold">Ultimate Co-founder Live Session</h2>
          <div className="flex items-center gap-2 text-green-400">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
            <span className="text-sm">Live</span>
          </div>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2 text-white">
            <Users size={16} />
            <span className="text-sm">{session.participants.length} participants</span>
          </div>
          <button
            onClick={() => setShowChat(!showChat)}
            className={`p-2 rounded-full transition-colors ${
              showChat ? 'bg-purple-600 text-white' : 'bg-gray-700/50 text-white hover:bg-gray-600/50'
            }`}
          >
            <MessageSquare size={20} />
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col md:flex-row">
        {/* Video Area */}
        <div className={`flex-1 p-4 flex flex-col ${showChat ? 'hidden md:flex' : ''}`}>
          {isLoading ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center text-white">
                <div className="w-10 h-10 border-4 border-white border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p>Loading avatars...</p>
              </div>
            </div>
          ) : (
            <div className="flex-1 flex flex-col">
              {/* Main Video - Active Agent */}
              <div className="flex-1 relative mb-4 rounded-xl overflow-hidden shadow-2xl">
                {activeAvatar ? (
                  <div className="w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex flex-col items-center justify-center">
                    {/* Background image for visual interest - use agent-specific background */}
                    <div className="absolute inset-0 opacity-10">
                      <img 
                        src={activeAgentBackground || "https://images.pexels.com/photos/3861958/pexels-photo-3861958.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"} 
                        alt="Background" 
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.currentTarget.src = `https://placehold.co/1200x800/1f2937/ffffff?text=Video+Call`;
                        }}
                      />
                    </div>
                    
                    {/* Use TavusAvatarView for the active agent */}
                    <div className="relative w-full max-w-2xl aspect-video z-10">
                      <TavusAvatarView 
                        avatar={activeAvatar}
                        muted={isAudioMuted}
                        onMuteChange={setIsAudioMuted}
                        className="w-full h-full"
                      />
                    </div>
                  </div>
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center">
                    <div className="text-center text-white">
                      <Users className="w-16 h-16 mx-auto mb-4 opacity-50" />
                      <p className="text-lg">No active agent</p>
                      <p className="text-sm opacity-75">Send a message to start the conversation</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Participant Thumbnails */}
              <div className="h-24 flex gap-2 overflow-x-auto pb-2">
                {/* User thumbnail */}
                <div className="relative h-full aspect-video bg-gray-800 rounded-lg flex-shrink-0 shadow-md">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-lg font-bold">U</span>
                    </div>
                  </div>
                  <div className="absolute bottom-2 left-2 bg-black/50 text-white px-2 py-0.5 rounded text-xs">
                    You
                  </div>
                  <div className="absolute top-2 right-2 flex gap-1">
                    {!isVideoEnabled && <VideoOff className="w-3 h-3 text-red-400" />}
                    {!isAudioEnabled && <MicOff className="w-3 h-3 text-red-400" />}
                  </div>
                </div>

                {/* Agent thumbnails */}
                {Object.entries(agentAvatars).map(([agentId, avatar]) => {
                  // Get agent-specific background
                  const agentBackground = agentBackgrounds[agentId as keyof typeof agentBackgrounds];
                  
                  return (
                    <div 
                      key={agentId}
                      className={`relative h-full aspect-video rounded-lg flex-shrink-0 cursor-pointer shadow-md ${
                        activeAgentId === agentId ? 'ring-2 ring-purple-500' : ''
                      }`}
                      onClick={() => setActiveAgentId(agentId)}
                    >
                      <img 
                        src={agentBackground || avatar.thumbnailUrl} 
                        alt={avatar.name}
                        className="w-full h-full object-cover rounded-lg"
                        onError={(e) => {
                          console.error('Failed to load thumbnail:', avatar.thumbnailUrl);
                          e.currentTarget.src = `https://placehold.co/150x150/1f2937/ffffff?text=${avatar.name.charAt(0)}`;
                        }}
                      />
                      <div className="absolute bottom-2 left-2 bg-black/50 text-white px-2 py-0.5 rounded text-xs">
                        {avatar.name.split(' ')[0]}
                      </div>
                      <div className="absolute top-2 right-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {/* Chat Sidebar */}
        <div className={`w-full md:w-80 bg-black/30 backdrop-blur-sm border-t md:border-t-0 md:border-l border-white/10 flex flex-col ${showChat ? '' : 'hidden md:flex'}`}>
          <div className="p-4 border-b border-white/10">
            <h3 className="text-white font-medium flex items-center gap-2">
              <MessageSquare size={16} />
              Chat
            </h3>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-3">
            {messages.map((message) => (
              <div key={message.id} className="text-sm">
                <div className="text-gray-400 text-xs mb-1">
                  {message.participant_id === 'user' ? 'You' : 
                   message.participant_id === 'system' ? 'System' : 
                   agentAvatars[message.participant_id]?.name || message.participant_id} • {message.timestamp.toLocaleTimeString()}
                </div>
                <div className="text-white">{message.content}</div>
              </div>
            ))}
          </div>

          {/* Message Input */}
          <div className="p-4 border-t border-white/10">
            <div className="flex gap-2">
              <input
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                placeholder="Type a message..."
                className="flex-1 bg-white/10 text-white px-3 py-2 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 placeholder-white/50"
              />
              <button
                onClick={sendMessage}
                className="px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
              >
                Send
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="bg-black/30 backdrop-blur-sm p-4 flex items-center justify-center gap-4">
        <button
          onClick={toggleAudio}
          className={`p-3 rounded-full transition-colors ${
            isAudioEnabled 
              ? 'bg-gray-700/50 hover:bg-gray-600/50 text-white' 
              : 'bg-red-500 hover:bg-red-600 text-white'
          }`}
        >
          {isAudioEnabled ? <Mic size={20} /> : <MicOff size={20} />}
        </button>

        <button
          onClick={toggleVideo}
          className={`p-3 rounded-full transition-colors ${
            isVideoEnabled 
              ? 'bg-gray-700/50 hover:bg-gray-600/50 text-white' 
              : 'bg-red-500 hover:bg-red-600 text-white'
          }`}
        >
          {isVideoEnabled ? <Video size={20} /> : <VideoOff size={20} />}
        </button>

        <button
          onClick={toggleScreenShare}
          className={`p-3 rounded-full transition-colors ${
            isScreenSharing 
              ? 'bg-blue-500 hover:bg-blue-600 text-white' 
              : 'bg-gray-700/50 hover:bg-gray-600/50 text-white'
          }`}
        >
          {isScreenSharing ? <MonitorOff size={20} /> : <Monitor size={20} />}
        </button>

        <button
          onClick={toggleAudioMute}
          className={`p-3 rounded-full transition-colors ${
            isAudioMuted
              ? 'bg-red-500 hover:bg-red-600 text-white'
              : 'bg-gray-700/50 hover:bg-gray-600/50 text-white'
          }`}
        >
          {isAudioMuted ? <VolumeX size={20} /> : <Volume2 size={20} />}
        </button>

        <button
          onClick={handleEndSession}
          className="px-6 py-3 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors flex items-center gap-2"
        >
          <PhoneOff size={16} />
          End Session
        </button>
      </div>
    </motion.div>
  );
};

// Import missing Zap component
const Zap = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon>
  </svg>
);