import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Agent } from '../types/agent';
import { MessageCircle, Video, Settings, TrendingUp } from 'lucide-react';
import { TavusAvatarView } from './TavusAvatarView';
import { tavus } from '../services/tavus';

interface AgentCardProps {
  agent: Agent;
  onSelect: (agent: Agent) => void;
  isSelected: boolean;
}

const statusColors = {
  active: 'bg-green-500',
  thinking: 'bg-yellow-500',
  collaborating: 'bg-blue-500',
  idle: 'bg-gray-400'
};

const statusLabels = {
  active: 'Active',
  thinking: 'Thinking',
  collaborating: 'Collaborating',
  idle: 'Idle'
};

// Agent-specific images
const agentImages = {
  strategic: 'https://images.pexels.com/photos/3184338/pexels-photo-3184338.jpeg?auto=compress&cs=tinysrgb&w=600',
  product: 'https://images.pexels.com/photos/3182812/pexels-photo-3182812.jpeg?auto=compress&cs=tinysrgb&w=600',
  technical: 'https://images.pexels.com/photos/3183150/pexels-photo-3183150.jpeg?auto=compress&cs=tinysrgb&w=600',
  operations: 'https://images.pexels.com/photos/3184292/pexels-photo-3184292.jpeg?auto=compress&cs=tinysrgb&w=600',
  marketing: 'https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg?auto=compress&cs=tinysrgb&w=600'
};

export const AgentCard: React.FC<AgentCardProps> = ({ agent, onSelect, isSelected }) => {
  const [avatar, setAvatar] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  useEffect(() => {
    loadAgentAvatar();
  }, []);

  const loadAgentAvatar = async () => {
    setIsLoading(true);
    try {
      const agentAvatar = await tavus.getAgentAvatar(agent.id);
      setAvatar(agentAvatar);
    } catch (error) {
      console.error(`Error loading avatar for agent ${agent.id}:`, error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className={`relative bg-white dark:bg-gray-800 rounded-xl p-6 cursor-pointer transition-all duration-300 ${
        isSelected 
          ? 'ring-2 ring-purple-500 shadow-lg shadow-purple-500/20' 
          : 'hover:shadow-lg hover:shadow-gray-200 dark:hover:shadow-gray-700/50'
      }`}
      onClick={() => onSelect(agent)}
    >
      {/* Status Indicator */}
      <div className="absolute top-4 right-4 flex items-center gap-2">
        <div className={`w-3 h-3 rounded-full ${statusColors[agent.status]} animate-pulse`} />
        <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
          {statusLabels[agent.status]}
        </span>
      </div>

      {/* Agent Avatar & Name */}
      <div className="flex items-center gap-4 mb-4">
        {isLoading ? (
          <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
            <div className="w-6 h-6 border-2 border-gray-300 dark:border-gray-600 border-t-transparent rounded-full animate-spin"></div>
          </div>
        ) : avatar && !imageError ? (
          <div className="w-12 h-12 rounded-full overflow-hidden">
            <img 
              src={avatar.thumbnailUrl} 
              alt={agent.name}
              className="w-full h-full object-cover"
              onError={handleImageError}
            />
          </div>
        ) : (
          <div className="w-12 h-12 rounded-full overflow-hidden">
            <img 
              src={agentImages[agent.id as keyof typeof agentImages] || `https://placehold.co/100x100/1f2937/ffffff?text=${agent.name.charAt(0)}`}
              alt={agent.name}
              className="w-full h-full object-cover"
              onError={(e) => {
                e.currentTarget.src = `https://placehold.co/100x100/1f2937/ffffff?text=${agent.name.charAt(0)}`;
              }}
            />
          </div>
        )}
        <div>
          <h3 className="font-semibold text-gray-900 dark:text-white">{agent.name}</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 capitalize">{agent.role}</p>
        </div>
      </div>

      {/* Description */}
      <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
        {agent.description}
      </p>

      {/* Current Task */}
      {agent.currentTask && (
        <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <p className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Current Task</p>
          <p className="text-sm text-gray-900 dark:text-white">{agent.currentTask}</p>
        </div>
      )}

      {/* Capabilities */}
      <div className="mb-4">
        <p className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Capabilities</p>
        <div className="flex flex-wrap gap-1">
          {agent.capabilities.slice(0, 3).map((capability, index) => (
            <span
              key={index}
              className="px-2 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 text-xs rounded-full"
            >
              {capability}
            </span>
          ))}
          {agent.capabilities.length > 3 && (
            <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded-full">
              +{agent.capabilities.length - 3}
            </span>
          )}
        </div>
      </div>

      {/* Metrics */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center">
          <p className="text-lg font-bold text-gray-900 dark:text-white">{agent.metrics.tasksCompleted}</p>
          <p className="text-xs text-gray-600 dark:text-gray-400">Tasks</p>
        </div>
        <div className="text-center">
          <p className="text-lg font-bold text-green-600">{agent.metrics.successRate}%</p>
          <p className="text-xs text-gray-600 dark:text-gray-400">Success</p>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-2">
        <button className="flex-1 flex items-center justify-center gap-2 px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded-lg transition-colors">
          <MessageCircle size={14} />
          Chat
        </button>
        <button className="px-3 py-2 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors">
          <Video size={14} />
        </button>
        <button className="px-3 py-2 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors">
          <Settings size={14} />
        </button>
      </div>
    </motion.div>
  );
};