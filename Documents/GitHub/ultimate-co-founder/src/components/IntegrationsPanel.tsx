import React from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON>lack, 
  Github, 
  FileText, 
  Zap, 
  CheckCircle, 
  AlertCircle,
  Settings,
  Plus,
  ExternalLink
} from 'lucide-react';

interface Integration {
  id: string;
  name: string;
  icon: React.ElementType;
  status: 'connected' | 'disconnected' | 'error';
  description: string;
  lastSync?: string;
}

export const IntegrationsPanel: React.FC = () => {
  const [integrations, setIntegrations] = React.useState<Integration[]>([
    {
      id: 'slack',
      name: 'Slack',
      icon: Slack,
      status: 'connected',
      description: 'Team communication and notifications',
      lastSync: '2 minutes ago'
    },
    {
      id: 'github',
      name: 'GitHub',
      icon: Github,
      status: 'connected',
      description: 'Code repository and version control',
      lastSync: '5 minutes ago'
    },
    {
      id: 'notion',
      name: 'Notion',
      icon: FileText,
      status: 'disconnected',
      description: 'Documentation and knowledge base'
    }
  ]);

  const toggleIntegration = (id: string) => {
    setIntegrations(prev => prev.map(integration => 
      integration.id === id 
        ? { 
            ...integration, 
            status: integration.status === 'connected' ? 'disconnected' : 'connected',
            lastSync: integration.status === 'disconnected' ? 'Just now' : undefined
          }
        : integration
    ));
  };

  const getStatusIcon = (status: Integration['status']) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: Integration['status']) => {
    switch (status) {
      case 'connected':
        return 'text-green-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-500';
    }
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="text-center">
        <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
          <Zap className="w-8 h-8 text-white" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Integrations
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Connect your favorite tools via Composio
        </p>
      </div>

      {/* Integration Status */}
      <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
        <div className="flex items-center gap-2">
          <CheckCircle className="w-5 h-5 text-green-600" />
          <span className="font-medium text-green-800 dark:text-green-200">
            {integrations.filter(i => i.status === 'connected').length} Connected
          </span>
        </div>
        <button className="text-green-600 hover:text-green-700 text-sm font-medium">
          View All
        </button>
      </div>

      {/* Integrations List */}
      <div className="space-y-3">
        {integrations.map((integration) => (
          <motion.div
            key={integration.id}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
          >
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-white dark:bg-gray-800 rounded-lg flex items-center justify-center">
                  <integration.icon size={16} className="text-gray-700 dark:text-gray-300" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    {integration.name}
                  </h4>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {integration.description}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                {getStatusIcon(integration.status)}
                <button
                  onClick={() => toggleIntegration(integration.id)}
                  className={`px-3 py-1 text-xs font-medium rounded-full transition-colors ${
                    integration.status === 'connected'
                      ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 hover:bg-green-200 dark:hover:bg-green-900/50'
                      : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500'
                  }`}
                >
                  {integration.status === 'connected' ? 'Connected' : 'Connect'}
                </button>
              </div>
            </div>

            {integration.lastSync && (
              <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                <span>Last sync: {integration.lastSync}</span>
                <button className="hover:text-gray-700 dark:hover:text-gray-300">
                  <Settings size={12} />
                </button>
              </div>
            )}
          </motion.div>
        ))}
      </div>

      {/* Add Integration */}
      <button className="w-full flex items-center justify-center gap-2 p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-purple-400 dark:hover:border-purple-500 text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 rounded-lg transition-colors">
        <Plus size={16} />
        Add New Integration
      </button>

      {/* Integration Actions */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
        <h4 className="font-medium text-gray-900 dark:text-white mb-3">Quick Actions</h4>
        <div className="space-y-2">
          {[
            { name: 'Sync All Data', desc: 'Update from all connected services' },
            { name: 'Export Connections', desc: 'Download integration settings' },
            { name: 'Webhook Settings', desc: 'Configure real-time updates' }
          ].map((action) => (
            <button
              key={action.name}
              className="w-full flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors group"
            >
              <div className="text-left">
                <p className="font-medium text-gray-900 dark:text-white text-sm">
                  {action.name}
                </p>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  {action.desc}
                </p>
              </div>
              <ExternalLink size={14} className="text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300" />
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};