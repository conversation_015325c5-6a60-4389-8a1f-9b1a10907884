import React, { useState, useEffect, useRef } from 'react';
import { TavusAvatar } from '../services/tavus';
import { Volume2, VolumeX, User } from 'lucide-react';

interface TavusAvatarViewProps {
  avatar: TavusAvatar;
  autoplay?: boolean;
  muted?: boolean;
  onMuteChange?: (muted: boolean) => void;
  className?: string;
  controls?: boolean;
}

export const TavusAvatarView: React.FC<TavusAvatarViewProps> = ({
  avatar,
  autoplay = true,
  muted = true,
  onMuteChange,
  className = '',
  controls = true
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isMuted, setIsMuted] = useState(muted);
  const [imageError, setImageError] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    setIsLoading(true);
    setHasError(false);
    setImageError(false);
  }, [avatar.videoUrl, avatar.thumbnailUrl]);

  useEffect(() => {
    setIsMuted(muted);
    if (videoRef.current) {
      videoRef.current.muted = muted;
    }
  }, [muted]);

  const handleVideoLoadedData = () => {
    setIsLoading(false);
    setHasError(false);
  };

  const handleVideoError = (e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    console.error('Failed to load video:', avatar.videoUrl);
    setHasError(true);
    setIsLoading(false);
  };

  const handleImageError = () => {
    console.error('Failed to load image:', avatar.thumbnailUrl);
    setImageError(true);
  };

  const toggleMute = () => {
    const newMutedState = !isMuted;
    setIsMuted(newMutedState);
    if (videoRef.current) {
      videoRef.current.muted = newMutedState;
    }
    if (onMuteChange) {
      onMuteChange(newMutedState);
    }
  };

  // Generate a fallback image URL using a more reliable service
  const getFallbackImageUrl = () => {
    // Use a reliable placeholder service
    return `https://placehold.co/400x300/1f2937/ffffff?text=${encodeURIComponent(avatar.name)}`;
  };

  // Get agent-specific background based on role
  const getAgentBackground = () => {
    const roleToBackground: Record<string, string> = {
      'Strategic Co-founder': 'https://images.pexels.com/photos/3184338/pexels-photo-3184338.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      'Product Co-founder': 'https://images.pexels.com/photos/3182812/pexels-photo-3182812.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      'Technical Co-founder': 'https://images.pexels.com/photos/3183150/pexels-photo-3183150.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      'Operations Co-founder': 'https://images.pexels.com/photos/3184292/pexels-photo-3184292.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      'Marketing Co-founder': 'https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
    };
    
    return roleToBackground[avatar.role] || 'https://images.pexels.com/photos/3861958/pexels-photo-3861958.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1';
  };

  return (
    <div className={`relative ${className}`}>
      {isLoading && !hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-900 rounded-lg">
          <div className="w-10 h-10 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
      
      <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-900 rounded-lg">
        {imageError ? (
          <div className="w-full h-full bg-gray-800 flex items-center justify-center">
            <div className="w-20 h-20 bg-gray-700 rounded-full flex items-center justify-center">
              <User className="w-10 h-10 text-gray-400" />
            </div>
          </div>
        ) : (
          <img 
            src={getAgentBackground()} 
            alt={avatar.name}
            className="w-full h-full object-cover rounded-lg opacity-20"
            onError={handleImageError}
          />
        )}
        <video
          ref={videoRef}
          className="hidden"
          autoPlay={autoplay}
          loop
          muted={isMuted}
          playsInline
          onLoadedData={handleVideoLoadedData}
          onError={handleVideoError}
        >
          <source src={avatar.videoUrl} type="video/mp4" />
          Your browser does not support the video tag.
        </video>
      </div>
      
      {controls && (
        <div className="absolute bottom-2 right-2">
          <button
            onClick={toggleMute}
            className="p-1.5 bg-black/50 hover:bg-black/70 rounded-full text-white transition-colors"
          >
            {isMuted ? <VolumeX size={16} /> : <Volume2 size={16} />}
          </button>
        </div>
      )}
      
      {/* Agent info overlay */}
      <div className="absolute bottom-4 left-4 bg-black/50 text-white px-3 py-1 rounded-lg text-sm">
        {avatar.name} • {avatar.role}
      </div>
    </div>
  );
};