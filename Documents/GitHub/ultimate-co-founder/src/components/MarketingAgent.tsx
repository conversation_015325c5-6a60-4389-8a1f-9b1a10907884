import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Megaphone, 
  Users, 
  Target, 
  BarChart3,
  Download,
  FileText,
  Search,
  Filter,
  Plus,
  Loader2,
  CheckCircle,
  X,
  Calendar,
  Mail,
  Building,
  Briefcase,
  MapPin,
  Tag,
  UserPlus,
  RefreshCw,
  ArrowRight,
  FileSpreadsheet
} from 'lucide-react';
import { apollo, ApolloLead, LeadSearchParams, LeadCollection } from '../services/apollo';
import toast from 'react-hot-toast';
import { CSVLink } from 'react-csv';

interface MarketingStrategy {
  brandPositioning: {
    tagline: string;
    valueProposition: string;
    targetAudience: string[];
    brandVoice: string;
    keyMessages: string[];
  };
  contentCalendar: Array<{
    week: number;
    topic: string;
    channel: string;
    format: string;
    goal: string;
  }>;
  growthTactics: Array<{
    name: string;
    description: string;
    budget: string;
    expectedROI: string;
    timeline: string;
  }>;
  metrics: {
    acquisition: {
      target: string;
      channels: string[];
    };
    activation: {
      target: string;
      strategies: string[];
    };
    retention: {
      target: string;
      strategies: string[];
    };
    revenue: {
      target: string;
      strategies: string[];
    };
    referral: {
      target: string;
      strategies: string[];
    };
  };
}

export const MarketingAgent: React.FC = () => {
  const [currentView, setCurrentView] = useState<'strategy' | 'leads'>('strategy');
  const [marketingStrategy, setMarketingStrategy] = useState<MarketingStrategy | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isGeneratingStrategy, setIsGeneratingStrategy] = useState(false);
  
  // Apollo.ai lead generation state
  const [searchParams, setSearchParams] = useState<LeadSearchParams>({
    industry: '',
    title: '',
    keywords: [],
    limit: 20
  });
  const [searchResults, setSearchResults] = useState<ApolloLead[]>([]);
  const [selectedLeads, setSelectedLeads] = useState<Set<string>>(new Set());
  const [isSearching, setIsSearching] = useState(false);
  const [leadCollections, setLeadCollections] = useState<LeadCollection[]>([]);
  const [showCreateCollectionModal, setShowCreateCollectionModal] = useState(false);
  const [newCollectionName, setNewCollectionName] = useState('');
  const [newCollectionDescription, setNewCollectionDescription] = useState('');
  const [isCreatingCollection, setIsCreatingCollection] = useState(false);
  const [selectedCollection, setSelectedCollection] = useState<LeadCollection | null>(null);
  const [collectionLeads, setCollectionLeads] = useState<ApolloLead[]>([]);
  const [isLoadingCollectionLeads, setIsLoadingCollectionLeads] = useState(false);
  const [newKeyword, setNewKeyword] = useState('');
  const [isExporting, setIsExporting] = useState(false);

  useEffect(() => {
    generateMarketingStrategy();
    loadLeadCollections();
  }, []);

  const generateMarketingStrategy = async () => {
    setIsGeneratingStrategy(true);
    
    try {
      // In a real implementation, this would call an API to generate the strategy
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const mockStrategy: MarketingStrategy = {
        brandPositioning: {
          tagline: "Your AI Co-founder Team, Ready in Minutes",
          valueProposition: "The first comprehensive AI co-founder platform that provides strategic, product, technical, operations, and marketing expertise through specialized AI agents with real-time video/voice collaboration.",
          targetAudience: [
            "Early-stage startup founders",
            "Solo entrepreneurs",
            "Small business owners pivoting to tech",
            "Non-technical founders needing technical guidance",
            "Technical founders needing business expertise"
          ],
          brandVoice: "Knowledgeable, supportive, and empowering. We speak with authority but remain approachable and jargon-free.",
          keyMessages: [
            "Five specialized AI co-founders in one platform",
            "Real-time video/voice collaboration with AI experts",
            "From idea to execution in record time",
            "Expert guidance without the cost of human consultants",
            "Comprehensive support across all business domains"
          ]
        },
        contentCalendar: [
          {
            week: 1,
            topic: "Launch announcement",
            channel: "Twitter, LinkedIn",
            format: "Blog post, social media",
            goal: "Awareness"
          },
          {
            week: 2,
            topic: "AI Co-founder Demo Video",
            channel: "YouTube, Blog",
            format: "Video tutorial",
            goal: "Consideration"
          },
          {
            week: 3,
            topic: "Startup Success Stories",
            channel: "Newsletter, LinkedIn",
            format: "Case study",
            goal: "Conversion"
          },
          {
            week: 4,
            topic: "Technical Deep Dive",
            channel: "Blog, Twitter",
            format: "Technical article",
            goal: "Consideration"
          },
          {
            week: 5,
            topic: "Product Hunt Launch",
            channel: "All Channels",
            format: "Launch campaign",
            goal: "Acquisition"
          },
          {
            week: 6,
            topic: "Founder Interview Series",
            channel: "YouTube, Podcast",
            format: "Interview",
            goal: "Awareness"
          }
        ],
        growthTactics: [
          {
            name: "AI Startup Accelerator Partnerships",
            description: "Partner with Y Combinator, Techstars for exclusive access",
            budget: "$2,500",
            expectedROI: "500 qualified leads, 50 conversions",
            timeline: "Month 1-2"
          },
          {
            name: "Viral LinkedIn Challenge",
            description: "#AICofounderChallenge with startup pitch videos",
            budget: "$500",
            expectedROI: "10K impressions, 200 signups",
            timeline: "Month 2"
          },
          {
            name: "Product Hunt Launch Strategy",
            description: "Coordinated launch with influencer network",
            budget: "$1,500",
            expectedROI: "Top 5 daily ranking, 1K signups",
            timeline: "Month 2"
          },
          {
            name: "Startup Podcast Tour",
            description: "20 podcast appearances over 3 months",
            budget: "$800",
            expectedROI: "5K qualified leads, 100 conversions",
            timeline: "Month 1-3"
          }
        ],
        metrics: {
          acquisition: {
            target: "1,000 new users in first 3 months",
            channels: ["Content marketing", "Partnerships", "Product Hunt", "Referrals"]
          },
          activation: {
            target: "70% of users complete first AI co-founder session",
            strategies: ["Guided onboarding", "Template projects", "Interactive tutorials"]
          },
          retention: {
            target: "60% monthly active user retention",
            strategies: ["Weekly insights email", "New agent capabilities", "Community building"]
          },
          revenue: {
            target: "$50K MRR by month 6",
            strategies: ["Freemium to paid conversion", "Enterprise upselling", "Usage-based billing"]
          },
          referral: {
            target: "20% of new users from referrals",
            strategies: ["Referral program", "Shareable outputs", "Co-founder team invites"]
          }
        }
      };
      
      setMarketingStrategy(mockStrategy);
    } catch (error) {
      console.error('Error generating marketing strategy:', error);
      toast.error('Failed to generate marketing strategy');
    } finally {
      setIsGeneratingStrategy(false);
    }
  };

  const loadLeadCollections = async () => {
    setIsLoading(true);
    try {
      const collections = await apollo.getLeadCollections();
      setLeadCollections(collections);
    } catch (error) {
      console.error('Error loading lead collections:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = async () => {
    setIsSearching(true);
    try {
      const leads = await apollo.searchLeads(searchParams);
      setSearchResults(leads);
      toast.success(`Found ${leads.length} leads matching your criteria`);
    } catch (error) {
      console.error('Error searching leads:', error);
      toast.error('Failed to search leads');
    } finally {
      setIsSearching(false);
    }
  };

  const handleCreateCollection = async () => {
    if (!newCollectionName) {
      toast.error('Collection name is required');
      return;
    }
    
    setIsCreatingCollection(true);
    try {
      const collection = await apollo.createLeadCollection(
        newCollectionName,
        newCollectionDescription
      );
      
      setLeadCollections(prev => [...prev, collection]);
      setShowCreateCollectionModal(false);
      setNewCollectionName('');
      setNewCollectionDescription('');
      
      toast.success('Lead collection created successfully');
      
      // If we have selected leads, add them to the new collection
      if (selectedLeads.size > 0) {
        await apollo.addLeadsToCollection(
          collection.id,
          Array.from(selectedLeads)
        );
        toast.success(`Added ${selectedLeads.size} leads to collection`);
      }
    } catch (error) {
      console.error('Error creating lead collection:', error);
      toast.error('Failed to create lead collection');
    } finally {
      setIsCreatingCollection(false);
    }
  };

  const handleAddLeadsToCollection = async (collectionId: string) => {
    if (selectedLeads.size === 0) {
      toast.error('No leads selected');
      return;
    }
    
    setIsLoading(true);
    try {
      const result = await apollo.addLeadsToCollection(
        collectionId,
        Array.from(selectedLeads)
      );
      
      toast.success(`Added ${result.count} leads to collection`);
      
      // Update the lead count in the collection
      setLeadCollections(prev => 
        prev.map(c => 
          c.id === collectionId 
            ? { ...c, leadCount: c.leadCount + result.count, updatedAt: new Date().toISOString() } 
            : c
        )
      );
      
      // Clear selection
      setSelectedLeads(new Set());
    } catch (error) {
      console.error('Error adding leads to collection:', error);
      toast.error('Failed to add leads to collection');
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewCollection = async (collection: LeadCollection) => {
    setSelectedCollection(collection);
    setIsLoadingCollectionLeads(true);
    
    try {
      const leads = await apollo.getCollectionLeads(collection.id);
      setCollectionLeads(leads);
    } catch (error) {
      console.error(`Error loading leads for collection ${collection.id}:`, error);
      toast.error('Failed to load collection leads');
    } finally {
      setIsLoadingCollectionLeads(false);
    }
  };

  const handleAddKeyword = () => {
    if (!newKeyword.trim()) return;
    
    setSearchParams(prev => ({
      ...prev,
      keywords: [...(prev.keywords || []), newKeyword.trim()]
    }));
    
    setNewKeyword('');
  };

  const handleRemoveKeyword = (keyword: string) => {
    setSearchParams(prev => ({
      ...prev,
      keywords: (prev.keywords || []).filter(k => k !== keyword)
    }));
  };

  const toggleLeadSelection = (leadId: string) => {
    const newSelection = new Set(selectedLeads);
    if (newSelection.has(leadId)) {
      newSelection.delete(leadId);
    } else {
      newSelection.add(leadId);
    }
    setSelectedLeads(newSelection);
  };

  const selectAllLeads = () => {
    const allLeadIds = searchResults.map(lead => lead.id);
    setSelectedLeads(new Set(allLeadIds));
  };

  const clearLeadSelection = () => {
    setSelectedLeads(new Set());
  };

  const prepareCSVData = () => {
    const leads = selectedLeads.size > 0 
      ? searchResults.filter(lead => selectedLeads.has(lead.id))
      : searchResults;
    
    return leads.map(lead => ({
      'First Name': lead.firstName,
      'Last Name': lead.lastName,
      'Email': lead.email,
      'Company': lead.company,
      'Title': lead.title,
      'Phone': lead.phone || '',
      'LinkedIn': lead.linkedin || '',
      'Industry': lead.industry || '',
      'Company Size': lead.companySize || '',
      'Location': lead.location || '',
      'Tags': lead.tags.join(', ')
    }));
  };

  const handleExportCSV = () => {
    setIsExporting(true);
    setTimeout(() => {
      setIsExporting(false);
    }, 1000);
  };

  const generateMarketingReport = () => {
    if (!marketingStrategy) return '';

    return `# Marketing Strategy Report

## Brand Positioning

**Tagline:** ${marketingStrategy.brandPositioning.tagline}

**Value Proposition:** ${marketingStrategy.brandPositioning.valueProposition}

**Target Audience:**
${marketingStrategy.brandPositioning.targetAudience.map(audience => `- ${audience}`).join('\n')}

**Brand Voice:** ${marketingStrategy.brandPositioning.brandVoice}

**Key Messages:**
${marketingStrategy.brandPositioning.keyMessages.map(message => `- ${message}`).join('\n')}

## Content Calendar

| Week | Topic | Channel | Format | Goal |
|------|-------|---------|--------|------|
${marketingStrategy.contentCalendar.map(item => 
  `| ${item.week} | ${item.topic} | ${item.channel} | ${item.format} | ${item.goal} |`
).join('\n')}

## Growth Tactics

${marketingStrategy.growthTactics.map((tactic, index) => `
### ${index + 1}. ${tactic.name}
- **Description:** ${tactic.description}
- **Budget:** ${tactic.budget}
- **Expected ROI:** ${tactic.expectedROI}
- **Timeline:** ${tactic.timeline}
`).join('')}

## Key Metrics

### Acquisition
- **Target:** ${marketingStrategy.metrics.acquisition.target}
- **Channels:** ${marketingStrategy.metrics.acquisition.channels.join(', ')}

### Activation
- **Target:** ${marketingStrategy.metrics.activation.target}
- **Strategies:** ${marketingStrategy.metrics.activation.strategies.join(', ')}

### Retention
- **Target:** ${marketingStrategy.metrics.retention.target}
- **Strategies:** ${marketingStrategy.metrics.retention.strategies.join(', ')}

### Revenue
- **Target:** ${marketingStrategy.metrics.revenue.target}
- **Strategies:** ${marketingStrategy.metrics.revenue.strategies.join(', ')}

### Referral
- **Target:** ${marketingStrategy.metrics.referral.target}
- **Strategies:** ${marketingStrategy.metrics.referral.strategies.join(', ')}

## Lead Generation Summary

**Total Lead Collections:** ${leadCollections.length}
**Total Leads Generated:** ${leadCollections.reduce((sum, collection) => sum + collection.leadCount, 0)}

---
*Generated by Marketing Co-founder Agent*`;
  };

  const downloadMarketingReport = () => {
    const report = generateMarketingReport();
    const blob = new Blob([report], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'marketing-strategy-report.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center">
        <div className="w-20 h-20 bg-gradient-to-br from-pink-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <Megaphone className="w-10 h-10 text-white" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Marketing Co-founder Agent
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          Growth strategy, lead generation, and brand building
        </p>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-lg">
        <div className="flex gap-4">
          <button
            onClick={() => setCurrentView('strategy')}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
              currentView === 'strategy'
                ? 'bg-pink-500 text-white'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            <Target size={16} />
            Marketing Strategy
          </button>
          <button
            onClick={() => setCurrentView('leads')}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
              currentView === 'leads'
                ? 'bg-pink-500 text-white'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            <Users size={16} />
            Lead Generation
          </button>
        </div>
      </div>

      {/* Content */}
      <AnimatePresence mode="wait">
        {currentView === 'strategy' && (
          <motion.div
            key="strategy"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {isGeneratingStrategy ? (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg text-center">
                <div className="w-16 h-16 bg-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Generating Marketing Strategy...
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Creating comprehensive marketing plan for your startup
                </p>
              </div>
            ) : marketingStrategy ? (
              <>
                {/* Brand Positioning */}
                <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
                  <div className="flex items-center gap-3 mb-4">
                    <Target className="w-6 h-6 text-pink-500" />
                    <h2 className="text-xl font-bold text-gray-900 dark:text-white">Brand Positioning</h2>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white mb-2">Tagline</h3>
                      <p className="text-lg text-pink-600 dark:text-pink-400 font-medium">
                        "{marketingStrategy.brandPositioning.tagline}"
                      </p>
                    </div>
                    
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white mb-2">Value Proposition</h3>
                      <p className="text-gray-600 dark:text-gray-400">
                        {marketingStrategy.brandPositioning.valueProposition}
                      </p>
                    </div>
                    
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white mb-2">Target Audience</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {marketingStrategy.brandPositioning.targetAudience.map((audience, index) => (
                          <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <Users className="w-4 h-4 text-pink-500" />
                            <span className="text-gray-700 dark:text-gray-300">{audience}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white mb-2">Key Messages</h3>
                      <div className="space-y-2">
                        {marketingStrategy.brandPositioning.keyMessages.map((message, index) => (
                          <div key={index} className="flex items-start gap-2">
                            <div className="w-5 h-5 bg-pink-500 text-white rounded-full flex items-center justify-center text-xs mt-0.5">
                              {index + 1}
                            </div>
                            <p className="text-gray-600 dark:text-gray-400">{message}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Content Calendar */}
                <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
                  <div className="flex items-center gap-3 mb-4">
                    <Calendar className="w-6 h-6 text-pink-500" />
                    <h2 className="text-xl font-bold text-gray-900 dark:text-white">Content Calendar</h2>
                  </div>
                  
                  <div className="overflow-x-auto">
                    <table className="min-w-full">
                      <thead>
                        <tr className="bg-gray-50 dark:bg-gray-700">
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Week</th>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Topic</th>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Channel</th>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Format</th>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Goal</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                        {marketingStrategy.contentCalendar.map((item, index) => (
                          <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">Week {item.week}</td>
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">{item.topic}</td>
                            <td className="px-4 py-3 text-sm text-gray-600 dark:text-gray-400">{item.channel}</td>
                            <td className="px-4 py-3 text-sm text-gray-600 dark:text-gray-400">{item.format}</td>
                            <td className="px-4 py-3 text-sm">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                item.goal === 'Awareness' ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300' :
                                item.goal === 'Consideration' ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300' :
                                item.goal === 'Conversion' ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300' :
                                'bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300'
                              }`}>
                                {item.goal}
                              </span>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* Growth Tactics */}
                <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
                  <div className="flex items-center gap-3 mb-4">
                    <BarChart3 className="w-6 h-6 text-pink-500" />
                    <h2 className="text-xl font-bold text-gray-900 dark:text-white">Growth Tactics</h2>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {marketingStrategy.growthTactics.map((tactic, index) => (
                      <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h3 className="font-medium text-gray-900 dark:text-white mb-2">{tactic.name}</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{tactic.description}</p>
                        <div className="grid grid-cols-3 gap-2 text-xs">
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Budget</span>
                            <p className="font-medium text-gray-900 dark:text-white">{tactic.budget}</p>
                          </div>
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">ROI</span>
                            <p className="font-medium text-gray-900 dark:text-white">{tactic.expectedROI}</p>
                          </div>
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Timeline</span>
                            <p className="font-medium text-gray-900 dark:text-white">{tactic.timeline}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Download Report Button */}
                <div className="flex gap-4">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={downloadMarketingReport}
                    className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-pink-500 to-red-600 hover:from-pink-600 hover:to-red-700 text-white rounded-lg font-medium transition-all"
                  >
                    <Download size={16} />
                    Download Marketing Strategy
                  </motion.button>
                  <button
                    onClick={() => setCurrentView('leads')}
                    className="px-6 py-3 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium transition-colors flex items-center gap-2"
                  >
                    <Users size={16} />
                    Generate Leads
                  </button>
                </div>
              </>
            ) : (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg text-center">
                <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Target className="w-8 h-8 text-gray-400 dark:text-gray-500" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  No Marketing Strategy Found
                </h2>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Generate a comprehensive marketing strategy for your startup
                </p>
                <button
                  onClick={generateMarketingStrategy}
                  className="px-6 py-3 bg-pink-500 hover:bg-pink-600 text-white rounded-lg font-medium transition-colors"
                >
                  Generate Strategy
                </button>
              </div>
            )}
          </motion.div>
        )}

        {currentView === 'leads' && (
          <motion.div
            key="leads"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {/* Lead Search */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <Search className="w-6 h-6 text-pink-500" />
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white">Find Product-Specific Leads</h2>
                </div>
                <button
                  onClick={() => loadLeadCollections()}
                  className="p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                >
                  <RefreshCw size={16} />
                </button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Industry
                  </label>
                  <select
                    value={searchParams.industry || ''}
                    onChange={(e) => setSearchParams(prev => ({ ...prev, industry: e.target.value }))}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="">All Industries</option>
                    <option value="Software">Software</option>
                    <option value="Finance">Finance</option>
                    <option value="Healthcare">Healthcare</option>
                    <option value="Retail">Retail</option>
                    <option value="Manufacturing">Manufacturing</option>
                    <option value="Education">Education</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Job Title
                  </label>
                  <select
                    value={searchParams.title || ''}
                    onChange={(e) => setSearchParams(prev => ({ ...prev, title: e.target.value }))}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="">All Titles</option>
                    <option value="CEO">CEO</option>
                    <option value="CTO">CTO</option>
                    <option value="CFO">CFO</option>
                    <option value="COO">COO</option>
                    <option value="VP of Engineering">VP of Engineering</option>
                    <option value="VP of Product">VP of Product</option>
                    <option value="VP of Marketing">VP of Marketing</option>
                    <option value="Director of IT">Director of IT</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Company Size
                  </label>
                  <select
                    value={searchParams.companySize || ''}
                    onChange={(e) => setSearchParams(prev => ({ ...prev, companySize: e.target.value }))}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="">All Sizes</option>
                    <option value="1-10">1-10 employees</option>
                    <option value="11-50">11-50 employees</option>
                    <option value="51-200">51-200 employees</option>
                    <option value="201-500">201-500 employees</option>
                    <option value="501-1000">501-1000 employees</option>
                    <option value="1000+">1000+ employees</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Location
                  </label>
                  <select
                    value={searchParams.location || ''}
                    onChange={(e) => setSearchParams(prev => ({ ...prev, location: e.target.value }))}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="">All Locations</option>
                    <option value="San Francisco, CA">San Francisco, CA</option>
                    <option value="New York, NY">New York, NY</option>
                    <option value="Austin, TX">Austin, TX</option>
                    <option value="Boston, MA">Boston, MA</option>
                    <option value="Seattle, WA">Seattle, WA</option>
                    <option value="London, UK">London, UK</option>
                  </select>
                </div>
              </div>
              
              {/* Keywords */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Product-Specific Keywords
                </label>
                <div className="flex gap-2 mb-2">
                  <input
                    type="text"
                    value={newKeyword}
                    onChange={(e) => setNewKeyword(e.target.value)}
                    placeholder="Add keyword (e.g., AI, SaaS, startup)"
                    className="flex-1 p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    onKeyPress={(e) => e.key === 'Enter' && handleAddKeyword()}
                  />
                  <button
                    onClick={handleAddKeyword}
                    className="p-2 bg-pink-500 hover:bg-pink-600 text-white rounded-lg"
                  >
                    <Plus size={16} />
                  </button>
                </div>
                
                {searchParams.keywords && searchParams.keywords.length > 0 && (
                  <div className="flex flex-wrap gap-2 mb-2">
                    {searchParams.keywords.map((keyword, index) => (
                      <div 
                        key={index}
                        className="flex items-center gap-1 px-2 py-1 bg-pink-100 dark:bg-pink-900/30 text-pink-700 dark:text-pink-300 rounded-full text-sm"
                      >
                        <Tag size={12} />
                        <span>{keyword}</span>
                        <button
                          onClick={() => handleRemoveKeyword(keyword)}
                          className="ml-1 text-pink-700 dark:text-pink-300 hover:text-pink-900 dark:hover:text-pink-100"
                        >
                          <X size={12} />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              
              <div className="flex gap-4">
                <button
                  onClick={handleSearch}
                  disabled={isSearching}
                  className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-pink-500 hover:bg-pink-600 text-white rounded-lg transition-colors disabled:opacity-70"
                >
                  {isSearching ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      Searching...
                    </>
                  ) : (
                    <>
                      <Search size={16} />
                      Find Leads
                    </>
                  )}
                </button>
                
                <button
                  onClick={() => setSearchParams({
                    industry: '',
                    title: '',
                    keywords: [],
                    limit: 20
                  })}
                  className="px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                >
                  Clear Filters
                </button>
              </div>
            </div>

            {/* Search Results */}
            {searchResults.length > 0 && (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <Users className="w-6 h-6 text-pink-500" />
                    <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                      {searchResults.length} Leads Found
                    </h2>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {selectedLeads.size} selected
                    </span>
                    
                    <div className="flex gap-1">
                      <button
                        onClick={selectAllLeads}
                        className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 text-xs"
                      >
                        Select All
                      </button>
                      {selectedLeads.size > 0 && (
                        <button
                          onClick={clearLeadSelection}
                          className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 text-xs"
                        >
                          Clear
                        </button>
                      )}
                    </div>
                  </div>
                </div>
                
                {/* Lead Actions */}
                {selectedLeads.size > 0 && (
                  <div className="flex flex-wrap gap-2 mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Actions:
                      </span>
                    </div>
                    
                    <div className="flex flex-wrap gap-2">
                      <div className="relative group">
                        <button
                          className="flex items-center gap-1 px-3 py-1 bg-pink-100 dark:bg-pink-900/30 text-pink-700 dark:text-pink-300 rounded-lg text-sm hover:bg-pink-200 dark:hover:bg-pink-900/50 transition-colors"
                        >
                          <UserPlus size={14} />
                          Add to Collection
                          <ChevronRight size={14} />
                        </button>
                        
                        <div className="absolute left-0 mt-1 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-10">
                          <div className="p-2">
                            <button
                              onClick={() => setShowCreateCollectionModal(true)}
                              className="w-full flex items-center gap-2 px-3 py-2 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
                            >
                              <Plus size={14} />
                              New Collection
                            </button>
                            
                            {leadCollections.map(collection => (
                              <button
                                key={collection.id}
                                onClick={() => handleAddLeadsToCollection(collection.id)}
                                className="w-full flex items-center gap-2 px-3 py-2 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
                              >
                                <FolderPlus size={14} />
                                {collection.name}
                              </button>
                            ))}
                          </div>
                        </div>
                      </div>
                      
                      <CSVLink
                        data={prepareCSVData()}
                        filename="apollo_leads.csv"
                        className="flex items-center gap-1 px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-lg text-sm hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors"
                        onClick={handleExportCSV}
                      >
                        {isExporting ? (
                          <>
                            <Loader2 size={14} className="animate-spin" />
                            Exporting...
                          </>
                        ) : (
                          <>
                            <FileSpreadsheet size={14} />
                            Export CSV
                          </>
                        )}
                      </CSVLink>
                    </div>
                  </div>
                )}
                
                {/* Leads Table */}
                <div className="overflow-x-auto">
                  <table className="min-w-full">
                    <thead>
                      <tr className="bg-gray-50 dark:bg-gray-700">
                        <th className="w-10 px-4 py-2"></th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Name</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Title</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Company</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Email</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Industry</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                      {searchResults.map((lead) => (
                        <tr key={lead.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                          <td className="px-4 py-3">
                            <input
                              type="checkbox"
                              checked={selectedLeads.has(lead.id)}
                              onChange={() => toggleLeadSelection(lead.id)}
                              className="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded"
                            />
                          </td>
                          <td className="px-4 py-3">
                            <div>
                              <p className="text-sm font-medium text-gray-900 dark:text-white">
                                {lead.firstName} {lead.lastName}
                              </p>
                              {lead.linkedin && (
                                <a 
                                  href={lead.linkedin} 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                  className="text-xs text-pink-600 dark:text-pink-400 hover:underline"
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  LinkedIn Profile
                                </a>
                              )}
                            </div>
                          </td>
                          <td className="px-4 py-3">
                            <div className="flex items-center gap-1">
                              <Briefcase className="w-3 h-3 text-gray-400" />
                              <span className="text-sm text-gray-600 dark:text-gray-400">{lead.title}</span>
                            </div>
                          </td>
                          <td className="px-4 py-3">
                            <div className="flex items-center gap-1">
                              <Building className="w-3 h-3 text-gray-400" />
                              <span className="text-sm text-gray-600 dark:text-gray-400">{lead.company}</span>
                            </div>
                          </td>
                          <td className="px-4 py-3">
                            <div className="flex items-center gap-1">
                              <Mail className="w-3 h-3 text-gray-400" />
                              <span className="text-sm text-gray-600 dark:text-gray-400">{lead.email}</span>
                            </div>
                          </td>
                          <td className="px-4 py-3">
                            <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-xs">
                              {lead.industry}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Lead Collections */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <FolderPlus className="w-6 h-6 text-pink-500" />
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white">Lead Collections</h2>
                </div>
                
                <button
                  onClick={() => setShowCreateCollectionModal(true)}
                  className="flex items-center gap-2 px-3 py-1.5 bg-pink-500 hover:bg-pink-600 text-white rounded-lg text-sm transition-colors"
                >
                  <Plus size={14} />
                  New Collection
                </button>
              </div>
              
              {isLoading ? (
                <div className="text-center py-8">
                  <Loader2 className="w-8 h-8 text-pink-500 mx-auto mb-4 animate-spin" />
                  <p className="text-gray-600 dark:text-gray-400">Loading collections...</p>
                </div>
              ) : leadCollections.length === 0 ? (
                <div className="text-center py-8 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <FolderPlus className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Collections Yet</h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    Create your first lead collection to start organizing your leads
                  </p>
                  <button
                    onClick={() => setShowCreateCollectionModal(true)}
                    className="px-4 py-2 bg-pink-500 hover:bg-pink-600 text-white rounded-lg transition-colors"
                  >
                    Create Collection
                  </button>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {leadCollections.map((collection) => (
                    <div
                      key={collection.id}
                      className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                      onClick={() => handleViewCollection(collection)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium text-gray-900 dark:text-white">{collection.name}</h3>
                        <span className="px-2 py-1 bg-pink-100 dark:bg-pink-900/30 text-pink-700 dark:text-pink-300 rounded-full text-xs">
                          {collection.leadCount} leads
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                        {collection.description || 'No description provided'}
                      </p>
                      <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                        <span>Created: {new Date(collection.createdAt).toLocaleDateString()}</span>
                        <span>Updated: {new Date(collection.updatedAt).toLocaleDateString()}</span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Selected Collection View */}
            {selectedCollection && (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <div className="flex items-center gap-3">
                      <FolderOpen className="w-6 h-6 text-pink-500" />
                      <h2 className="text-xl font-bold text-gray-900 dark:text-white">{selectedCollection.name}</h2>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {selectedCollection.description || 'No description provided'}
                    </p>
                  </div>
                  
                  <button
                    onClick={() => setSelectedCollection(null)}
                    className="p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                  >
                    <X size={16} />
                  </button>
                </div>
                
                {isLoadingCollectionLeads ? (
                  <div className="text-center py-8">
                    <Loader2 className="w-8 h-8 text-pink-500 mx-auto mb-4 animate-spin" />
                    <p className="text-gray-600 dark:text-gray-400">Loading leads...</p>
                  </div>
                ) : collectionLeads.length === 0 ? (
                  <div className="text-center py-8 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Leads in Collection</h3>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                      This collection doesn't have any leads yet
                    </p>
                    <button
                      onClick={() => setSelectedCollection(null)}
                      className="px-4 py-2 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                    >
                      Back to Collections
                    </button>
                  </div>
                ) : (
                  <>
                    <div className="overflow-x-auto mb-4">
                      <table className="min-w-full">
                        <thead>
                          <tr className="bg-gray-50 dark:bg-gray-700">
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Name</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Title</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Company</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Email</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Industry</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                          {collectionLeads.map((lead) => (
                            <tr key={lead.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                              <td className="px-4 py-3">
                                <div>
                                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                                    {lead.firstName} {lead.lastName}
                                  </p>
                                  {lead.linkedin && (
                                    <a 
                                      href={lead.linkedin} 
                                      target="_blank" 
                                      rel="noopener noreferrer"
                                      className="text-xs text-pink-600 dark:text-pink-400 hover:underline"
                                      onClick={(e) => e.stopPropagation()}
                                    >
                                      LinkedIn Profile
                                    </a>
                                  )}
                                </div>
                              </td>
                              <td className="px-4 py-3">
                                <div className="flex items-center gap-1">
                                  <Briefcase className="w-3 h-3 text-gray-400" />
                                  <span className="text-sm text-gray-600 dark:text-gray-400">{lead.title}</span>
                                </div>
                              </td>
                              <td className="px-4 py-3">
                                <div className="flex items-center gap-1">
                                  <Building className="w-3 h-3 text-gray-400" />
                                  <span className="text-sm text-gray-600 dark:text-gray-400">{lead.company}</span>
                                </div>
                              </td>
                              <td className="px-4 py-3">
                                <div className="flex items-center gap-1">
                                  <Mail className="w-3 h-3 text-gray-400" />
                                  <span className="text-sm text-gray-600 dark:text-gray-400">{lead.email}</span>
                                </div>
                              </td>
                              <td className="px-4 py-3">
                                <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-xs">
                                  {lead.industry}
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                    
                    <div className="flex justify-between">
                      <button
                        onClick={() => setSelectedCollection(null)}
                        className="px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                      >
                        Back to Collections
                      </button>
                      
                      <CSVLink
                        data={collectionLeads.map(lead => ({
                          'First Name': lead.firstName,
                          'Last Name': lead.lastName,
                          'Email': lead.email,
                          'Company': lead.company,
                          'Title': lead.title,
                          'Phone': lead.phone || '',
                          'LinkedIn': lead.linkedin || '',
                          'Industry': lead.industry || '',
                          'Company Size': lead.companySize || '',
                          'Location': lead.location || '',
                          'Tags': lead.tags.join(', ')
                        }))}
                        filename={`${selectedCollection.name.toLowerCase().replace(/\s+/g, '_')}_leads.csv`}
                        className="flex items-center gap-2 px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors"
                      >
                        <FileSpreadsheet size={16} />
                        Export to CSV
                      </CSVLink>
                    </div>
                  </>
                )}
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Create Collection Modal */}
      {showCreateCollectionModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">Create Lead Collection</h3>
                <button 
                  onClick={() => setShowCreateCollectionModal(false)}
                  className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Collection Name *
                  </label>
                  <input
                    type="text"
                    value={newCollectionName}
                    onChange={(e) => setNewCollectionName(e.target.value)}
                    placeholder="Enter collection name"
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Description (Optional)
                  </label>
                  <textarea
                    value={newCollectionDescription}
                    onChange={(e) => setNewCollectionDescription(e.target.value)}
                    placeholder="Enter collection description"
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    rows={3}
                  />
                </div>

                {selectedLeads.size > 0 && (
                  <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="flex items-center gap-2 text-green-700 dark:text-green-300">
                      <CheckCircle size={16} />
                      <span>{selectedLeads.size} leads will be added to this collection</span>
                    </div>
                  </div>
                )}
              </div>

              <div className="mt-6 flex gap-3">
                <button
                  onClick={handleCreateCollection}
                  disabled={isCreatingCollection || !newCollectionName}
                  className="flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-pink-500 hover:bg-pink-600 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors disabled:cursor-not-allowed"
                >
                  {isCreatingCollection ? (
                    <>
                      <Loader2 className="w-5 h-5 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Plus className="w-5 h-5" />
                      Create Collection
                    </>
                  )}
                </button>
                <button
                  onClick={() => setShowCreateCollectionModal(false)}
                  className="px-4 py-3 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Custom FolderPlus icon
const FolderPlus: React.FC<{ size?: number; className?: string }> = ({ size = 24, className = '' }) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    width={size} 
    height={size} 
    viewBox="0 0 24 24" 
    fill="none" 
    stroke="currentColor" 
    strokeWidth="2" 
    strokeLinecap="round" 
    strokeLinejoin="round" 
    className={className}
  >
    <path d="M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2Z"></path>
    <line x1="12" y1="10" x2="12" y2="16"></line>
    <line x1="9" y1="13" x2="15" y2="13"></line>
  </svg>
);

// Custom FolderOpen icon
const FolderOpen: React.FC<{ size?: number; className?: string }> = ({ size = 24, className = '' }) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    width={size} 
    height={size} 
    viewBox="0 0 24 24" 
    fill="none" 
    stroke="currentColor" 
    strokeWidth="2" 
    strokeLinecap="round" 
    strokeLinejoin="round" 
    className={className}
  >
    <path d="m6 14 1.45-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.55 6a2 2 0 0 1-1.94 1.5H4a2 2 0 0 1-2-2V5c0-1.1.9-2 2-2h3.93a2 2 0 0 1 1.66.9l.82 1.2a2 2 0 0 0 1.66.9H18a2 2 0 0 1 2 2v2"></path>
  </svg>
);