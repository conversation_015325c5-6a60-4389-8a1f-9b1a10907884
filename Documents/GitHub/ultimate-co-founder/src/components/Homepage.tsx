import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MessageCircle, 
  Mic, 
  Video, 
  Send, 
  TrendingUp, 
  Rocket, 
  Code2, 
  Settings, 
  Megaphone,
  Play,
  ArrowRight,
  Sparkles,
  Users,
  Zap,
  FileText,
  DollarSign,
  Phone,
  ChevronRight,
  Star,
  Globe,
  Github,
  Twitter,
  Linkedin,
  CheckCircle,
  Clock,
  Target,
  LogOut,
  User,
  Monitor,
  X
} from 'lucide-react';
import { useAIOrchestrator } from '../hooks/useAIOrchestrator';
import { LiveKitRoom } from './LiveKitRoom';
import { useAuth } from './AuthProvider';
import { Link } from 'react-router-dom';
import { ThemeToggle } from './ThemeToggle';
import { MainNavigation } from './MainNavigation';
import { Footer } from './Footer';

interface Agent {
  id: string;
  name: string;
  role: string;
  icon: React.ElementType;
  color: string;
  description: string;
  expertise: string[];
  image: string;
}

interface HomepageProps {
  onShowAuth: () => void;
}

export const Homepage: React.FC<HomepageProps> = ({ onShowAuth }) => {
  const [chatInput, setChatInput] = useState('');
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [showLiveKitModal, setShowLiveKitModal] = useState(false);
  const [currentAgentIndex, setCurrentAgentIndex] = useState(0);
  const { user, logout, isAuthenticated } = useAuth();

  const {
    messages,
    isProcessing,
    pendingSuggestions,
    liveKitSession,
    sendMessage,
    executeSuggestion,
    startLiveKitSession,
    endLiveKitSession,
    clearMessages,
    hasApiKey
  } = useAIOrchestrator();

  const agents: Agent[] = [
    {
      id: 'strategic',
      name: 'Strategic',
      role: 'Market & Strategy',
      icon: TrendingUp,
      color: 'from-blue-500 to-purple-600',
      description: 'Market analysis, competitive research, and strategic planning',
      expertise: ['Market Research', 'Strategic Planning', 'Competitive Analysis', 'Business Modeling'],
      image: 'https://images.pexels.com/photos/3184338/pexels-photo-3184338.jpeg?auto=compress&cs=tinysrgb&w=600'
    },
    {
      id: 'product',
      name: 'Product',
      role: 'UX & Features',
      icon: Rocket,
      color: 'from-purple-500 to-pink-600',
      description: 'Product strategy, user experience, and feature prioritization',
      expertise: ['Product Strategy', 'UX Design', 'Feature Planning', 'User Research'],
      image: 'https://images.pexels.com/photos/3182812/pexels-photo-3182812.jpeg?auto=compress&cs=tinysrgb&w=600'
    },
    {
      id: 'technical',
      name: 'Technical',
      role: 'Architecture & Code',
      icon: Code2,
      color: 'from-green-500 to-blue-600',
      description: 'System architecture, development, and technical implementation',
      expertise: ['System Architecture', 'Code Generation', 'Technical Review', 'DevOps'],
      image: 'https://images.pexels.com/photos/3183150/pexels-photo-3183150.jpeg?auto=compress&cs=tinysrgb&w=600'
    },
    {
      id: 'operations',
      name: 'Operations',
      role: 'Process & Scale',
      icon: Settings,
      color: 'from-orange-500 to-red-600',
      description: 'Process optimization, resource management, and operations',
      expertise: ['Process Design', 'Resource Planning', 'Quality Assurance', 'Automation'],
      image: 'https://images.pexels.com/photos/3184292/pexels-photo-3184292.jpeg?auto=compress&cs=tinysrgb&w=600'
    },
    {
      id: 'marketing',
      name: 'Marketing',
      role: 'Growth & Brand',
      icon: Megaphone,
      color: 'from-pink-500 to-red-600',
      description: 'Growth strategy, brand building, and customer acquisition',
      expertise: ['Growth Strategy', 'Content Creation', 'Analytics', 'Campaign Management'],
      image: 'https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg?auto=compress&cs=tinysrgb&w=600'
    }
  ];

  // Auto-rotate agent preview
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentAgentIndex((prev) => (prev + 1) % agents.length);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  const handleSendMessage = async () => {
    if (!chatInput.trim()) return;

    const agentIds = selectedAgent ? [selectedAgent] : undefined;
    await sendMessage(chatInput, agentIds);
    setChatInput('');
    
    // If user is not logged in and this is their first message, prompt them to create an account
    if (!isAuthenticated && messages.length === 0) {
      setTimeout(() => {
        onShowAuth();
      }, 2000);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleStartLiveKit = async (sessionType: 'voice' | 'video' | 'screen-share') => {
    console.log('🎬 Homepage: Starting LiveKit session...', { sessionType, isAuthenticated });

    if (!isAuthenticated) {
      console.log('🔒 User not authenticated, showing auth modal');
      onShowAuth();
      return;
    }

    try {
      const agentIds = selectedAgent ? [selectedAgent] : ['strategic', 'product', 'technical'];
      console.log('🤖 Selected agents:', agentIds);

      console.log('🚀 Calling startLiveKitSession...');
      const session = await startLiveKitSession(agentIds, sessionType);
      console.log('✅ LiveKit session started successfully:', session);

      setShowLiveKitModal(false);

      // Add success notification to window for debugging
      if (typeof window !== 'undefined') {
        (window as any).lastHomepageSession = session;
        console.log('💾 Session saved to window.lastHomepageSession for debugging');
      }

    } catch (error) {
      console.error('❌ Homepage: Failed to start LiveKit session:', error);
      console.error('❌ Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        error
      });

      // Add error to window for debugging
      if (typeof window !== 'undefined') {
        (window as any).lastHomepageError = error;
        console.log('💾 Error saved to window.lastHomepageError for debugging');
      }

      // Show user-friendly error message
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

      if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
        toast.error('Network error: Please check your internet connection and try again.');
      } else if (errorMessage.includes('auth') || errorMessage.includes('401')) {
        toast.error('Authentication error: Please log in again.');
        onShowAuth();
      } else if (errorMessage.includes('permission')) {
        toast.error('Permission error: Please allow camera/microphone access in your browser.');
      } else {
        toast.error(`Failed to start ${sessionType} session. Running in demo mode.`);
      }
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'border-red-200 bg-red-50 text-red-700 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300';
      case 'medium':
        return 'border-yellow-200 bg-yellow-50 text-yellow-700 dark:border-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'low':
        return 'border-green-200 bg-green-50 text-green-700 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300';
      default:
        return 'border-gray-200 bg-gray-50 text-gray-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  // Show LiveKit room if session is active
  if (liveKitSession && liveKitSession.status === 'connected') {
    return <LiveKitRoom session={liveKitSession} onEnd={endLiveKitSession} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Navigation */}
      <MainNavigation />

      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="relative z-10"
            >
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6 leading-tight">
                Your AI Co-Founder Team,
                <br />
                <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                  All in One Chat
                </span>
              </h1>
              
              <p className="text-xl text-gray-600 dark:text-gray-400 mb-8 leading-relaxed">
                Ask or speak your idea—get strategy, product, tech, ops, and marketing guidance instantly. 
                Five specialized AI co-founders working together in perfect harmony.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 mb-8">
                <button
                  onClick={() => {
                    if (!isAuthenticated) {
                      onShowAuth();
                    } else if (messages.length === 0) {
                      setChatInput("I want to build a restaurant management app with AI-powered features");
                    }
                  }}
                  className="px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl font-semibold text-lg transition-all hover:shadow-lg"
                >
                  Try It Now - Free
                </button>
                <button 
                  onClick={() => {
                    if (isAuthenticated) {
                      setShowLiveKitModal(true);
                    } else {
                      onShowAuth();
                    }
                  }}
                  className="px-8 py-4 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-700 hover:border-gray-400 dark:hover:border-gray-600 text-gray-900 dark:text-white rounded-xl font-semibold text-lg transition-all hover:shadow-lg flex items-center gap-2"
                >
                  <Video size={20} />
                  Start Live Session
                </button>
              </div>

              {/* Agent Selection */}
              <div className="mb-8">
                <p className="text-gray-600 dark:text-gray-400 mb-4">Refine with a specific co-founder:</p>
                <div className="flex flex-wrap gap-3">
                  {selectedAgent && (
                    <button
                      onClick={() => setSelectedAgent(null)}
                      className="flex items-center gap-2 px-4 py-2 rounded-lg border-2 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-gray-300 dark:hover:border-gray-600 transition-all"
                    >
                      <div className="w-6 h-6 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                        <Users className="w-3 h-3 text-white" />
                      </div>
                      <span className="font-medium text-gray-900 dark:text-white">All Co-founders</span>
                    </button>
                  )}
                  
                  {agents.map((agent) => {
                    const IconComponent = agent.icon;
                    return (
                      <button
                        key={agent.id}
                        onClick={() => {
                          if (!isAuthenticated) {
                            onShowAuth();
                          } else {
                            setSelectedAgent(selectedAgent === agent.id ? null : agent.id);
                          }
                        }}
                        className={`flex items-center gap-2 px-4 py-2 rounded-lg border-2 transition-all ${
                          selectedAgent === agent.id
                            ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20'
                            : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-gray-300 dark:hover:border-gray-600'
                        }`}
                      >
                        <div className={`w-6 h-6 bg-gradient-to-br ${agent.color} rounded-lg flex items-center justify-center`}>
                          <IconComponent className="w-3 h-3 text-white" />
                        </div>
                        <span className="font-medium text-gray-900 dark:text-white">{agent.name}</span>
                      </button>
                    );
                  })}
                </div>
              </div>
            </motion.div>

            {/* Chat Widget */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 p-6 max-w-xl mx-auto lg:mx-0 w-full"
            >
              {/* Agent Indicator */}
              {selectedAgent ? (
                <div className="mb-4 flex items-center justify-center gap-2">
                  <div className={`w-6 h-6 bg-gradient-to-br ${agents.find(a => a.id === selectedAgent)?.color || 'from-purple-500 to-blue-600'} rounded-lg flex items-center justify-center`}>
                    {(() => {
                      const IconComponent = agents.find(a => a.id === selectedAgent)?.icon || MessageCircle;
                      return <IconComponent className="w-3 h-3 text-white" />;
                    })()}
                  </div>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Chatting with {agents.find(a => a.id === selectedAgent)?.name || 'Agent'} Co-founder
                  </span>
                </div>
              ) : (
                <div className="mb-4 flex items-center justify-center gap-2">
                  <div className="w-6 h-6 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                    <Users className="w-3 h-3 text-white" />
                  </div>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Chatting with All Co-founders
                  </span>
                </div>
              )}

              {/* Chat Messages */}
              <div className="h-80 overflow-y-auto mb-4 space-y-3">
                {messages.length === 0 ? (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 bg-gradient-to-br from-purple-100 to-blue-100 dark:from-purple-900/30 dark:to-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                      <MessageCircle className="w-8 h-8 text-purple-600 dark:text-purple-400" />
                    </div>
                    <p className="text-gray-500 dark:text-gray-400 mb-2">Ready to help you build your startup</p>
                    <p className="text-sm text-gray-400 dark:text-gray-500">Try: "I want to build a restaurant management app"</p>
                  </div>
                ) : (
                  <>
                    {messages.map((message) => (
                      <div key={message.id}>
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                        >
                          <div className={`max-w-xs p-3 rounded-lg ${
                            message.type === 'user'
                              ? 'bg-purple-600 text-white'
                              : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
                          }`}>
                            {message.type === 'agent' && (
                              <p className="text-xs font-medium text-purple-600 dark:text-purple-400 mb-1">{message.agent}</p>
                            )}
                            <p className="text-sm">{message.content}</p>
                          </div>
                        </motion.div>

                        {/* Agent Suggestions */}
                        {message.suggestions && message.suggestions.length > 0 && (
                          <motion.div
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.3 }}
                            className="mt-3 space-y-2"
                          >
                            {message.suggestions.map((suggestion) => {
                              const IconComponent = suggestion.icon || Target;
                              return (
                                <motion.button
                                  key={suggestion.id}
                                  onClick={() => executeSuggestion(suggestion)}
                                  className={`w-full text-left p-3 border-2 rounded-lg transition-all hover:scale-[1.02] ${getPriorityColor(suggestion.priority)}`}
                                  whileHover={{ scale: 1.02 }}
                                  whileTap={{ scale: 0.98 }}
                                >
                                  <div className="flex items-start gap-3">
                                    <div className="w-8 h-8 bg-white dark:bg-gray-800 rounded-lg flex items-center justify-center flex-shrink-0">
                                      <IconComponent className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                      <div className="flex items-center justify-between mb-1">
                                        <h4 className="font-medium text-sm">{suggestion.title}</h4>
                                        <div className="flex items-center gap-2">
                                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                                            suggestion.priority === 'high' ? 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300' :
                                            suggestion.priority === 'medium' ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300' :
                                            'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                                          }`}>
                                            {suggestion.priority}
                                          </span>
                                          <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
                                            <Clock className="w-3 h-3" />
                                            {suggestion.estimatedTime}
                                          </span>
                                        </div>
                                      </div>
                                      <p className="text-xs opacity-80 mb-2">{suggestion.description}</p>
                                      <div className="flex items-center justify-between">
                                        <span className="text-xs font-medium">{suggestion.agentName} Agent</span>
                                        <ArrowRight className="w-3 h-3" />
                                      </div>
                                    </div>
                                  </div>
                                </motion.button>
                              );
                            })}
                          </motion.div>
                        )}
                      </div>
                    ))}
                    {isProcessing && (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="flex justify-start"
                      >
                        <div className="bg-gray-100 dark:bg-gray-700 p-3 rounded-lg">
                          <div className="flex space-x-1">
                            <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"></div>
                            <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                            <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </>
                )}
              </div>

              {/* Chat Input */}
              <div className="flex items-center gap-3">
                <div className="flex-1 relative">
                  <textarea
                    value={chatInput}
                    onChange={(e) => setChatInput(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder={selectedAgent 
                      ? `Ask ${agents.find(a => a.id === selectedAgent)?.name} about your startup idea...`
                      : "Ask all agents about your startup idea..."}
                    className="w-full p-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg resize-none focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    rows={1}
                    disabled={isProcessing}
                  />
                  <button
                    onClick={handleSendMessage}
                    disabled={!chatInput.trim() || isProcessing}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 disabled:text-gray-400 dark:disabled:text-gray-600 transition-colors"
                  >
                    <Send size={16} />
                  </button>
                </div>
                <button
                  onClick={() => {
                    if (isAuthenticated) {
                      setShowLiveKitModal(true);
                    } else {
                      onShowAuth();
                    }
                  }}
                  className="p-3 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white rounded-lg transition-all"
                >
                  <Mic size={16} />
                </button>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section id="how-it-works" className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">How It Works</h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Three simple steps to get comprehensive startup guidance from your AI co-founder team
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                step: '01',
                title: 'Tell us your idea',
                description: 'Type or speak your startup concept. Our AI agents listen and understand your vision.',
                icon: MessageCircle,
                color: 'from-blue-500 to-purple-600',
                image: 'https://images.pexels.com/photos/3182812/pexels-photo-3182812.jpeg?auto=compress&cs=tinysrgb&w=600'
              },
              {
                step: '02',
                title: 'Get unified guidance',
                description: 'All five co-founders respond in one feed with strategic, product, technical, ops, and marketing insights.',
                icon: Users,
                color: 'from-purple-500 to-pink-600',
                image: 'https://images.pexels.com/photos/3182781/pexels-photo-3182781.jpeg?auto=compress&cs=tinysrgb&w=600'
              },
              {
                step: '03',
                title: 'Fine-tune & export',
                description: 'Click into any agent for detailed analysis, custom outputs, and actionable next steps.',
                icon: FileText,
                color: 'from-pink-500 to-red-600',
                image: 'https://images.pexels.com/photos/3182774/pexels-photo-3182774.jpeg?auto=compress&cs=tinysrgb&w=600'
              }
            ].map((step, index) => {
              const IconComponent = step.icon;
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.2 }}
                  className="text-center"
                >
                  <div className="relative mb-6 rounded-xl overflow-hidden aspect-video shadow-lg">
                    <img 
                      src={step.image} 
                      alt={step.title} 
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.currentTarget.src = `https://placehold.co/600x400/1f2937/ffffff?text=${encodeURIComponent(step.title)}`;
                      }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end p-4">
                      <div className={`w-12 h-12 bg-gradient-to-br ${step.color} rounded-xl flex items-center justify-center mr-3`}>
                        <IconComponent className="w-6 h-6 text-white" />
                      </div>
                      <div className="text-left">
                        <div className="text-sm font-bold text-purple-300 mb-1">{step.step}</div>
                        <h3 className="text-xl font-bold text-white">{step.title}</h3>
                      </div>
                    </div>
                  </div>
                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed">{step.description}</p>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Agent Preview Carousel */}
      <section id="agents" className="py-20 bg-white dark:bg-gray-900">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">Meet Your AI Co-Founder Team</h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Five specialized agents, each an expert in their domain, working together to build your startup
            </p>
          </motion.div>

          {/* Featured Agent */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12 items-center">
            <motion.div
              key={`image-${currentAgentIndex}`}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="rounded-2xl overflow-hidden shadow-xl"
            >
              <img 
                src={agents[currentAgentIndex].image} 
                alt={`${agents[currentAgentIndex].name} Co-founder`} 
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.currentTarget.src = `https://placehold.co/600x400/1f2937/ffffff?text=${agents[currentAgentIndex].name}+Co-founder`;
                }}
              />
            </motion.div>
            
            <motion.div
              key={`info-${currentAgentIndex}`}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 p-8"
            >
              <div className="text-left">
                <div className="flex items-center gap-4 mb-4">
                  <div className={`w-16 h-16 bg-gradient-to-br ${agents[currentAgentIndex].color} rounded-2xl flex items-center justify-center`}>
                    {(() => {
                      const IconComponent = agents[currentAgentIndex].icon;
                      return <IconComponent className="w-8 h-8 text-white" />;
                    })()}
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{agents[currentAgentIndex].name} Co-founder</h3>
                    <p className="text-purple-600 dark:text-purple-400 font-medium">{agents[currentAgentIndex].role}</p>
                  </div>
                </div>
                
                <p className="text-gray-600 dark:text-gray-400 mb-6">{agents[currentAgentIndex].description}</p>
                
                <div className="flex flex-wrap gap-2 mb-6">
                  {agents[currentAgentIndex].expertise.map((skill, index) => (
                    <span key={index} className="px-3 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-full text-sm">
                      {skill}
                    </span>
                  ))}
                </div>
                
                <button
                  onClick={() => {
                    if (!isAuthenticated) {
                      onShowAuth();
                    } else {
                      setSelectedAgent(agents[currentAgentIndex].id);
                    }
                  }}
                  className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-lg font-medium transition-all flex items-center gap-2"
                >
                  Chat with {agents[currentAgentIndex].name}
                  <ArrowRight size={16} />
                </button>
              </div>
            </motion.div>
          </div>

          {/* Agent Grid */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {agents.map((agent, index) => {
              const IconComponent = agent.icon;
              return (
                <motion.button
                  key={agent.id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  onClick={() => setCurrentAgentIndex(index)}
                  className={`p-4 rounded-xl border-2 transition-all hover:shadow-md ${
                    currentAgentIndex === index
                      ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20'
                      : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-gray-300 dark:hover:border-gray-600'
                  }`}
                >
                  <div className={`w-12 h-12 bg-gradient-to-br ${agent.color} rounded-lg flex items-center justify-center mx-auto mb-3`}>
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="font-medium text-gray-900 dark:text-white text-sm">{agent.name}</h4>
                </motion.button>
              );
            })}
          </div>
        </div>
      </section>

      {/* LiveKit Banner */}
      <section className="py-16 relative overflow-hidden">
        <div className="absolute inset-0 z-0">
          <img 
            src="https://images.pexels.com/photos/7709020/pexels-photo-7709020.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1" 
            alt="Video call background" 
            className="w-full h-full object-cover opacity-20 dark:opacity-10"
            onError={(e) => {
              e.currentTarget.src = `https://placehold.co/1200x400/1f2937/ffffff?text=Video+Collaboration`;
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-r from-green-500/90 to-blue-600/90 dark:from-green-900/90 dark:to-blue-900/90 mix-blend-multiply"></div>
        </div>
        
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className="flex items-center justify-center gap-3 mb-6">
              <Video className="w-8 h-8 text-white" />
              <h2 className="text-3xl font-bold text-white">Voice & Video Coaching</h2>
            </div>
            <p className="text-xl text-green-100 mb-8 max-w-2xl mx-auto">
              Get real-time guidance through live video sessions. See your co-founders in action, 
              ask questions, and collaborate face-to-face.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-white">
                <Video className="w-10 h-10 text-white/80 mb-4 mx-auto" />
                <h3 className="text-xl font-bold mb-2">Video Sessions</h3>
                <p className="text-sm text-white/80">Face-to-face meetings with your AI co-founders for in-depth strategy sessions</p>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-white">
                <Mic className="w-10 h-10 text-white/80 mb-4 mx-auto" />
                <h3 className="text-xl font-bold mb-2">Voice Calls</h3>
                <p className="text-sm text-white/80">Audio-only conversations for quick guidance and brainstorming on the go</p>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-white">
                <Monitor className="w-10 h-10 text-white/80 mb-4 mx-auto" />
                <h3 className="text-xl font-bold mb-2">Screen Sharing</h3>
                <p className="text-sm text-white/80">Share your screen for collaborative work on documents, code, and designs</p>
              </div>
            </div>
            
            <button 
              onClick={() => {
                if (isAuthenticated) {
                  setShowLiveKitModal(true);
                } else {
                  onShowAuth();
                }
              }}
              className="px-8 py-4 bg-white text-green-600 rounded-xl font-semibold text-lg transition-all hover:scale-105 shadow-lg flex items-center gap-3 mx-auto"
            >
              <Play size={20} />
              Start Live Session
            </button>
          </motion.div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">What Founders Say</h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Hear from entrepreneurs who've built successful startups with our AI co-founders
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                quote: "The strategic co-founder helped me identify a market opportunity I hadn't considered. We pivoted our business model and saw 3x growth in 6 months.",
                author: "Sarah Johnson",
                role: "Founder, FinTech Startup",
                image: "https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=150"
              },
              {
                quote: "Having five AI co-founders is like having a dream team on demand. The technical agent saved us months of development time with its architecture recommendations.",
                author: "Michael Chen",
                role: "CTO, SaaS Platform",
                image: "https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=150"
              },
              {
                quote: "The marketing co-founder developed our entire go-to-market strategy in one session. Their insights helped us acquire our first 1,000 customers for half the projected CAC.",
                author: "Aisha Patel",
                role: "CEO, E-commerce Brand",
                image: "https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=150"
              }
            ].map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-white dark:bg-gray-700 rounded-xl p-6 shadow-md"
              >
                <div className="flex items-center gap-4 mb-4">
                  <img 
                    src={testimonial.image} 
                    alt={testimonial.author} 
                    className="w-12 h-12 rounded-full object-cover"
                    onError={(e) => {
                      e.currentTarget.src = `https://placehold.co/150x150/1f2937/ffffff?text=${testimonial.author.charAt(0)}`;
                    }}
                  />
                  <div>
                    <h4 className="font-bold text-gray-900 dark:text-white">{testimonial.author}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{testimonial.role}</p>
                  </div>
                </div>
                <p className="text-gray-700 dark:text-gray-300 italic">"{testimonial.quote}"</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* LiveKit Modal */}
      <AnimatePresence>
        {showLiveKitModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowLiveKitModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white dark:bg-gray-800 rounded-2xl p-8 max-w-md w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Video className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Start Live Session</h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Connect with your AI co-founder team through video and voice. 
                  Get real-time guidance and collaborative problem-solving.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div className="relative rounded-xl overflow-hidden aspect-video group cursor-pointer"
                       onClick={() => handleStartLiveKit('video')}>
                    <img 
                      src="https://images.pexels.com/photos/4226140/pexels-photo-4226140.jpeg?auto=compress&cs=tinysrgb&w=600" 
                      alt="Video call"
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.currentTarget.src = `https://placehold.co/600x400/1f2937/ffffff?text=Video+Call`;
                      }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end justify-center p-3">
                      <span className="text-white font-medium">Video Call</span>
                    </div>
                    <div className="absolute inset-0 bg-green-600/20 opacity-0 group-hover:opacity-100 flex items-center justify-center transition-opacity">
                      <div className="bg-white rounded-full p-2">
                        <Video className="w-6 h-6 text-green-600" />
                      </div>
                    </div>
                  </div>
                  
                  <div className="relative rounded-xl overflow-hidden aspect-video group cursor-pointer"
                       onClick={() => handleStartLiveKit('voice')}>
                    <img 
                      src="https://images.pexels.com/photos/7709020/pexels-photo-7709020.jpeg?auto=compress&cs=tinysrgb&w=600" 
                      alt="Voice call"
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.currentTarget.src = `https://placehold.co/600x400/1f2937/ffffff?text=Voice+Call`;
                      }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end justify-center p-3">
                      <span className="text-white font-medium">Voice Only</span>
                    </div>
                    <div className="absolute inset-0 bg-blue-600/20 opacity-0 group-hover:opacity-100 flex items-center justify-center transition-opacity">
                      <div className="bg-white rounded-full p-2">
                        <Phone className="w-6 h-6 text-blue-600" />
                      </div>
                    </div>
                  </div>
                  
                  <div className="relative rounded-xl overflow-hidden aspect-video group cursor-pointer"
                       onClick={() => handleStartLiveKit('screen-share')}>
                    <img 
                      src="https://images.pexels.com/photos/3182746/pexels-photo-3182746.jpeg?auto=compress&cs=tinysrgb&w=600" 
                      alt="Screen share"
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.currentTarget.src = `https://placehold.co/600x400/1f2937/ffffff?text=Screen+Share`;
                      }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end justify-center p-3">
                      <span className="text-white font-medium">Screen Share</span>
                    </div>
                    <div className="absolute inset-0 bg-purple-600/20 opacity-0 group-hover:opacity-100 flex items-center justify-center transition-opacity">
                      <div className="bg-white rounded-full p-2">
                        <Monitor className="w-6 h-6 text-purple-600" />
                      </div>
                    </div>
                  </div>
                </div>
                
                <button
                  onClick={() => setShowLiveKitModal(false)}
                  className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* API Key Warning */}
      {!hasApiKey && (
        <div className="fixed bottom-4 right-4 bg-yellow-100 dark:bg-yellow-900/50 border border-yellow-300 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200 p-4 rounded-lg shadow-lg max-w-md">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0">
              <Sparkles className="w-5 h-5" />
            </div>
            <div>
              <h4 className="font-medium mb-1">Demo Mode Active</h4>
              <p className="text-sm">
                You're currently using demo mode with simulated AI responses. For full functionality, add your API keys in the environment variables.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};