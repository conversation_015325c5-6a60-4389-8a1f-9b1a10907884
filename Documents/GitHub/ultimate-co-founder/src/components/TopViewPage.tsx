import React, { useState, useEffect } from 'react';
import { TopViewVideoCreator } from './TopViewVideoCreator';
import { topViewService, VideoResult } from '../services/topview';
import { toast } from 'react-hot-toast';

export const TopViewPage: React.FC = () => {
  const [healthStatus, setHealthStatus] = useState<any>(null);
  const [createdVideos, setCreatedVideos] = useState<VideoResult[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    checkHealthStatus();
  }, []);

  const checkHealthStatus = async () => {
    try {
      const status = await topViewService.getHealthStatus();
      setHealthStatus(status);
    } catch (error) {
      console.error('Failed to check health status:', error);
      toast.error('Failed to connect to TopView.ai service');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVideoCreated = (result: VideoResult) => {
    setCreatedVideos(prev => [result, ...prev]);
    toast.success('Video added to your collection!');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading TopView.ai...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            🎬 TopView.ai Video Studio
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Create professional AI-powered videos with TopView.ai integration. 
            Transform URLs, scripts, images, and materials into engaging video content.
          </p>
        </div>

        {/* Health Status */}
        {healthStatus && (
          <div className="mb-8">
            <div className={`p-4 rounded-lg ${
              healthStatus.status === 'healthy' 
                ? 'bg-green-50 border border-green-200' 
                : 'bg-red-50 border border-red-200'
            }`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <span className={`text-2xl mr-3 ${
                    healthStatus.status === 'healthy' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {healthStatus.status === 'healthy' ? '✅' : '❌'}
                  </span>
                  <div>
                    <h3 className={`font-semibold ${
                      healthStatus.status === 'healthy' ? 'text-green-900' : 'text-red-900'
                    }`}>
                      TopView.ai Service Status
                    </h3>
                    <p className={`text-sm ${
                      healthStatus.status === 'healthy' ? 'text-green-700' : 'text-red-700'
                    }`}>
                      {healthStatus.status === 'healthy' 
                        ? 'All systems operational' 
                        : 'Service unavailable'
                      }
                      {healthStatus.mock_mode && ' (Running in demo mode)'}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`text-sm ${
                    healthStatus.status === 'healthy' ? 'text-green-700' : 'text-red-700'
                  }`}>
                    API Key: {healthStatus.api_key_configured ? '✅ Configured' : '❌ Not configured'}
                  </p>
                  <p className={`text-xs ${
                    healthStatus.status === 'healthy' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {healthStatus.features?.length || 0} features available
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Features Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="text-3xl mb-3">🔗</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">URL to Video</h3>
            <p className="text-gray-600 text-sm">
              Convert any website URL into an engaging marketing video automatically.
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="text-3xl mb-3">🎭</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Video Avatar</h3>
            <p className="text-gray-600 text-sm">
              Create professional videos with AI avatars speaking your custom scripts.
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="text-3xl mb-3">🛍️</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Product Avatar</h3>
            <p className="text-gray-600 text-sm">
              Showcase products with AI avatars for compelling product demonstrations.
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="text-3xl mb-3">📁</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Materials to Video</h3>
            <p className="text-gray-600 text-sm">
              Combine multiple images and videos into cohesive marketing content.
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="text-3xl mb-3">🖼️</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Image to Video</h3>
            <p className="text-gray-600 text-sm">
              Animate static images with AI-powered motion and effects.
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="text-3xl mb-3">🤖</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">AI Integration</h3>
            <p className="text-gray-600 text-sm">
              Seamlessly integrated with Ultimate Co-founder AI agents for automated content creation.
            </p>
          </div>
        </div>

        {/* Video Creator */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <TopViewVideoCreator 
              onVideoCreated={handleVideoCreated}
              className="h-fit"
            />
          </div>
          
          {/* Video Gallery */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                🎥 Your Videos
              </h3>
              
              {createdVideos.length === 0 ? (
                <div className="text-center py-8">
                  <div className="text-4xl mb-4">🎬</div>
                  <p className="text-gray-500">
                    No videos created yet. Use the video creator to get started!
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {createdVideos.map((video, index) => (
                    <div key={video.taskId} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-900">
                          Video #{createdVideos.length - index}
                        </span>
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          video.status === 'success' 
                            ? 'bg-green-100 text-green-800'
                            : video.status === 'failed'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {video.status}
                        </span>
                      </div>
                      
                      {video.coverUrl && (
                        <img 
                          src={video.coverUrl} 
                          alt="Video thumbnail"
                          className="w-full h-24 object-cover rounded mb-2"
                        />
                      )}
                      
                      <p className="text-xs text-gray-500 mb-2">
                        Task ID: {video.taskId}
                      </p>
                      
                      {video.videoDuration && (
                        <p className="text-xs text-gray-500 mb-2">
                          Duration: {Math.round(video.videoDuration / 1000)}s
                        </p>
                      )}
                      
                      {video.videoUrl && (
                        <a
                          href={video.videoUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-blue-600 hover:text-blue-800 text-sm"
                        >
                          <span className="mr-1">📥</span>
                          Download Video
                        </a>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Integration Info */}
        <div className="mt-12 bg-blue-50 rounded-lg p-6">
          <h3 className="text-xl font-bold text-blue-900 mb-4">
            🤖 AI Agent Integration
          </h3>
          <p className="text-blue-800 mb-4">
            TopView.ai is fully integrated with the Ultimate Co-founder AI agents. 
            Your AI agents can automatically create videos for:
          </p>
          <ul className="list-disc list-inside text-blue-800 space-y-1">
            <li><strong>Marketing Agent:</strong> Product demos and promotional videos</li>
            <li><strong>Strategic Agent:</strong> Pitch decks and presentation videos</li>
            <li><strong>Product Agent:</strong> Feature explanations and tutorials</li>
            <li><strong>Operations Agent:</strong> Process documentation videos</li>
            <li><strong>Technical Agent:</strong> Technical concept explanations</li>
          </ul>
        </div>

        {/* API Information */}
        {healthStatus?.mock_mode && (
          <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-yellow-800 mb-2">
              ⚠️ Demo Mode Active
            </h3>
            <p className="text-yellow-700 mb-4">
              TopView.ai is currently running in demo mode with mock responses. 
              To enable full functionality:
            </p>
            <ol className="list-decimal list-inside text-yellow-700 space-y-1">
              <li>Sign up for a TopView.ai API account at <a href="https://www.topview.ai/openapi" target="_blank" rel="noopener noreferrer" className="underline">topview.ai/openapi</a></li>
              <li>Get your API key and UID from the dashboard</li>
              <li>Set the <code>TOPVIEW_API_KEY</code> and <code>TOPVIEW_UID</code> environment variables</li>
              <li>Restart the application to enable real video creation</li>
            </ol>
          </div>
        )}
      </div>
    </div>
  );
};
