import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAgentStore } from '../store/agentStore';
import { Send, Paperclip, Mic, MicOff, Users } from 'lucide-react';

export const ChatPanel: React.FC = () => {
  const { selectedAgent, messages, addMessage, setSelectedAgent } = useAgentStore();
  const [newMessage, setNewMessage] = React.useState('');
  const [isRecording, setIsRecording] = React.useState(false);
  const messagesEndRef = React.useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  React.useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = () => {
    if (!newMessage.trim()) return;

    const message = {
      id: Date.now().toString(),
      agentId: 'user',
      content: newMessage,
      timestamp: new Date(),
      type: 'text' as const
    };

    addMessage(message);
    setNewMessage('');

    // Simulate agent response
    setTimeout(() => {
      const response = {
        id: (Date.now() + 1).toString(),
        agentId: selectedAgent ? selectedAgent.id : 'strategic',
        content: selectedAgent 
          ? `Thanks for reaching out! I'm ${selectedAgent.name}, and I'm here to help with ${selectedAgent.role} matters. Let me analyze your request and provide you with actionable insights.`
          : "Thanks for your message! I've shared it with the entire co-founder team. We'll analyze your request from strategic, product, technical, operations, and marketing perspectives to provide comprehensive guidance.",
        timestamp: new Date(),
        type: 'text' as const
      };
      addMessage(response);
    }, 1000);
  };

  const toggleRecording = () => {
    setIsRecording(!isRecording);
    // TODO: Implement voice recording
  };

  if (!selectedAgent) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
          <Send className="w-8 h-8 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Select an Agent
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Choose a co-founder agent to start collaborating
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-96">
      {/* Chat Header */}
      <div className="flex items-center justify-between gap-3 pb-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center text-lg">
            {selectedAgent.avatar}
          </div>
          <div>
            <h3 className="font-medium text-gray-900 dark:text-white">
              {selectedAgent.name}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 capitalize">
              {selectedAgent.role} Co-founder
            </p>
          </div>
        </div>
        
        <button
          onClick={() => setSelectedAgent(null)}
          className="px-3 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-full text-xs font-medium flex items-center gap-1 hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-colors"
        >
          <Users size={12} />
          All Co-founders
        </button>
      </div>

      {/* Agent Indicator */}
      <div className="py-2 px-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg my-2 text-center">
        <p className="text-xs text-purple-700 dark:text-purple-300">
          Chatting with {selectedAgent.name} Co-founder
        </p>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto py-4 space-y-4">
        <AnimatePresence>
          {messages
            .filter(msg => msg.agentId === selectedAgent.id || msg.agentId === 'user')
            .map((message) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className={`flex ${message.agentId === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs p-3 rounded-lg ${
                    message.agentId === 'user'
                      ? 'bg-purple-600 text-white'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
                  }`}
                >
                  <p className="text-sm">{message.content}</p>
                  <p className={`text-xs mt-1 ${
                    message.agentId === 'user' 
                      ? 'text-purple-200' 
                      : 'text-gray-500 dark:text-gray-400'
                  }`}>
                    {message.timestamp.toLocaleTimeString([], { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </p>
                </div>
              </motion.div>
            ))}
        </AnimatePresence>
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
        <div className="flex items-center gap-2">
          <div className="flex-1 relative">
            <input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              placeholder={`Ask ${selectedAgent.name} about ${selectedAgent.role.toLowerCase()}...`}
              className="w-full px-4 py-2 pr-12 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
            />
            <button
              onClick={() => {}}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <Paperclip size={16} />
            </button>
          </div>
          
          <button
            onClick={toggleRecording}
            className={`p-2 rounded-lg transition-colors ${
              isRecording
                ? 'bg-red-500 hover:bg-red-600 text-white'
                : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-300 dark:hover:bg-gray-600'
            }`}
          >
            {isRecording ? <MicOff size={16} /> : <Mic size={16} />}
          </button>

          <button
            onClick={handleSendMessage}
            disabled={!newMessage.trim()}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-300 dark:disabled:bg-gray-600 text-white rounded-lg transition-colors disabled:cursor-not-allowed"
          >
            <Send size={16} />
          </button>
        </div>
      </div>
    </div>
  );
};