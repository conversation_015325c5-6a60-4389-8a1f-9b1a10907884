import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAgentStore } from '../store/agentStore';
import { 
  Video, 
  VideoOff, 
  Mic, 
  MicOff, 
  Phone, 
  PhoneOff, 
  Users,
  Settings,
  Monitor,
  Volume2,
  VolumeX
} from 'lucide-react';
import { tavus, TavusAvatar } from '../services/tavus';
import toast from 'react-hot-toast';
import { TavusAvatarView } from './TavusAvatarView';

// Agent-specific images for video call backgrounds
const agentBackgrounds = {
  strategic: 'https://images.pexels.com/photos/3184338/pexels-photo-3184338.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
  product: 'https://images.pexels.com/photos/3182812/pexels-photo-3182812.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
  technical: 'https://images.pexels.com/photos/3183150/pexels-photo-3183150.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
  operations: 'https://images.pexels.com/photos/3184292/pexels-photo-3184292.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
  marketing: 'https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
};

export const VideoCallPanel: React.FC = () => {
  const { agents, isVideoCallActive, toggleVideoCall } = useAgentStore();
  const [isCameraOn, setIsCameraOn] = useState(true);
  const [isMicOn, setIsMicOn] = useState(true);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [isAudioMuted, setIsAudioMuted] = useState(true);
  const [selectedAgentId, setSelectedAgentId] = useState<string | null>(null);
  const [agentAvatars, setAgentAvatars] = useState<Record<string, TavusAvatar>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [hasPermissions, setHasPermissions] = useState<boolean | null>(null);

  const activeAgents = agents.filter(agent => 
    agent.status === 'active' || agent.status === 'collaborating'
  );

  useEffect(() => {
    if (isVideoCallActive && activeAgents.length > 0) {
      loadAgentAvatars();
      
      // Set the first active agent as selected by default
      if (!selectedAgentId) {
        setSelectedAgentId(activeAgents[0].id);
      }
    }
  }, [isVideoCallActive, activeAgents]);

  const loadAgentAvatars = async () => {
    setIsLoading(true);
    try {
      const avatars: Record<string, TavusAvatar> = {};
      
      // Load avatars for all active agents
      for (const agent of activeAgents) {
        const avatar = await tavus.getAgentAvatar(agent.id);
        avatars[agent.id] = avatar;
      }
      
      setAgentAvatars(avatars);
    } catch (error) {
      console.error('Error loading agent avatars:', error);
      toast.error('Failed to load agent avatars');
    } finally {
      setIsLoading(false);
    }
  };

  const toggleAudioMute = () => {
    setIsAudioMuted(!isAudioMuted);
  };

  const getSelectedAgentAvatar = () => {
    if (!selectedAgentId || !agentAvatars[selectedAgentId]) {
      return null;
    }
    return agentAvatars[selectedAgentId];
  };

  const selectedAvatar = getSelectedAgentAvatar();
  const selectedAgentBackground = selectedAgentId ? agentBackgrounds[selectedAgentId as keyof typeof agentBackgrounds] : null;

  const requestDevicePermissions = async () => {
    try {
      // Request camera permission
      const cameraStream = await navigator.mediaDevices.getUserMedia({ video: true });
      
      // Request microphone permission
      const micStream = await navigator.mediaDevices.getUserMedia({ audio: true });
      
      // Stop the streams immediately after getting permissions
      cameraStream.getTracks().forEach(track => track.stop());
      micStream.getTracks().forEach(track => track.stop());
      
      setHasPermissions(true);
      return true;
    } catch (error) {
      console.error('Error requesting device permissions:', error);
      setHasPermissions(false);
      toast.error('Please allow camera and microphone access to start a video call');
      return false;
    }
  };

  const handleStartSession = async () => {
    const hasAccess = await requestDevicePermissions();
    if (hasAccess) {
      toggleVideoCall();
    }
  };

  return (
    <div className="space-y-4">
      {/* Video Call Status */}
      <div className="text-center">
        <div className={`w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center ${
          isVideoCallActive 
            ? 'bg-green-100 dark:bg-green-900/30' 
            : 'bg-gray-100 dark:bg-gray-700'
        }`}>
          {isVideoCallActive ? (
            <Video className="w-8 h-8 text-green-600" />
          ) : (
            <VideoOff className="w-8 h-8 text-gray-400" />
          )}
        </div>
        
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {isVideoCallActive ? 'Live Session Active' : 'Start Video Session'}
        </h3>
        
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {isVideoCallActive 
            ? `Connected with ${activeAgents.length} co-founder${activeAgents.length !== 1 ? 's' : ''}`
            : 'Connect with your AI co-founders via video'
          }
        </p>
      </div>

      {/* Video Preview */}
      {isVideoCallActive ? (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="space-y-4"
        >
          {/* Main Video */}
          <div className="relative bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg overflow-hidden aspect-video shadow-lg">
            {isLoading ? (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-white">
                  <div className="w-10 h-10 border-4 border-white border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                  <p className="text-sm">Loading avatars...</p>
                </div>
              </div>
            ) : selectedAvatar ? (
              <div className="absolute inset-0">
                {/* Background image for visual interest - use agent-specific background */}
                <div className="absolute inset-0 opacity-10">
                  <img 
                    src={selectedAgentBackground || "https://images.pexels.com/photos/3861958/pexels-photo-3861958.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"} 
                    alt="Background" 
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.src = `https://placehold.co/1200x800/1f2937/ffffff?text=Video+Call`;
                    }}
                  />
                </div>
                
                <TavusAvatarView 
                  avatar={selectedAvatar}
                  muted={isAudioMuted}
                  onMuteChange={setIsAudioMuted}
                  className="w-full h-full"
                />
              </div>
            ) : (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-white">
                  <Video className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p className="text-sm opacity-75">Select an agent to view their avatar</p>
                </div>
              </div>
            )}

            {/* Video Controls Overlay */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center gap-2">
              <button
                onClick={() => setIsCameraOn(!isCameraOn)}
                className={`p-2 rounded-full transition-colors ${
                  isCameraOn 
                    ? 'bg-gray-800/80 text-white hover:bg-gray-700/80' 
                    : 'bg-red-500 text-white hover:bg-red-600'
                }`}
              >
                {isCameraOn ? <Video size={16} /> : <VideoOff size={16} />}
              </button>
              
              <button
                onClick={() => setIsMicOn(!isMicOn)}
                className={`p-2 rounded-full transition-colors ${
                  isMicOn 
                    ? 'bg-gray-800/80 text-white hover:bg-gray-700/80' 
                    : 'bg-red-500 text-white hover:bg-red-600'
                }`}
              >
                {isMicOn ? <Mic size={16} /> : <MicOff size={16} />}
              </button>
              
              <button
                onClick={() => setIsScreenSharing(!isScreenSharing)}
                className={`p-2 rounded-full transition-colors ${
                  isScreenSharing 
                    ? 'bg-blue-500 text-white hover:bg-blue-600' 
                    : 'bg-gray-800/80 text-white hover:bg-gray-700/80'
                }`}
              >
                <Monitor size={16} />
              </button>

              <button
                onClick={toggleAudioMute}
                className={`p-2 rounded-full transition-colors ${
                  isAudioMuted
                    ? 'bg-red-500 text-white hover:bg-red-600'
                    : 'bg-gray-800/80 text-white hover:bg-gray-700/80'
                }`}
              >
                {isAudioMuted ? <VolumeX size={16} /> : <Volume2 size={16} />}
              </button>
            </div>
          </div>

          {/* Agent Selection */}
          <div className="flex items-center gap-2 overflow-x-auto pb-2">
            {Object.entries(agentAvatars).map(([agentId, avatar]) => {
              // Get agent-specific background image
              const agentBackground = agentBackgrounds[agentId as keyof typeof agentBackgrounds];
              
              return (
                <button
                  key={agentId}
                  onClick={() => setSelectedAgentId(agentId)}
                  className={`relative flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden ${
                    selectedAgentId === agentId ? 'ring-2 ring-purple-500' : ''
                  }`}
                >
                  <img 
                    src={agentBackground || avatar.thumbnailUrl} 
                    alt={avatar.name}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      console.error('Failed to load thumbnail:', avatar.thumbnailUrl);
                      e.currentTarget.src = `https://placehold.co/150x150/1f2937/ffffff?text=${avatar.name.charAt(0)}`;
                    }}
                  />
                  <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white text-xs py-0.5 text-center truncate">
                    {avatar.name.split(' ')[0]}
                  </div>
                </button>
              );
            })}
          </div>
        </motion.div>
      ) : (
        <div className="relative rounded-lg overflow-hidden aspect-video shadow-lg">
          <img 
            src="https://images.pexels.com/photos/4226140/pexels-photo-4226140.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1" 
            alt="Video call preview" 
            className="w-full h-full object-cover"
            onError={(e) => {
              e.currentTarget.src = `https://placehold.co/800x450/1f2937/ffffff?text=Start+Video+Call`;
            }}
          />
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
            <div className="text-center text-white">
              <Video className="w-16 h-16 mx-auto mb-4 opacity-70" />
              <p className="text-xl font-bold mb-2">Start a Video Call</p>
              <p className="text-sm opacity-80 max-w-xs mx-auto">Connect with your AI co-founders for real-time collaboration</p>
            </div>
          </div>
        </div>
      )}

      {/* Call Controls */}
      <div className="flex items-center justify-center gap-4">
        <button
          onClick={isVideoCallActive ? toggleVideoCall : handleStartSession}
          className={`flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-colors ${
            isVideoCallActive
              ? 'bg-red-500 hover:bg-red-600 text-white'
              : 'bg-green-500 hover:bg-green-600 text-white'
          }`}
        >
          {isVideoCallActive ? (
            <>
              <PhoneOff size={16} />
              End Session
            </>
          ) : (
            <>
              <Phone size={16} />
              Start Session
            </>
          )}
        </button>
        
        <button className="p-3 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors">
          <Settings size={16} />
        </button>
      </div>

      {/* Quick Actions */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
        <h4 className="font-medium text-gray-900 dark:text-white mb-3">Quick Actions</h4>
        <div className="grid grid-cols-2 gap-2">
          <button className="p-3 text-left bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors">
            <p className="font-medium text-gray-900 dark:text-white text-sm">Schedule Meeting</p>
            <p className="text-xs text-gray-600 dark:text-gray-400">Plan ahead</p>
          </button>
          <button className="p-3 text-left bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors">
            <p className="font-medium text-gray-900 dark:text-white text-sm">Record Session</p>
            <p className="text-xs text-gray-600 dark:text-gray-400">Save insights</p>
          </button>
        </div>
      </div>

      {/* Permissions Modal */}
      {hasPermissions === false && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full p-6">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <VideoOff className="w-8 h-8 text-red-600 dark:text-red-400" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                Camera and Microphone Access Required
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Please allow access to your camera and microphone to start a video call with your AI co-founders.
              </p>
            </div>
            <div className="space-y-4">
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">How to enable permissions:</h4>
                <ol className="list-decimal list-inside space-y-2 text-sm text-gray-600 dark:text-gray-400">
                  <li>Click the camera/lock icon in your browser's address bar</li>
                  <li>Select "Allow" for both camera and microphone</li>
                  <li>Refresh the page and try again</li>
                </ol>
              </div>
              <div className="flex gap-3">
                <button
                  onClick={() => setHasPermissions(null)}
                  className="flex-1 px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={async () => {
                    const result = await requestDevicePermissions();
                    if (result) {
                      toggleVideoCall();
                    }
                  }}
                  className="flex-1 px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors"
                >
                  Try Again
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};