import { apiService } from './api';
import toast from 'react-hot-toast';

export interface Agent {
  id: string;
  name: string;
  role: string;
  goal: string;
  backstory: string;
  tools: string[];
  status: string;
}

export interface Task {
  id: string;
  description: string;
  agent: string;
  expected_output: string;
  context?: string[];
}

export interface CrewResult {
  task_id: string;
  agent_id: string;
  result: string;
  metadata: {
    execution_time: number;
    tokens_used?: number;
    model: string;
    status: string;
  };
}

class CrewAIService {
  private apiKey: string = import.meta.env.VITE_OPENAI_API_KEY || '';
  
  async executeTask(agentId: string, taskDescription: string, context?: string): Promise<CrewResult> {
    try {
      // Check if we have an API key
      if (!this.apiKey) {
        console.warn('No OpenAI API key provided. Using mock response.');
        return this.getMockTaskResult(agentId, taskDescription);
      }
      
      const response = await apiService.post(`/api/v1/agents/execute`, {
        agent_id: agentId,
        description: taskDescription,
        context
      });
      return response;
    } catch (error) {
      console.error(`<PERSON>rror executing task for agent ${agentId}:`, error);
      toast.error(`Error executing task: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      // Return mock data in case of error
      return this.getMockTaskResult(agentId, taskDescription);
    }
  }

  async executeMultiAgentTask(taskDescription: string, agentIds: string[]): Promise<CrewResult[]> {
    try {
      // Check if we have an API key
      if (!this.apiKey) {
        console.warn('No OpenAI API key provided. Using mock response.');
        return agentIds.map(agentId => this.getMockTaskResult(agentId, taskDescription));
      }
      
      const response = await apiService.post('/api/v1/agents/execute-multi', {
        description: taskDescription,
        agent_ids: agentIds
      });
      return response.results;
    } catch (error) {
      console.error('Error executing multi-agent task:', error);
      toast.error(`Error executing multi-agent task: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      // Return mock data in case of error
      return agentIds.map(agentId => this.getMockTaskResult(agentId, taskDescription));
    }
  }

  async getAgents(): Promise<Agent[]> {
    try {
      const response = await apiService.get('/api/v1/agents');
      return response.agents;
    } catch (error) {
      console.error('Error getting agents:', error);
      
      // Return mock data in case of error
      return [
        {
          id: "strategic",
          name: "Alex Strategic",
          role: "Strategic Co-founder",
          goal: "Analyze market opportunities and develop comprehensive business strategies",
          backstory: "Former McKinsey consultant with expertise in market analysis",
          tools: ["Market Research", "Competitive Analysis"],
          status: "active"
        },
        {
          id: "product",
          name: "Sam Product",
          role: "Product Co-founder",
          goal: "Design user-centric products and validate market assumptions",
          backstory: "Former product lead at Google with expertise in UX design",
          tools: ["User Research", "Feature Prioritization"],
          status: "active"
        },
        {
          id: "technical",
          name: "Taylor Tech",
          role: "Technical Co-founder",
          goal: "Architect scalable systems and generate production-ready code",
          backstory: "Former CTO with expertise in system architecture",
          tools: ["Code Generation", "Architecture Design"],
          status: "active"
        },
        {
          id: "operations",
          name: "Jordan Ops",
          role: "Operations Co-founder",
          goal: "Establish operational excellence and ensure legal compliance",
          backstory: "Former COO with expertise in process optimization",
          tools: ["Process Design", "Legal Compliance"],
          status: "active"
        },
        {
          id: "marketing",
          name: "Morgan Marketing",
          role: "Marketing Co-founder",
          goal: "Drive growth through strategic marketing and brand building",
          backstory: "Former CMO with expertise in growth marketing",
          tools: ["Growth Strategy", "Brand Building"],
          status: "active"
        }
      ];
    }
  }

  async getAgent(agentId: string): Promise<Agent> {
    try {
      return await apiService.get(`/api/v1/agents/${agentId}`);
    } catch (error) {
      console.error(`Error getting agent ${agentId}:`, error);
      
      // Return mock data in case of error
      const mockAgents = {
        strategic: {
          id: "strategic",
          name: "Alex Strategic",
          role: "Strategic Co-founder",
          goal: "Analyze market opportunities and develop comprehensive business strategies",
          backstory: "Former McKinsey consultant with expertise in market analysis",
          tools: ["Market Research", "Competitive Analysis"],
          status: "active"
        },
        product: {
          id: "product",
          name: "Sam Product",
          role: "Product Co-founder",
          goal: "Design user-centric products and validate market assumptions",
          backstory: "Former product lead at Google with expertise in UX design",
          tools: ["User Research", "Feature Prioritization"],
          status: "active"
        },
        technical: {
          id: "technical",
          name: "Taylor Tech",
          role: "Technical Co-founder",
          goal: "Architect scalable systems and generate production-ready code",
          backstory: "Former CTO with expertise in system architecture",
          tools: ["Code Generation", "Architecture Design"],
          status: "active"
        },
        operations: {
          id: "operations",
          name: "Jordan Ops",
          role: "Operations Co-founder",
          goal: "Establish operational excellence and ensure legal compliance",
          backstory: "Former COO with expertise in process optimization",
          tools: ["Process Design", "Legal Compliance"],
          status: "active"
        },
        marketing: {
          id: "marketing",
          name: "Morgan Marketing",
          role: "Marketing Co-founder",
          goal: "Drive growth through strategic marketing and brand building",
          backstory: "Former CMO with expertise in growth marketing",
          tools: ["Growth Strategy", "Brand Building"],
          status: "active"
        }
      };
      
      return mockAgents[agentId as keyof typeof mockAgents] || {
        id: agentId,
        name: `Unknown Agent (${agentId})`,
        role: "Unknown Role",
        goal: "Unknown Goal",
        backstory: "Unknown Backstory",
        tools: [],
        status: "unknown"
      };
    }
  }

  async updateAgentStatus(agentId: string, status: string): Promise<void> {
    try {
      await apiService.post(`/api/v1/agents/${agentId}/status`, { status });
    } catch (error) {
      console.error(`Error updating agent ${agentId} status:`, error);
      // Silently fail in case of error
    }
  }

  private getMockTaskResult(agentId: string, taskDescription: string): CrewResult {
    const mockResponses: Record<string, string> = {
      strategic: `# Strategic Analysis

Based on your request, I've analyzed the market opportunity and competitive landscape for your idea.

## Market Analysis
- The global market size for this type of solution is approximately $2.3B and growing at 15% annually
- Key customer segments include small businesses (40%), mid-market companies (35%), and enterprises (25%)
- Primary pain points include manual processes, lack of integration, and poor data visibility

## Competitive Landscape
- 3 direct competitors with similar offerings, but all lacking key features you've mentioned
- 5 indirect competitors focusing on adjacent problems
- Key differentiation opportunities in AI automation, user experience, and pricing model

## Strategic Recommendations
1. Focus on the small business segment initially for faster adoption
2. Develop a freemium model to accelerate user acquisition
3. Prioritize integrations with popular tools in your target market
4. Consider a partnership strategy with complementary service providers

Let me know if you'd like me to dive deeper into any specific aspect of this analysis.`,

      product: `# Product Strategy

I've analyzed your product concept and developed recommendations for your roadmap.

## User Personas
1. **Sarah (Primary)** - Small business owner, time-constrained, needs simple solutions
2. **Michael** - Operations manager at mid-size company, needs detailed reporting
3. **Jennifer** - Enterprise IT decision maker, concerned with security and compliance

## Feature Prioritization (RICE Framework)
| Feature | Reach | Impact | Confidence | Effort | Score |
|---------|-------|--------|------------|--------|-------|
| User authentication | 10 | 3 | 10 | 1 | 300 |
| Dashboard | 10 | 8 | 9 | 3 | 240 |
| Reporting | 8 | 7 | 8 | 5 | 89.6 |
| Admin panel | 3 | 6 | 9 | 4 | 40.5 |

## Recommended MVP Scope
- User authentication and profile management
- Basic dashboard with key metrics
- Core functionality with minimal features
- Simple reporting capabilities

## User Testing Plan
1. Create interactive prototype in Figma
2. Conduct 5-7 user interviews with target personas
3. Iterate based on feedback before development

Would you like me to elaborate on any of these areas or discuss specific features in more detail?`,

      technical: `# Technical Architecture Recommendation

Based on your requirements, here's my recommended technical approach:

## Tech Stack
- **Frontend**: React with TypeScript and Tailwind CSS
- **Backend**: Node.js with Express
- **Database**: PostgreSQL for relational data
- **Caching**: Redis for performance optimization
- **Authentication**: JWT with refresh tokens
- **Hosting**: AWS (ECS for containers, RDS for database)

## Architecture Diagram
` + '```' + `
Client → API Gateway → Microservices → Database
                     ↓
             Authentication Service
                     ↓
              Background Workers
` + '```' + `

## Scalability Considerations
- Horizontal scaling for API services
- Database read replicas for query performance
- CDN for static assets
- Rate limiting and caching strategies

## Security Recommendations
- Implement OWASP security best practices
- Regular security audits and penetration testing
- Data encryption at rest and in transit
- Proper secrets management with AWS Secrets Manager

## Development Roadmap
1. Set up CI/CD pipeline with GitHub Actions
2. Develop authentication service
3. Implement core API functionality
4. Create frontend components and pages
5. Set up monitoring and logging

Would you like me to generate any starter code or elaborate on any specific aspect of this architecture?`,

      operations: `# Operations Framework

I've developed an operations framework for your business:

## Legal Structure
- Recommended entity: Delaware C-Corporation
- Key legal documents needed:
  - Certificate of Incorporation
  - Bylaws
  - Founder agreements
  - Employee agreements with IP assignment
  - Privacy policy and terms of service

## Financial Projections
| Category | Year 1 | Year 2 | Year 3 |
|----------|--------|--------|--------|
| Revenue | $250K | $1.2M | $3.5M |
| Expenses | $400K | $900K | $2.1M |
| Net Income | -$150K | $300K | $1.4M |

## Hiring Plan
1. **Month 1-3**: Technical co-founder, 2 developers
2. **Month 4-6**: Product manager, designer
3. **Month 7-12**: Sales lead, customer success, additional developers

## Operational Processes
- Agile development with 2-week sprints
- Monthly OKR planning and review
- Quarterly strategic planning sessions
- Annual budgeting and resource allocation

## Risk Management
- Identified 5 key business risks with mitigation strategies
- Compliance requirements mapped for your industry
- Insurance recommendations

Would you like me to elaborate on any specific operational area?`,

      marketing: `# Marketing Strategy

I've developed a comprehensive marketing strategy for your product launch:

## Brand Positioning
- **Tagline**: "Simplify. Automate. Grow."
- **Value Proposition**: "The all-in-one solution that saves small businesses 10+ hours per week"
- **Brand Voice**: Professional yet approachable, solution-oriented, empowering

## Customer Acquisition Channels
1. **Content Marketing** (40% of budget)
   - SEO-optimized blog content
   - Industry guides and whitepapers
   - Video tutorials and case studies

2. **Paid Acquisition** (30% of budget)
   - Google Ads targeting high-intent keywords
   - LinkedIn for B2B targeting
   - Retargeting campaigns for website visitors

3. **Partnerships** (20% of budget)
   - Co-marketing with complementary tools
   - Industry influencer collaborations
   - Affiliate program for referrals

4. **Community Building** (10% of budget)
   - Active presence in relevant online communities
   - Webinars and virtual events
   - User-generated content initiatives

## Launch Plan
- **Pre-launch**: Email list building, teaser content
- **Launch**: PR push, product hunt campaign, special offers
- **Post-launch**: Case study development, testimonial collection

## Growth Metrics
- CAC target: $200 per customer
- LTV target: $2,000+ per customer
- 3-month payback period goal

Would you like me to develop any specific component of this strategy in more detail?`
    };

    // Generate a response based on the agent type
    const result = mockResponses[agentId] || `I've analyzed your request: "${taskDescription}" and here are my thoughts as the ${agentId} co-founder...`;

    return {
      task_id: `task_${Date.now()}`,
      agent_id: agentId,
      result: result,
      metadata: {
        execution_time: 2500,
        tokens_used: 1500,
        model: "gpt-4",
        status: "completed"
      }
    };
  }
}

export const crewAI = new CrewAIService();