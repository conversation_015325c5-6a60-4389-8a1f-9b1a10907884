import { apiService } from './api';
import toast from 'react-hot-toast';

export interface GeneratedDocument {
  id: string;
  type: 'document' | 'slide' | 'sheet' | 'podcast';
  title: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  status: 'draft' | 'generated' | 'error';
  downloadUrl?: string;
  previewUrl?: string;
}

export interface GenerateDocumentRequest {
  type: 'document' | 'slide' | 'sheet' | 'podcast';
  title: string;
  prompt: string;
  template?: string;
  additionalContext?: string;
}

class DocumentGeneratorService {
  private apiUrl: string = 'https://api.document-generator.com';
  private apiKey: string = import.meta.env.VITE_DOCUMENT_GENERATOR_API_KEY || '';
  private githubRepo: string = 'https://github.com/DocumentGenerator';

  async generateDocument(request: GenerateDocumentRequest): Promise<GeneratedDocument> {
    try {
      // Try to use the actual API if available
      if (this.apiKey) {
        const response = await apiService.post('/api/v1/documents/generate', request);
        return response;
      }
      
      // If no API key, use mock response
      return this.mockGenerateDocument(request);
    } catch (error) {
      console.error('Error generating document:', error);
      toast.error('Failed to generate document');
      
      // Return mock response on error
      return this.mockGenerateDocument(request);
    }
  }

  async getDocument(documentId: string): Promise<GeneratedDocument> {
    try {
      if (this.apiKey) {
        return await apiService.get(`/api/v1/documents/${documentId}`);
      }
      
      // Mock response
      return {
        id: documentId,
        type: 'document',
        title: 'Sample Document',
        content: 'This is a sample document generated by our AI.',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: 'generated',
        downloadUrl: `${this.githubRepo}/sample-document.docx`,
        previewUrl: `${this.githubRepo}/sample-document-preview.html`
      };
    } catch (error) {
      console.error(`Error getting document ${documentId}:`, error);
      throw error;
    }
  }

  async listDocuments(): Promise<GeneratedDocument[]> {
    try {
      if (this.apiKey) {
        const response = await apiService.get('/api/v1/documents');
        return response.documents;
      }
      
      // Return mock data if API fails
      return [
        {
          id: `doc_${Date.now() - 86400000}`,
          type: 'document',
          title: 'Business Plan',
          content: 'Comprehensive business plan for startup.',
          createdAt: new Date(Date.now() - 86400000).toISOString(),
          updatedAt: new Date(Date.now() - 86400000).toISOString(),
          status: 'generated',
          downloadUrl: `${this.githubRepo}/business-plan.docx`,
          previewUrl: `${this.githubRepo}/business-plan-preview.html`
        },
        {
          id: `slide_${Date.now() - 43200000}`,
          type: 'slide',
          title: 'Investor Pitch Deck',
          content: 'Pitch deck for investor presentation.',
          createdAt: new Date(Date.now() - 43200000).toISOString(),
          updatedAt: new Date(Date.now() - 43200000).toISOString(),
          status: 'generated',
          downloadUrl: `${this.githubRepo}/investor-pitch.pptx`,
          previewUrl: `${this.githubRepo}/investor-pitch-preview.html`
        },
        {
          id: `sheet_${Date.now() - 21600000}`,
          type: 'sheet',
          title: 'Financial Projections',
          content: 'Three-year financial projections.',
          createdAt: new Date(Date.now() - 21600000).toISOString(),
          updatedAt: new Date(Date.now() - 21600000).toISOString(),
          status: 'generated',
          downloadUrl: `${this.githubRepo}/financial-projections.xlsx`,
          previewUrl: `${this.githubRepo}/financial-projections-preview.html`
        },
        {
          id: `podcast_${Date.now()}`,
          type: 'podcast',
          title: 'Startup Journey Episode 1',
          content: 'First episode of startup journey podcast.',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          status: 'generated',
          downloadUrl: `${this.githubRepo}/startup-journey-ep1.mp3`,
          previewUrl: `${this.githubRepo}/startup-journey-ep1-preview.html`
        }
      ];
    } catch (error) {
      console.error('Error listing documents:', error);
      return [];
    }
  }

  private mockGenerateDocument(request: GenerateDocumentRequest): GeneratedDocument {
    const id = `${request.type}_${Date.now()}`;
    const fileExtension = this.getFileExtension(request.type);
    
    return {
      id,
      type: request.type,
      title: request.title,
      content: `Generated content based on prompt: ${request.prompt}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: 'generated',
      downloadUrl: `${this.githubRepo}/${request.title.toLowerCase().replace(/\s+/g, '-')}.${fileExtension}`,
      previewUrl: `${this.githubRepo}/${request.title.toLowerCase().replace(/\s+/g, '-')}-preview.html`
    };
  }

  private getFileExtension(type: string): string {
    switch (type) {
      case 'document':
        return 'docx';
      case 'slide':
        return 'pptx';
      case 'sheet':
        return 'xlsx';
      case 'podcast':
        return 'mp3';
      default:
        return 'txt';
    }
  }
}

export const documentGenerator = new DocumentGeneratorService();