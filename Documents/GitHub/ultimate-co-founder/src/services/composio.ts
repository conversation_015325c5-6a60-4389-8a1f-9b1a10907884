import { apiService } from './api';

export interface ComposioIntegration {
  id: string;
  name: string;
  description: string;
  category: string;
  status: 'connected' | 'disconnected' | 'error';
  config: Record<string, any>;
  last_sync?: string;
}

export interface ComposioAction {
  id: string;
  integration: string;
  action: string;
  parameters: Record<string, any>;
  result?: any;
  status: 'pending' | 'running' | 'completed' | 'failed';
}

class ComposioService {
  private apiKey: string = import.meta.env.VITE_COMPOSIO_API_KEY || '';
  private mockIntegrations: Record<string, ComposioIntegration> = {
    slack: {
      id: 'slack',
      name: 'Slack',
      description: 'Team communication and notifications',
      category: 'Communication',
      status: 'connected',
      config: {
        workspace: 'ultimate-cofounder',
        channels: ['#general', '#development', '#marketing']
      },
      last_sync: new Date().toISOString()
    },
    github: {
      id: 'github',
      name: 'GitHub',
      description: 'Code repository and version control',
      category: 'Development',
      status: 'connected',
      config: {
        organization: 'ultimate-cofounder',
        repositories: ['main-app', 'ai-agents', 'documentation']
      },
      last_sync: new Date().toISOString()
    },
    notion: {
      id: 'notion',
      name: 'Notion',
      description: 'Documentation and knowledge base',
      category: 'Productivity',
      status: 'disconnected',
      config: {}
    },
    'google-drive': {
      id: 'google-drive',
      name: 'Google Drive',
      description: 'File storage and collaboration',
      category: 'Storage',
      status: 'connected',
      config: {
        folders: ['Data Room', 'Documents', 'Presentations']
      },
      last_sync: new Date().toISOString()
    }
  };

  async getIntegrations(): Promise<ComposioIntegration[]> {
    try {
      const response = await apiService.get('/api/v1/integrations');
      return response.integrations;
    } catch (error) {
      console.error('Error getting integrations:', error);
      // Return mock integrations if API fails
      return Object.values(this.mockIntegrations);
    }
  }

  async connectIntegration(integrationId: string, config: Record<string, any>): Promise<ComposioIntegration> {
    try {
      const response = await apiService.post(`/api/v1/integrations/${integrationId}/connect`, {
        config
      });
      return response.integration;
    } catch (error) {
      console.error(`Error connecting integration ${integrationId}:`, error);
      
      // Update mock integration if API fails
      if (this.mockIntegrations[integrationId]) {
        this.mockIntegrations[integrationId] = {
          ...this.mockIntegrations[integrationId],
          status: 'connected',
          config: { ...this.mockIntegrations[integrationId].config, ...config },
          last_sync: new Date().toISOString()
        };
        return this.mockIntegrations[integrationId];
      }
      
      throw error;
    }
  }

  async disconnectIntegration(integrationId: string): Promise<void> {
    try {
      await apiService.post(`/api/v1/integrations/${integrationId}/disconnect`);
    } catch (error) {
      console.error(`Error disconnecting integration ${integrationId}:`, error);
      
      // Update mock integration if API fails
      if (this.mockIntegrations[integrationId]) {
        this.mockIntegrations[integrationId] = {
          ...this.mockIntegrations[integrationId],
          status: 'disconnected',
          last_sync: undefined
        };
      }
    }
  }

  async executeAction(integrationId: string, action: string, parameters: Record<string, any>): Promise<any> {
    try {
      const response = await apiService.post(`/api/v1/integrations/${integrationId}/execute`, {
        action,
        parameters
      });
      return response;
    } catch (error) {
      console.error(`Error executing action ${action} on ${integrationId}:`, error);
      
      // Return mock response if API fails
      return {
        success: true,
        action_id: `action_${Date.now()}`,
        integration: integrationId,
        action,
        parameters,
        result: `Mock result for ${action} on ${integrationId}`,
        timestamp: new Date().toISOString()
      };
    }
  }

  async syncIntegration(integrationId: string): Promise<void> {
    try {
      await apiService.post(`/api/v1/integrations/${integrationId}/sync`);
    } catch (error) {
      console.error(`Error syncing integration ${integrationId}:`, error);
      
      // Update mock integration if API fails
      if (this.mockIntegrations[integrationId] && this.mockIntegrations[integrationId].status === 'connected') {
        this.mockIntegrations[integrationId] = {
          ...this.mockIntegrations[integrationId],
          last_sync: new Date().toISOString()
        };
      }
    }
  }

  async getIntegration(integrationId: string): Promise<ComposioIntegration> {
    try {
      return await apiService.get(`/api/v1/integrations/${integrationId}`);
    } catch (error) {
      console.error(`Error getting integration ${integrationId}:`, error);
      
      // Return mock integration if API fails
      if (this.mockIntegrations[integrationId]) {
        return this.mockIntegrations[integrationId];
      }
      
      throw error;
    }
  }
}

export const composio = new ComposioService();