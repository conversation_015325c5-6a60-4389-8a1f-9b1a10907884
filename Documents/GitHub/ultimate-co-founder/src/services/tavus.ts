import { apiService } from './api';

export interface TavusAvatar {
  id: string;
  name: string;
  role: string;
  videoUrl: string;
  thumbnailUrl: string;
  status: 'ready' | 'processing' | 'error';
}

class TavusService {
  private apiKey: string = import.meta.env.VITE_TAVUS_API_KEY || '';
  
  // Map of agent IDs to their Tavus avatar images
  private agentAvatars: Record<string, TavusAvatar> = {
    strategic: {
      id: 'strategic-avatar',
      name: '<PERSON>',
      role: 'Strategic Co-founder',
      videoUrl: 'https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4',
      thumbnailUrl: 'https://picsum.photos/400/300?random=1',
      status: 'ready'
    },
    product: {
      id: 'product-avatar',
      name: '<PERSON>',
      role: 'Product Co-founder',
      videoUrl: 'https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4',
      thumbnailUrl: 'https://picsum.photos/400/300?random=2',
      status: 'ready'
    },
    technical: {
      id: 'technical-avatar',
      name: '<PERSON>',
      role: 'Technical Co-founder',
      videoUrl: 'https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4',
      thumbnailUrl: 'https://picsum.photos/400/300?random=3',
      status: 'ready'
    },
    operations: {
      id: 'operations-avatar',
      name: 'Jordan Ops',
      role: 'Operations Co-founder',
      videoUrl: 'https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4',
      thumbnailUrl: 'https://picsum.photos/400/300?random=4',
      status: 'ready'
    },
    marketing: {
      id: 'marketing-avatar',
      name: 'Morgan Marketing',
      role: 'Marketing Co-founder',
      videoUrl: 'https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4',
      thumbnailUrl: 'https://picsum.photos/400/300?random=5',
      status: 'ready'
    }
  };

  async getAgentAvatar(agentId: string): Promise<TavusAvatar> {
    try {
      // If we have a real API key, make the API call
      if (this.apiKey) {
        const response = await apiService.get(`/api/v1/tavus/avatars/${agentId}`);
        return response;
      }
      
      // Otherwise return the mock avatar
      if (this.agentAvatars[agentId]) {
        return this.agentAvatars[agentId];
      }
      
      // If no avatar exists for this agent, return a default one
      return {
        id: `${agentId}-avatar`,
        name: this.getAgentName(agentId),
        role: this.getAgentRole(agentId),
        videoUrl: 'https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4',
        thumbnailUrl: `https://picsum.photos/400/300?random=${Math.floor(Math.random() * 100) + 10}`,
        status: 'ready'
      };
    } catch (error) {
      console.error(`Error getting Tavus avatar for agent ${agentId}:`, error);
      
      // Return a default avatar on error
      return {
        id: `${agentId}-avatar`,
        name: this.getAgentName(agentId),
        role: this.getAgentRole(agentId),
        videoUrl: 'https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4',
        thumbnailUrl: `https://picsum.photos/400/300?random=${Math.floor(Math.random() * 100) + 50}`,
        status: 'ready'
      };
    }
  }

  async generateAvatarVideo(agentId: string, script: string): Promise<string> {
    try {
      // If we have a real API key, make the API call
      if (this.apiKey) {
        const response = await apiService.post('/api/v1/tavus/generate', {
          agent_id: agentId,
          script
        });
        return response.video_url;
      }
      
      // Otherwise return a mock video URL
      console.log(`Mock generating avatar video for ${agentId} with script: ${script}`);
      return this.agentAvatars[agentId]?.videoUrl || 'https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4';
    } catch (error) {
      console.error(`Error generating Tavus avatar video for agent ${agentId}:`, error);
      throw error;
    }
  }

  private getAgentName(agentId: string): string {
    const agentNames: Record<string, string> = {
      strategic: 'Alex Strategic',
      product: 'Sam Product',
      technical: 'Taylor Tech',
      operations: 'Jordan Ops',
      marketing: 'Morgan Marketing'
    };
    
    return agentNames[agentId] || `${agentId.charAt(0).toUpperCase() + agentId.slice(1)} Agent`;
  }

  private getAgentRole(agentId: string): string {
    const agentRoles: Record<string, string> = {
      strategic: 'Strategic Co-founder',
      product: 'Product Co-founder',
      technical: 'Technical Co-founder',
      operations: 'Operations Co-founder',
      marketing: 'Marketing Co-founder'
    };
    
    return agentRoles[agentId] || `${agentId.charAt(0).toUpperCase() + agentId.slice(1)} Co-founder`;
  }
}

export const tavus = new TavusService();