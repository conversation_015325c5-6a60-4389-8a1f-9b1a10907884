import { apiService } from './api';
import toast from 'react-hot-toast';

export interface ApolloLead {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  company: string;
  title: string;
  phone?: string;
  linkedin?: string;
  industry?: string;
  companySize?: string;
  location?: string;
  tags: string[];
  lastContactedAt?: string;
  createdAt: string;
}

export interface LeadSearchParams {
  industry?: string;
  companySize?: string;
  location?: string;
  title?: string;
  keywords?: string[];
  page?: number;
  limit?: number;
}

export interface LeadCollection {
  id: string;
  name: string;
  description: string;
  leadCount: number;
  createdAt: string;
  updatedAt: string;
}

class ApolloService {
  private apiKey: string = import.meta.env.VITE_APOLLO_API_KEY || '';
  
  async searchLeads(params: LeadSearchParams): Promise<ApolloLead[]> {
    try {
      // If we have a real API key, make the API call
      if (this.apiKey) {
        const response = await apiService.post('/api/v1/apollo/search', params);
        return response.leads;
      }
      
      // Otherwise return mock data
      return this.getMockLeads(params);
    } catch (error) {
      console.error('Error searching Apollo leads:', error);
      toast.error('Failed to search leads');
      
      // Return mock data on error
      return this.getMockLeads(params);
    }
  }

  async createLeadCollection(name: string, description: string): Promise<LeadCollection> {
    try {
      if (this.apiKey) {
        const response = await apiService.post('/api/v1/apollo/collections', {
          name,
          description
        });
        return response;
      }
      
      // Return mock data
      return {
        id: `collection_${Date.now()}`,
        name,
        description,
        leadCount: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error creating lead collection:', error);
      toast.error('Failed to create lead collection');
      throw error;
    }
  }

  async addLeadsToCollection(collectionId: string, leadIds: string[]): Promise<{ success: boolean; count: number }> {
    try {
      if (this.apiKey) {
        const response = await apiService.post(`/api/v1/apollo/collections/${collectionId}/leads`, {
          lead_ids: leadIds
        });
        return response;
      }
      
      // Return mock response
      return {
        success: true,
        count: leadIds.length
      };
    } catch (error) {
      console.error('Error adding leads to collection:', error);
      toast.error('Failed to add leads to collection');
      throw error;
    }
  }

  async getLeadCollections(): Promise<LeadCollection[]> {
    try {
      if (this.apiKey) {
        const response = await apiService.get('/api/v1/apollo/collections');
        return response.collections;
      }
      
      // Return mock collections
      return [
        {
          id: 'collection_1',
          name: 'SaaS Startup Founders',
          description: 'Founders of early-stage SaaS startups',
          leadCount: 127,
          createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 'collection_2',
          name: 'Enterprise Decision Makers',
          description: 'C-level executives at Fortune 500 companies',
          leadCount: 85,
          createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 'collection_3',
          name: 'Product Managers',
          description: 'Senior PMs at tech companies',
          leadCount: 203,
          createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString()
        }
      ];
    } catch (error) {
      console.error('Error getting lead collections:', error);
      toast.error('Failed to get lead collections');
      return [];
    }
  }

  async getCollectionLeads(collectionId: string): Promise<ApolloLead[]> {
    try {
      if (this.apiKey) {
        const response = await apiService.get(`/api/v1/apollo/collections/${collectionId}/leads`);
        return response.leads;
      }
      
      // Return mock leads for the collection
      return this.getMockLeads({ limit: 50 });
    } catch (error) {
      console.error(`Error getting leads for collection ${collectionId}:`, error);
      toast.error('Failed to get collection leads');
      return [];
    }
  }

  private getMockLeads(params: LeadSearchParams): ApolloLead[] {
    const industries = ['Software', 'Finance', 'Healthcare', 'Retail', 'Manufacturing', 'Education'];
    const companySizes = ['1-10', '11-50', '51-200', '201-500', '501-1000', '1000+'];
    const locations = ['San Francisco, CA', 'New York, NY', 'Austin, TX', 'Boston, MA', 'Seattle, WA', 'London, UK'];
    const titles = ['CEO', 'CTO', 'CFO', 'COO', 'VP of Engineering', 'VP of Product', 'VP of Marketing', 'Director of IT'];
    
    // Filter by params if provided
    let filteredIndustries = params.industry ? [params.industry] : industries;
    let filteredCompanySizes = params.companySize ? [params.companySize] : companySizes;
    let filteredLocations = params.location ? [params.location] : locations;
    let filteredTitles = params.title ? [params.title] : titles;
    
    // Generate random leads
    const count = params.limit || 20;
    const leads: ApolloLead[] = [];
    
    for (let i = 0; i < count; i++) {
      const industry = filteredIndustries[Math.floor(Math.random() * filteredIndustries.length)];
      const companySize = filteredCompanySizes[Math.floor(Math.random() * filteredCompanySizes.length)];
      const location = filteredLocations[Math.floor(Math.random() * filteredLocations.length)];
      const title = filteredTitles[Math.floor(Math.random() * filteredTitles.length)];
      
      // Generate a company name based on industry
      const companyPrefixes = ['Tech', 'Global', 'Advanced', 'Next', 'Smart', 'Innovative'];
      const companySuffixes = ['Solutions', 'Systems', 'Technologies', 'Group', 'Inc', 'Labs'];
      const companyPrefix = companyPrefixes[Math.floor(Math.random() * companyPrefixes.length)];
      const companySuffix = companySuffixes[Math.floor(Math.random() * companySuffixes.length)];
      const company = `${companyPrefix} ${industry} ${companySuffix}`;
      
      // Generate a random first and last name
      const firstNames = ['John', 'Jane', 'Michael', 'Emily', 'David', 'Sarah', 'Robert', 'Jennifer'];
      const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Miller', 'Davis', 'Garcia'];
      const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
      const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
      
      leads.push({
        id: `lead_${Date.now()}_${i}`,
        firstName,
        lastName,
        email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${company.toLowerCase().replace(/\s+/g, '')}.com`,
        company,
        title,
        phone: `+1${Math.floor(Math.random() * 1000000000 + 1000000000)}`,
        linkedin: `https://linkedin.com/in/${firstName.toLowerCase()}-${lastName.toLowerCase()}-${Math.floor(Math.random() * 10000)}`,
        industry,
        companySize,
        location,
        tags: params.keywords || [],
        createdAt: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString()
      });
    }
    
    return leads;
  }
}

export const apollo = new ApolloService();