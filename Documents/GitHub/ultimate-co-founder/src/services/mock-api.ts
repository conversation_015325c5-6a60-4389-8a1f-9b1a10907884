// Mock API service for development when backend is not available
export class MockApiService {
  private users = new Map([
    ['<EMAIL>', {
      id: '1',
      email: '<EMAIL>',
      name: 'Demo User',
      password: 'demo123',
      created_at: new Date().toISOString(),
      is_active: true
    }]
  ]);

  private currentUser: any = null;
  private token: string | null = null;

  async login(email: string, password: string) {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // For demo purposes, allow any credentials in development
    if (process.env.NODE_ENV === 'development') {
      const mockUser = {
        id: '999',
        email: email || '<EMAIL>',
        name: 'Demo User',
        created_at: new Date().toISOString(),
        is_active: true
      };
      
      this.token = 'mock_token_' + Date.now();
      this.currentUser = mockUser;
      
      return {
        access_token: this.token,
        token_type: 'bearer',
        expires_in: 3600
      };
    }

    const user = this.users.get(email);
    if (!user || user.password !== password) {
      throw new Error('Invalid email or password');
    }

    this.token = 'mock_token_' + Date.now();
    this.currentUser = user;
    
    return {
      access_token: this.token,
      token_type: 'bearer',
      expires_in: 3600
    };
  }

  async register(email: string, password: string, name: string) {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));

    if (this.users.has(email)) {
      throw new Error('Email already registered');
    }

    const newUser = {
      id: Date.now().toString(),
      email,
      name,
      password,
      created_at: new Date().toISOString(),
      is_active: true
    };

    this.users.set(email, newUser);
    return {
      id: newUser.id,
      email: newUser.email,
      name: newUser.name,
      created_at: newUser.created_at,
      is_active: newUser.is_active
    };
  }

  async getCurrentUser() {
    if (!this.token || !this.currentUser) {
      throw new Error('Not authenticated');
    }

    return {
      id: this.currentUser.id,
      email: this.currentUser.email,
      name: this.currentUser.name,
      created_at: this.currentUser.created_at,
      is_active: this.currentUser.is_active
    };
  }

  async logout() {
    this.token = null;
    this.currentUser = null;
  }

  async healthCheck() {
    return { status: 'healthy' };
  }

  isAuthenticated() {
    return !!this.token;
  }

  getToken() {
    return this.token;
  }

  setToken(token: string) {
    this.token = token;
  }

  clearAuth() {
    this.token = null;
    this.currentUser = null;
  }
}

export const mockApiService = new MockApiService();