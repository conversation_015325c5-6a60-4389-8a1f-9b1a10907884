import { apiService } from './api';
import { crewAI } from './crewai';
import { liveKit } from './livekit';
import { composio } from './composio';
import toast from 'react-hot-toast';
import { Target, TrendingUp, Rocket, Code2, Settings, Megaphone } from 'lucide-react';

export interface OrchestrationTask {
  id: string;
  description: string;
  agentIds: string[];
  status: 'pending' | 'running' | 'completed' | 'failed';
  results: CrewResult[];
  liveKitSession?: LiveKitSession;
  integrationActions?: ComposioAction[];
  boltProject?: string;
  createdAt: Date;
  completedAt?: Date;
}

export interface AgentSuggestion {
  id: string;
  agentId: string;
  agentName: string;
  title: string;
  description: string;
  action: string;
  priority: 'high' | 'medium' | 'low';
  estimatedTime: string;
  parameters?: Record<string, any>;
  icon?: React.ElementType;
}

export interface CrewResult {
  task_id: string;
  agent_id: string;
  result: string;
  metadata: {
    execution_time: number;
    tokens_used?: number;
    model: string;
    status: string;
  };
}

export interface LiveKitSession {
  id: string;
  room_name: string;
  token: string;
  url: string;
  status: 'connecting' | 'connected' | 'disconnected' | 'error';
  participants: LiveKitParticipant[];
  duration: number;
  recording_url?: string;
}

export interface LiveKitParticipant {
  id: string;
  name: string;
  role: 'user' | 'agent';
  is_local: boolean;
  audio_enabled: boolean;
  video_enabled: boolean;
  screen_share_enabled: boolean;
}

export interface ComposioAction {
  id: string;
  integration: string;
  action: string;
  parameters: Record<string, any>;
  result?: any;
  status: 'pending' | 'running' | 'completed' | 'failed';
}

class AIOrchestrator {
  private tasks: Map<string, OrchestrationTask> = new Map();
  private activeTask: OrchestrationTask | null = null;
  private openaiKey: string = import.meta.env.VITE_OPENAI_API_KEY || '';

  constructor() {
    // Check if we have an OpenAI API key
    if (!this.openaiKey) {
      console.warn('No OpenAI API key provided. Using mock responses for AI features.');
    }
  }

  async processUserInput(input: string, selectedAgents?: string[]): Promise<{
    responses: CrewResult[];
    suggestions: AgentSuggestion[];
  }> {
    try {
      // If we have a real API, use it
      if (this.openaiKey) {
        try {
          const response = await apiService.post('/api/v1/orchestrator/process', {
            description: input,
            agent_ids: selectedAgents,
            include_livekit: false
          });
          
          return {
            responses: response.responses || [],
            suggestions: response.suggestions || []
          };
        } catch (error) {
          console.error('API error, falling back to direct CrewAI execution:', error);
          // Fall back to direct CrewAI execution if the API fails
          return this.processWithCrewAI(input, selectedAgents);
        }
      } else {
        // Use CrewAI directly if no API key
        return this.processWithCrewAI(input, selectedAgents);
      }
    } catch (error) {
      console.error('Error processing user input:', error);
      toast.error('Failed to process message');
      
      // Return mock data if all else fails
      return this.getMockResponse(input, selectedAgents);
    }
  }

  private async processWithCrewAI(input: string, selectedAgents?: string[]): Promise<{
    responses: CrewResult[];
    suggestions: AgentSuggestion[];
  }> {
    try {
      let responses: CrewResult[];
      
      if (selectedAgents && selectedAgents.length === 1) {
        // Single agent task
        const result = await crewAI.executeTask(selectedAgents[0], input);
        responses = [result];
      } else {
        // Multi-agent task
        const agentIds = selectedAgents || ['strategic', 'product', 'technical', 'operations', 'marketing'];
        responses = await crewAI.executeMultiAgentTask(input, agentIds);
      }
      
      // Generate suggestions based on responses
      const suggestions = this.generateSuggestions(input, responses);
      
      return { responses, suggestions };
    } catch (error) {
      console.error('Error with CrewAI processing:', error);
      throw error;
    }
  }

  private generateSuggestions(input: string, results: CrewResult[]): AgentSuggestion[] {
    const suggestions: AgentSuggestion[] = [];
    
    // Generate suggestions based on agent responses
    results.forEach(result => {
      const agentId = result.agent_id;
      
      if (agentId === 'strategic') {
        suggestions.push({
          id: `strategic_${Date.now()}`,
          agentId: 'strategic',
          agentName: 'Strategic',
          title: 'Conduct Market Analysis',
          description: 'Deep dive into TAM/SAM/SOM, competitive landscape, and market opportunities',
          action: 'market_analysis',
          priority: 'high',
          estimatedTime: '15 min',
          parameters: { analysis_type: 'comprehensive', include_competitors: true },
          icon: TrendingUp
        });
      } else if (agentId === 'product') {
        suggestions.push({
          id: `product_${Date.now()}`,
          agentId: 'product',
          agentName: 'Product',
          title: 'User Interview Sessions',
          description: 'Conduct 3 video interviews with target users to validate assumptions',
          action: 'user_interviews',
          priority: 'high',
          estimatedTime: '20 min',
          parameters: { interview_count: 3, duration: '5min each' },
          icon: Rocket
        });
      } else if (agentId === 'technical') {
        suggestions.push({
          id: `technical_${Date.now()}`,
          agentId: 'technical',
          agentName: 'Technical',
          title: 'Design System Architecture',
          description: 'Create technical blueprint and generate MVP code scaffold',
          action: 'technical_architecture',
          priority: 'medium',
          estimatedTime: '25 min',
          parameters: { include_scaffold: true, deploy_to_bolt: true },
          icon: Code2
        });
      } else if (agentId === 'operations') {
        suggestions.push({
          id: `operations_${Date.now()}`,
          agentId: 'operations',
          agentName: 'Operations',
          title: 'Legal & Financial Setup',
          description: 'Establish business structure, compliance, and financial projections',
          action: 'legal_setup',
          priority: 'medium',
          estimatedTime: '18 min',
          parameters: { include_financials: true, legal_compliance: true },
          icon: Settings
        });
      } else if (agentId === 'marketing') {
        suggestions.push({
          id: `marketing_${Date.now()}`,
          agentId: 'marketing',
          agentName: 'Marketing',
          title: 'Brand & Growth Strategy',
          description: 'Develop positioning, messaging, and go-to-market plan',
          action: 'marketing_strategy',
          priority: 'low',
          estimatedTime: '12 min',
          parameters: { include_branding: true, gtm_strategy: true },
          icon: Megaphone
        });
      }
    });
    
    return suggestions;
  }

  async executeSuggestion(suggestion: AgentSuggestion): Promise<CrewResult> {
    try {
      // If we have a real API, use it
      if (this.openaiKey) {
        try {
          const response = await apiService.post('/api/v1/orchestrator/execute-suggestion', {
            suggestion_id: suggestion.id,
            agent_id: suggestion.agentId,
            action: suggestion.action,
            parameters: suggestion.parameters || {}
          });
          
          return response.result;
        } catch (error) {
          console.error('API error, falling back to direct CrewAI execution:', error);
          // Fall back to direct CrewAI execution
        }
      }
      
      // Execute with CrewAI directly
      let taskDescription = '';
      
      switch (suggestion.action) {
        case 'market_analysis':
          taskDescription = `Conduct a comprehensive market analysis including TAM/SAM/SOM calculations, competitive landscape, and market opportunities. ${suggestion.parameters?.focus || ''}`;
          break;
        case 'user_interviews':
          taskDescription = `Plan and conduct user interviews with ${suggestion.parameters?.interview_count || 3} target users to validate key assumptions. Focus on ${suggestion.parameters?.focus || 'user needs and pain points'}.`;
          break;
        case 'technical_architecture':
          taskDescription = `Design a complete system architecture for the product, including technology stack, infrastructure, and implementation plan. ${suggestion.parameters?.requirements || ''}`;
          break;
        case 'legal_setup':
          taskDescription = `Establish the legal and operational foundation for the business, including entity structure, compliance requirements, and financial projections. ${suggestion.parameters?.business_type || ''}`;
          break;
        case 'marketing_strategy':
          taskDescription = `Develop a comprehensive marketing and growth strategy, including brand positioning, target audience, channels, and go-to-market plan. ${suggestion.parameters?.target_market || ''}`;
          break;
        default:
          taskDescription = `Execute ${suggestion.title}: ${suggestion.description}`;
      }
      
      return await crewAI.executeTask(suggestion.agentId, taskDescription);
    } catch (error) {
      console.error('Error executing suggestion:', error);
      toast.error(`Failed to execute: ${suggestion.title}`);
      
      // Return mock result if all else fails
      return {
        task_id: `task_${Date.now()}_${suggestion.agentId}`,
        agent_id: suggestion.agentId,
        result: `I've completed the ${suggestion.title.toLowerCase()}. Here are my findings and recommendations...`,
        metadata: {
          execution_time: 3500,
          tokens_used: 250,
          model: "gpt-4",
          status: "completed"
        }
      };
    }
  }

  async startLiveKitSession(agentIds: string[], sessionType: 'voice' | 'video' | 'screen-share'): Promise<LiveKitSession> {
    try {
      console.log('🚀 AI Orchestrator: Starting LiveKit session...', { agentIds, sessionType });

      const session = await liveKit.createSession(agentIds, sessionType);
      console.log('🎥 Session created, connecting to room...', session);

      await liveKit.connectToRoom(session);
      console.log('✅ Successfully connected to LiveKit room');

      toast.success(`${sessionType.charAt(0).toUpperCase() + sessionType.slice(1)} session started!`);
      return session;
    } catch (error) {
      console.error('❌ Error starting LiveKit session:', error);
      toast.error(`Failed to start ${sessionType} session. Running in demo mode.`);
      
      // Return mock session if API fails
      return {
        id: `session_${Date.now()}`,
        room_name: `room_${Date.now()}`,
        token: 'mock_token',
        url: 'wss://mock-livekit-url.com',
        status: 'connected',
        participants: [
          {
            id: 'user',
            name: 'You',
            role: 'user',
            is_local: true,
            audio_enabled: true,
            video_enabled: sessionType === 'video',
            screen_share_enabled: sessionType === 'screen-share'
          },
          ...agentIds.map(agentId => ({
            id: agentId,
            name: this.getAgentName(agentId),
            role: 'agent' as const,
            is_local: false,
            audio_enabled: true,
            video_enabled: sessionType === 'video',
            screen_share_enabled: false
          }))
        ],
        duration: 0
      };
    }
  }

  private getAgentName(agentId: string): string {
    const agentNames: Record<string, string> = {
      strategic: 'Alex Strategic',
      product: 'Sam Product',
      technical: 'Taylor Tech',
      operations: 'Jordan Ops',
      marketing: 'Morgan Marketing'
    };
    
    return agentNames[agentId] || agentId;
  }

  async endLiveKitSession(sessionId: string): Promise<void> {
    try {
      await liveKit.endSession(sessionId);
    } catch (error) {
      console.error('Error ending LiveKit session:', error);
      toast.error('Failed to end LiveKit session properly, but the session has been closed.');
    }
  }

  async syncIntegrations(): Promise<void> {
    try {
      await composio.syncIntegrations();
    } catch (error) {
      console.error('Error syncing integrations:', error);
      toast.error('Failed to sync integrations');
    }
  }

  async getOrchestratorStatus() {
    try {
      return await apiService.get('/api/v1/orchestrator/status');
    } catch (error) {
      console.error('Error getting orchestrator status:', error);
      
      // Return mock status if API fails
      return {
        status: 'active',
        agents_available: 5,
        active_sessions: 0,
        integrations_connected: 3,
        timestamp: new Date().toISOString()
      };
    }
  }

  getActiveTask(): OrchestrationTask | null {
    return this.activeTask;
  }

  getTask(taskId: string): OrchestrationTask | null {
    return this.tasks.get(taskId) || null;
  }

  getAllTasks(): OrchestrationTask[] {
    return Array.from(this.tasks.values());
  }

  private getMockResponse(input: string, selectedAgents?: string[]): {
    responses: CrewResult[];
    suggestions: AgentSuggestion[];
  } {
    const agentIds = selectedAgents || ['strategic', 'product', 'technical', 'operations', 'marketing'];
    
    // Generate mock responses for each agent
    const responses: CrewResult[] = agentIds.map(agentId => ({
      task_id: `task_${Date.now()}_${agentId}`,
      agent_id: agentId,
      result: this.getMockAgentResponse(agentId, input),
      metadata: {
        execution_time: 2500,
        tokens_used: 150,
        model: "gpt-4",
        status: "completed"
      }
    }));
    
    // Generate suggestions
    const suggestions = this.generateSuggestions(input, responses);
    
    return { responses, suggestions };
  }

  private getMockAgentResponse(agentId: string, input: string): string {
    const responses: Record<string, string> = {
      strategic: `Based on your idea, I see a significant market opportunity. The total addressable market appears to be in the range of $2-3 billion annually, with a serviceable obtainable market of approximately $500 million.

Key competitors include:
1. Established players with legacy solutions
2. New entrants with partial solutions
3. Adjacent products that solve related problems

Your competitive advantage could be in providing a more integrated, user-friendly solution with better automation. I recommend focusing initially on small to medium businesses in the following verticals:
- Professional services
- Retail
- Healthcare

Would you like me to develop a more detailed go-to-market strategy or competitive analysis?`,

      product: `From a product perspective, your idea has strong potential. Based on initial analysis, I recommend focusing on these key features for your MVP:

1. User authentication and profile management
2. Core functionality with intuitive UI
3. Basic reporting and analytics
4. Integration with common tools

User research suggests the following pain points that your product should address:
- Time-consuming manual processes
- Lack of visibility into key metrics
- Poor integration between existing tools
- Difficulty in scaling operations

I recommend conducting user interviews with 5-7 potential customers to validate these assumptions before proceeding with development.`,

      technical: `From a technical standpoint, I recommend the following architecture for your product:

**Frontend:**
- React with TypeScript
- Tailwind CSS for styling
- Redux for state management

**Backend:**
- Node.js with Express
- PostgreSQL database
- Redis for caching

**Infrastructure:**
- AWS for hosting (ECS for containers)
- CI/CD with GitHub Actions
- Monitoring with Datadog

This stack provides a good balance of development speed, scalability, and maintainability. It also allows for easy integration with third-party services through APIs.

Would you like me to create a more detailed technical specification or generate some starter code?`,

      operations: `From an operations perspective, here's what you should consider:

**Legal Structure:**
- Delaware C-Corporation is recommended for fundraising
- Key legal documents needed: incorporation, bylaws, founder agreements

**Financial Projections:**
- Initial investment: $150-200K
- Runway: 12-18 months
- Break-even point: Month 18-24

**Hiring Plan:**
1. Technical team (2-3 developers)
2. Product manager
3. Marketing specialist
4. Customer success (post-launch)

**Key Metrics to Track:**
- Customer Acquisition Cost (CAC)
- Lifetime Value (LTV)
- Monthly Recurring Revenue (MRR)
- Churn rate

Would you like me to develop a detailed financial model or operational plan?`,

      marketing: `From a marketing perspective, here's my initial strategy for your product:

**Target Audience:**
- Primary: Small business owners (25-45 years old)
- Secondary: Operations managers at mid-sized companies

**Positioning:**
- "The intelligent solution that saves you 10+ hours per week"

**Marketing Channels:**
1. Content marketing (blog, guides, case studies)
2. Paid search targeting high-intent keywords
3. LinkedIn for B2B targeting
4. Strategic partnerships with complementary tools

**Launch Strategy:**
- Pre-launch: Email list building, teaser content
- Launch: PR push, Product Hunt campaign
- Post-launch: Case studies, testimonial collection

I estimate a marketing budget of $3-5K/month would be sufficient for initial traction. Would you like me to develop a more detailed marketing plan or content strategy?`
    };

    return responses[agentId] || `I've analyzed your request: "${input}" and here are my thoughts as the ${agentId} co-founder...`;
  }
}

export const aiOrchestrator = new AIOrchestrator();