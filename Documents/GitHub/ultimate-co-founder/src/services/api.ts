import axios, { AxiosInstance, AxiosResponse } from 'axios';
import toast from 'react-hot-toast';
import { mockApiService } from './mock-api';

export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  status: string;
}

export interface AuthTokens {
  access_token: string;
  token_type: string;
  expires_in: number;
}

export interface User {
  id: string;
  email: string;
  name: string;
  created_at: string;
  is_active: boolean;
}

// Mock Tavus Avatar data
const getTavusAvatarMock = (agentId: string) => {
  const mockAvatars: Record<string, any> = {
    strategic: {
      id: 'strategic-avatar-id',
      name: 'Strategic Advisor Avatar',
      description: 'AI-powered strategic business advisor',
      videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4',
      thumbnailUrl: 'https://picsum.photos/400/300?random=1',
      status: 'ready',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    marketing: {
      id: 'marketing-avatar-id',
      name: 'Marketing Expert Avatar',
      description: 'AI-powered marketing specialist',
      videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4',
      thumbnailUrl: 'https://picsum.photos/400/300?random=2',
      status: 'ready',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    technical: {
      id: 'technical-avatar-id',
      name: 'Technical Lead Avatar',
      description: 'AI-powered technical advisor',
      videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4',
      thumbnailUrl: 'https://picsum.photos/400/300?random=3',
      status: 'ready',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    operations: {
      id: 'operations-avatar-id',
      name: 'Operations Manager Avatar',
      description: 'AI-powered operations specialist',
      videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4',
      thumbnailUrl: 'https://picsum.photos/400/300?random=4',
      status: 'ready',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    product: {
      id: 'product-avatar-id',
      name: 'Product Manager Avatar',
      description: 'AI-powered product development specialist',
      videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4',
      thumbnailUrl: 'https://picsum.photos/400/300?random=5',
      status: 'ready',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  };

  return mockAvatars[agentId] || {
    id: `${agentId}-avatar-id`,
    name: `${agentId.charAt(0).toUpperCase() + agentId.slice(1)} Avatar`,
    description: `AI-powered ${agentId} specialist`,
    videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4',
    thumbnailUrl: 'https://picsum.photos/400/300?random=6',
    status: 'ready',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
};

class ApiService {
  private api: AxiosInstance;
  private baseURL: string;
  private useMockApi: boolean = false;

  constructor() {
    // Always prioritize VITE_API_URL environment variable
    this.baseURL = import.meta.env.VITE_API_URL || 'http://127.0.0.1:8000';
    
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = this.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response: AxiosResponse) => response,
      (error) => {
        // If we get a network error, switch to mock API
        if (error.code === 'NETWORK_ERROR' || error.message === 'Network Error' || error.code === 'ECONNREFUSED') {
          console.warn('Backend server not available, switching to mock API');
          this.useMockApi = true;
          toast.info('Using demo mode - backend server not available');
          return Promise.reject(new Error('SWITCH_TO_MOCK'));
        }

        if (error.response?.status === 401) {
          this.clearAuth();
          window.location.href = '/';
          toast.error('Session expired. Please login again.');
        } else if (error.response?.status >= 500) {
          toast.error('Server error. Please try again later.');
        } else if (error.response?.data?.detail) {
          toast.error(error.response.data.detail);
        }
        return Promise.reject(error);
      }
    );
  }

  // Auth methods
  getToken(): string | null {
    if (this.useMockApi) {
      return mockApiService.getToken();
    }
    return localStorage.getItem('access_token');
  }

  setToken(token: string): void {
    if (this.useMockApi) {
      mockApiService.setToken(token);
    } else {
      localStorage.setItem('access_token', token);
    }
  }

  clearAuth(): void {
    if (this.useMockApi) {
      mockApiService.clearAuth();
    } else {
      localStorage.removeItem('access_token');
      localStorage.removeItem('user');
    }
  }

  isAuthenticated(): boolean {
    if (this.useMockApi) {
      return mockApiService.isAuthenticated();
    }
    return !!this.getToken();
  }

  // API methods with fallback to mock
  async get<T>(url: string): Promise<T> {
    if (this.useMockApi) {
      try {
        // Try to use mock API for common endpoints
        if (url === '/health') {
          return mockApiService.healthCheck() as unknown as T;
        } else if (url === '/api/v1/auth/me') {
          return mockApiService.getCurrentUser() as unknown as T;
        } else if (url === '/api/v1/agents') {
          return { agents: [] } as unknown as T;
        } else if (url === '/api/v1/integrations') {
          return { integrations: [] } as unknown as T;
        } else if (url.startsWith('/api/v1/tavus/avatars/')) {
          // Handle Tavus avatar requests
          const agentId = url.split('/').pop();
          if (agentId) {
            return getTavusAvatarMock(agentId) as unknown as T;
          }
          throw new Error('Invalid agent ID for Tavus avatar');
        } else if (url === '/api/v1/documents') {
          return { 
            documents: [
              {
                id: 'mock-doc-1',
                title: 'Sample Business Plan',
                type: 'business_plan',
                status: 'completed',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                content: 'This is a sample business plan document generated in demo mode.',
                metadata: {
                  pages: 15,
                  word_count: 2500
                }
              },
              {
                id: 'mock-doc-2',
                title: 'Market Analysis Report',
                type: 'market_analysis',
                status: 'completed',
                created_at: new Date(Date.now() - 86400000).toISOString(),
                updated_at: new Date(Date.now() - 86400000).toISOString(),
                content: 'This is a sample market analysis report generated in demo mode.',
                metadata: {
                  pages: 8,
                  word_count: 1200
                }
              },
              {
                id: 'mock-doc-3',
                title: 'Technical Specification',
                type: 'technical_spec',
                status: 'draft',
                created_at: new Date(Date.now() - 172800000).toISOString(),
                updated_at: new Date(Date.now() - 172800000).toISOString(),
                content: 'This is a sample technical specification document generated in demo mode.',
                metadata: {
                  pages: 12,
                  word_count: 1800
                }
              }
            ]
          } as unknown as T;
        } else if (url === '/api/v1/apollo/collections') {
          return {
            collections: [
              {
                id: 'collection_1',
                name: 'SaaS Startup Founders',
                description: 'Founders of early-stage SaaS startups',
                leadCount: 127,
                createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
                updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
              },
              {
                id: 'collection_2',
                name: 'Enterprise Decision Makers',
                description: 'C-level executives at Fortune 500 companies',
                leadCount: 85,
                createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
                updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
              },
              {
                id: 'collection_3',
                name: 'Product Managers',
                description: 'Senior PMs at tech companies',
                leadCount: 203,
                createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
                updatedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString()
              }
            ]
          } as unknown as T;
        } else if (url.startsWith('/api/v1/apollo/collections/') && url.includes('/leads')) {
          // Generate mock leads for a collection
          const industries = ['Software', 'Finance', 'Healthcare', 'Retail', 'Manufacturing', 'Education'];
          const companySizes = ['1-10', '11-50', '51-200', '201-500', '501-1000', '1000+'];
          const locations = ['San Francisco, CA', 'New York, NY', 'Austin, TX', 'Boston, MA', 'Seattle, WA', 'London, UK'];
          const titles = ['CEO', 'CTO', 'CFO', 'COO', 'VP of Engineering', 'VP of Product', 'VP of Marketing', 'Director of IT'];
          
          const count = 50;
          const leads = [];
          
          for (let i = 0; i < count; i++) {
            const industry = industries[Math.floor(Math.random() * industries.length)];
            const companySize = companySizes[Math.floor(Math.random() * companySizes.length)];
            const location = locations[Math.floor(Math.random() * locations.length)];
            const title = titles[Math.floor(Math.random() * titles.length)];
            
            const companyPrefixes = ['Tech', 'Global', 'Advanced', 'Next', 'Smart', 'Innovative'];
            const companySuffixes = ['Solutions', 'Systems', 'Technologies', 'Group', 'Inc', 'Labs'];
            const companyPrefix = companyPrefixes[Math.floor(Math.random() * companyPrefixes.length)];
            const companySuffix = companySuffixes[Math.floor(Math.random() * companySuffixes.length)];
            const company = `${companyPrefix} ${industry} ${companySuffix}`;
            
            const firstNames = ['John', 'Jane', 'Michael', 'Emily', 'David', 'Sarah', 'Robert', 'Jennifer'];
            const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Miller', 'Davis', 'Garcia'];
            const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
            const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
            
            leads.push({
              id: `lead_${Date.now()}_${i}`,
              firstName,
              lastName,
              email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${company.toLowerCase().replace(/\s+/g, '')}.com`,
              company,
              title,
              phone: `+1${Math.floor(Math.random() * 1000000000 + 1000000000)}`,
              linkedin: `https://linkedin.com/in/${firstName.toLowerCase()}-${lastName.toLowerCase()}-${Math.floor(Math.random() * 10000)}`,
              industry,
              companySize,
              location,
              tags: [],
              createdAt: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString()
            });
          }
          
          return { leads } as unknown as T;
        }
        throw new Error('Mock API does not support this GET request');
      } catch (error) {
        console.error('Mock API error:', error);
        throw error;
      }
    }
    
    try {
      const response = await this.api.get<T>(url);
      return response.data;
    } catch (error: any) {
      if (error.message === 'SWITCH_TO_MOCK') {
        // Try again with mock API
        this.useMockApi = true;
        return this.get<T>(url);
      }
      throw error;
    }
  }

  async post<T>(url: string, data?: any): Promise<T> {
    if (this.useMockApi) {
      try {
        // Try to use mock API for common endpoints
        if (url === '/api/v1/auth/login') {
          const formData = new FormData();
          const username = data.get('username') || data.username;
          const password = data.get('password') || data.password;
          return mockApiService.login(username, password) as unknown as T;
        } else if (url === '/api/v1/auth/register') {
          return mockApiService.register(data.email, data.password, data.name) as unknown as T;
        } else if (url === '/api/v1/auth/logout') {
          return mockApiService.logout() as unknown as T;
        } else if (url.includes('/api/v1/orchestrator/process')) {
          return {
            responses: [],
            suggestions: []
          } as unknown as T;
        } else if (url.includes('/api/v1/livekit/sessions')) {
          return {
            id: 'mock-session-id',
            room_name: 'mock-room',
            token: 'mock-token',
            url: 'wss://mock-livekit.com',
            status: 'connected',
            participants: [],
            duration: 0
          } as unknown as T;
        } else if (url === '/api/v1/documents') {
          return {
            id: 'mock-doc-new',
            title: data.title || 'New Document',
            type: data.type || 'general',
            status: 'processing',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            content: '',
            metadata: {
              pages: 0,
              word_count: 0
            }
          } as unknown as T;
        } else if (url === '/api/v1/apollo/search') {
          // Generate mock search results
          const industries = ['Software', 'Finance', 'Healthcare', 'Retail', 'Manufacturing', 'Education'];
          const companySizes = ['1-10', '11-50', '51-200', '201-500', '501-1000', '1000+'];
          const locations = ['San Francisco, CA', 'New York, NY', 'Austin, TX', 'Boston, MA', 'Seattle, WA', 'London, UK'];
          const titles = ['CEO', 'CTO', 'CFO', 'COO', 'VP of Engineering', 'VP of Product', 'VP of Marketing', 'Director of IT'];
          
          // Filter by params if provided
          let filteredIndustries = data.industry ? [data.industry] : industries;
          let filteredCompanySizes = data.companySize ? [data.companySize] : companySizes;
          let filteredLocations = data.location ? [data.location] : locations;
          let filteredTitles = data.title ? [data.title] : titles;
          
          // Generate random leads
          const count = data.limit || 20;
          const leads = [];
          
          for (let i = 0; i < count; i++) {
            const industry = filteredIndustries[Math.floor(Math.random() * filteredIndustries.length)];
            const companySize = filteredCompanySizes[Math.floor(Math.random() * filteredCompanySizes.length)];
            const location = filteredLocations[Math.floor(Math.random() * filteredLocations.length)];
            const title = filteredTitles[Math.floor(Math.random() * filteredTitles.length)];
            
            const companyPrefixes = ['Tech', 'Global', 'Advanced', 'Next', 'Smart', 'Innovative'];
            const companySuffixes = ['Solutions', 'Systems', 'Technologies', 'Group', 'Inc', 'Labs'];
            const companyPrefix = companyPrefixes[Math.floor(Math.random() * companyPrefixes.length)];
            const companySuffix = companySuffixes[Math.floor(Math.random() * companySuffixes.length)];
            const company = `${companyPrefix} ${industry} ${companySuffix}`;
            
            const firstNames = ['John', 'Jane', 'Michael', 'Emily', 'David', 'Sarah', 'Robert', 'Jennifer'];
            const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Miller', 'Davis', 'Garcia'];
            const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
            const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
            
            leads.push({
              id: `lead_${Date.now()}_${i}`,
              firstName,
              lastName,
              email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${company.toLowerCase().replace(/\s+/g, '')}.com`,
              company,
              title,
              phone: `+1${Math.floor(Math.random() * 1000000000 + 1000000000)}`,
              linkedin: `https://linkedin.com/in/${firstName.toLowerCase()}-${lastName.toLowerCase()}-${Math.floor(Math.random() * 10000)}`,
              industry,
              companySize,
              location,
              tags: data.keywords || [],
              createdAt: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString()
            });
          }
          
          return { leads } as unknown as T;
        } else if (url === '/api/v1/apollo/collections') {
          return {
            id: `collection_${Date.now()}`,
            name: data.name,
            description: data.description,
            leadCount: 0,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          } as unknown as T;
        } else if (url.includes('/api/v1/apollo/collections/') && url.includes('/leads')) {
          return {
            success: true,
            count: data.lead_ids.length
          } as unknown as T;
        }
        throw new Error('Mock API does not support this POST request');
      } catch (error) {
        console.error('Mock API error:', error);
        throw error;
      }
    }
    
    try {
      const response = await this.api.post<T>(url, data);
      return response.data;
    } catch (error: any) {
      if (error.message === 'SWITCH_TO_MOCK') {
        // Try again with mock API
        this.useMockApi = true;
        return this.post<T>(url, data);
      }
      throw error;
    }
  }

  async put<T>(url: string, data?: any): Promise<T> {
    if (this.useMockApi) {
      throw new Error('Mock API does not support PUT requests');
    }
    
    try {
      const response = await this.api.put<T>(url, data);
      return response.data;
    } catch (error: any) {
      if (error.message === 'SWITCH_TO_MOCK') {
        this.useMockApi = true;
        throw new Error('Mock API does not support PUT requests');
      }
      throw error;
    }
  }

  async delete<T>(url: string): Promise<T> {
    if (this.useMockApi) {
      throw new Error('Mock API does not support DELETE requests');
    }
    
    try {
      const response = await this.api.delete<T>(url);
      return response.data;
    } catch (error: any) {
      if (error.message === 'SWITCH_TO_MOCK') {
        this.useMockApi = true;
        throw new Error('Mock API does not support DELETE requests');
      }
      throw error;
    }
  }

  // Auth API calls with mock fallback
  async login(email: string, password: string): Promise<AuthTokens> {
    try {
      const formData = new FormData();
      formData.append('username', email);
      formData.append('password', password);

      const response = await this.api.post<AuthTokens>('/api/v1/auth/login', formData, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
      });
      
      this.setToken(response.data.access_token);
      return response.data;
    } catch (error: any) {
      if (error.message === 'SWITCH_TO_MOCK' || this.useMockApi) {
        const result = await mockApiService.login(email, password);
        this.setToken(result.access_token);
        return result;
      }
      throw error;
    }
  }

  async register(email: string, password: string, name: string):  Promise<User> {
    try {
      const response = await this.api.post<User>('/api/v1/auth/register', {
        email,
        password,
        name
      });
      return response.data;
    } catch (error: any) {
      if (error.message === 'SWITCH_TO_MOCK' || this.useMockApi) {
        return mockApiService.register(email, password, name);
      }
      throw error;
    }
  }

  async getCurrentUser(): Promise<User> {
    try {
      return await this.get<User>('/api/v1/auth/me');
    } catch (error: any) {
      if (error.message === 'SWITCH_TO_MOCK' || this.useMockApi) {
        return mockApiService.getCurrentUser();
      }
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      if (!this.useMockApi) {
        await this.post('/api/v1/auth/logout');
      }
    } catch (error: any) {
      if (error.message !== 'SWITCH_TO_MOCK' && !this.useMockApi) {
        throw error;
      }
    } finally {
      if (this.useMockApi) {
        await mockApiService.logout();
      }
      this.clearAuth();
    }
  }

  // Health check with mock fallback
  async healthCheck(): Promise<{ status: string }> {
    try {
      return await this.get('/health');
    } catch (error: any) {
      if (error.message === 'SWITCH_TO_MOCK' || this.useMockApi) {
        return mockApiService.healthCheck();
      }
      throw error;
    }
  }
}

export const apiService = new ApiService();