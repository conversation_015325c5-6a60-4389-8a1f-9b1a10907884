import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  Zap, 
  ArrowLeft, 
  Briefcase, 
  MapPin, 
  Clock,
  DollarSign,
  ChevronRight,
  Search,
  Filter,
  CheckCircle,
  Users,
  Code,
  TrendingUp,
  HeartHandshake,
  Rocket,
  Building2,
  Send,
  X
} from 'lucide-react';

interface Job {
  id: string;
  title: string;
  department: string;
  location: string;
  type: 'Full-time' | 'Part-time' | 'Contract' | 'Remote';
  salary: string;
  posted: string;
  description: string;
  requirements: string[];
  responsibilities: string[];
  benefits: string[];
}

export const Careers: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState<string | null>(null);
  const [selectedLocation, setSelectedLocation] = useState<string | null>(null);
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [showJobModal, setShowJobModal] = useState(false);

  // Mock jobs data
  const jobs: Job[] = [
    {
      id: 'ai-research-scientist',
      title: 'AI Research Scientist',
      department: 'AI Research',
      location: 'San Francisco, CA',
      type: 'Full-time',
      salary: '$150,000 - $200,000',
      posted: '2025-06-20',
      description: "We're looking for an AI Research Scientist to join our team and help advance the capabilities of our AI co-founder agents. You\'ll work on cutting-edge multi-agent systems, improving coordination, and developing new capabilities.",
      requirements: [
        'PhD in Computer Science, AI, or related field',
        '3+ years of experience in AI research',
        'Strong publication record in NLP, multi-agent systems, or LLMs',
        'Experience with PyTorch or TensorFlow',
        'Familiarity with LangChain, CrewAI, or similar frameworks'
      ],
      responsibilities: [
        'Conduct research to improve our AI co-founder agents',
        'Develop new capabilities for specialized agents',
        'Publish research findings in top-tier conferences',
        'Collaborate with engineering team to implement research findings',
        'Stay current with the latest developments in AI research'
      ],
      benefits: [
        'Competitive salary and equity package',
        'Comprehensive health, dental, and vision insurance',
        'Unlimited PTO policy',
        'Remote-friendly work environment',
        'Professional development budget',
        'Latest hardware and software'
      ]
    },
    {
      id: 'senior-frontend-engineer',
      title: 'Senior Frontend Engineer',
      department: 'Engineering',
      location: 'Remote',
      type: 'Full-time',
      salary: '$140,000 - $180,000',
      posted: '2025-06-22',
      description: "We're seeking a Senior Frontend Engineer to help build and improve our web application. You\'ll work on creating intuitive interfaces for users to interact with our AI co-founder agents and collaborate on projects.",
      requirements: [
        '5+ years of experience in frontend development',
        'Expert knowledge of React, TypeScript, and modern frontend tools',
        'Experience with real-time applications and WebSockets',
        'Strong UX sensibilities and attention to detail',
        'Experience with video/audio applications is a plus'
      ],
      responsibilities: [
        'Build and maintain our React-based web application',
        'Implement real-time features using WebSockets',
        'Integrate with LiveKit for video/voice capabilities',
        'Collaborate with design team to implement UI/UX improvements',
        'Write clean, maintainable, and well-tested code'
      ],
      benefits: [
        'Competitive salary and equity package',
        'Comprehensive health, dental, and vision insurance',
        'Unlimited PTO policy',
        'Remote-friendly work environment',
        'Professional development budget',
        'Latest hardware and software'
      ]
    },
    {
      id: 'product-manager',
      title: 'Product Manager',
      department: 'Product',
      location: 'New York, NY',
      type: 'Full-time',
      salary: '$130,000 - $170,000',
      posted: '2025-06-25',
      description: "We're looking for a Product Manager to help shape the future of our AI co-founder platform. You\'ll work closely with engineering, design, and business teams to define product strategy and roadmap.",
      requirements: [
        '4+ years of experience in product management',
        'Experience with AI or SaaS products',
        'Strong analytical and problem-solving skills',
        'Excellent communication and stakeholder management',
        'Technical background preferred'
      ],
      responsibilities: [
        'Define product strategy and roadmap',
        'Gather and prioritize product requirements',
        'Work with engineering and design teams to deliver features',
        'Analyze user feedback and metrics to inform product decisions',
        'Conduct competitive analysis and market research'
      ],
      benefits: [
        'Competitive salary and equity package',
        'Comprehensive health, dental, and vision insurance',
        'Unlimited PTO policy',
        'Remote-friendly work environment',
        'Professional development budget',
        'Latest hardware and software'
      ]
    },
    {
      id: 'backend-engineer',
      title: 'Backend Engineer',
      department: 'Engineering',
      location: 'Remote',
      type: 'Full-time',
      salary: '$130,000 - $170,000',
      posted: '2025-06-18',
      description: "We're seeking a Backend Engineer to help build and scale our platform. You\'ll work on our API, database, and infrastructure to ensure our AI co-founder agents can provide reliable and performant guidance.",
      requirements: [
        '3+ years of experience in backend development',
        'Experience with Node.js, Python, or similar languages',
        'Knowledge of SQL and NoSQL databases',
        'Experience with cloud infrastructure (AWS, GCP, or Azure)',
        'Understanding of API design and microservices'
      ],
      responsibilities: [
        'Design and implement backend services and APIs',
        'Optimize database performance and reliability',
        'Implement security best practices',
        'Collaborate with frontend team on integration',
        'Participate in code reviews and technical discussions'
      ],
      benefits: [
        'Competitive salary and equity package',
        'Comprehensive health, dental, and vision insurance',
        'Unlimited PTO policy',
        'Remote-friendly work environment',
        'Professional development budget',
        'Latest hardware and software'
      ]
    },
    {
      id: 'marketing-manager',
      title: 'Marketing Manager',
      department: 'Marketing',
      location: 'San Francisco, CA',
      type: 'Full-time',
      salary: '$120,000 - $150,000',
      posted: '2025-06-15',
      description: "We're looking for a Marketing Manager to help grow our user base and establish our brand in the market. You\'ll develop and execute marketing strategies across various channels to reach and engage potential customers.",
      requirements: [
        '4+ years of experience in B2B SaaS marketing',
        'Experience with content marketing, SEO, and social media',
        'Strong analytical skills and data-driven approach',
        'Excellent writing and communication skills',
        'Experience with marketing automation tools'
      ],
      responsibilities: [
        'Develop and execute marketing strategies',
        'Create compelling content for various channels',
        'Manage social media presence and community',
        'Analyze marketing metrics and optimize campaigns',
        'Collaborate with product and sales teams'
      ],
      benefits: [
        'Competitive salary and equity package',
        'Comprehensive health, dental, and vision insurance',
        'Unlimited PTO policy',
        'Remote-friendly work environment',
        'Professional development budget',
        'Latest hardware and software'
      ]
    }
  ];

  const departments = Array.from(new Set(jobs.map(job => job.department)));
  const locations = Array.from(new Set(jobs.map(job => job.location)));

  const filteredJobs = jobs
    .filter(job => 
      job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      job.department.toLowerCase().includes(searchQuery.toLowerCase()) ||
      job.description.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .filter(job => 
      !selectedDepartment || job.department === selectedDepartment
    )
    .filter(job => 
      !selectedLocation || job.location === selectedLocation
    );

  const handleViewJob = (job: Job) => {
    setSelectedJob(job);
    setShowJobModal(true);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <span className="font-bold text-gray-900 dark:text-white">Ultimate Co-founder</span>
              </Link>
              <span className="mx-4 text-gray-300 dark:text-gray-600">|</span>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">Careers</h1>
            </div>
            <div className="flex items-center gap-4">
              <Link to="/" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                <ArrowLeft className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">Join Our Mission</h1>
          <p className="text-xl md:text-2xl max-w-3xl mx-auto mb-8">
            Help us revolutionize entrepreneurship by building AI co-founders that empower the next generation of startups
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <a 
              href="#open-positions"
              className="px-6 py-3 bg-white text-purple-600 rounded-lg font-medium hover:bg-gray-100 transition-colors"
            >
              View Open Positions
            </a>
            <Link 
              to="/about"
              className="px-6 py-3 bg-purple-700 text-white rounded-lg font-medium hover:bg-purple-800 transition-colors"
            >
              Learn About Us
            </Link>
          </div>
        </div>
      </div>

      {/* Why Join Us */}
      <div className="py-16 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Why Join Ultimate Co-founder?</h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Be part of a team that's changing how startups are built
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                icon: Rocket,
                title: 'Cutting-Edge AI',
                description: "Work with the latest AI technologies and push the boundaries of what's possible with multi-agent systems."
              },
              {
                icon: HeartHandshake,
                title: 'Meaningful Impact',
                description: 'Help thousands of entrepreneurs turn their ideas into successful businesses and create real economic value.'
              },
              {
                icon: Users,
                title: 'Exceptional Team',
                description: 'Join a diverse team of experts from companies like Google, OpenAI, and successful startups.'
              },
              {
                icon: TrendingUp,
                title: 'Growth Opportunity',
                description: 'Be part of a rapidly growing company with abundant opportunities for career advancement and learning.'
              },
              {
                icon: Building2,
                title: 'Strong Culture',
                description: 'Enjoy a collaborative, inclusive, and supportive work environment that values work-life balance.'
              },
              {
                icon: DollarSign,
                title: 'Competitive Compensation',
                description: 'Receive excellent compensation with salary, equity, and comprehensive benefits.'
              }
            ].map((benefit, index) => (
              <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mb-4">
                  <benefit.icon className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">{benefit.title}</h3>
                <p className="text-gray-600 dark:text-gray-400">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Open Positions */}
      <div id="open-positions" className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">Open Positions</h2>
          
          {/* Search and Filters */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm mb-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search positions..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              <div>
                <select
                  value={selectedDepartment || ''}
                  onChange={(e) => setSelectedDepartment(e.target.value || null)}
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="">All Departments</option>
                  {departments.map((dept) => (
                    <option key={dept} value={dept}>{dept}</option>
                  ))}
                </select>
              </div>
              <div>
                <select
                  value={selectedLocation || ''}
                  onChange={(e) => setSelectedLocation(e.target.value || null)}
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="">All Locations</option>
                  {locations.map((loc) => (
                    <option key={loc} value={loc}>{loc}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Job Listings */}
          <div className="space-y-4">
            {filteredJobs.length > 0 ? (
              filteredJobs.map((job) => (
                <div 
                  key={job.id}
                  className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow"
                >
                  <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{job.title}</h3>
                      <div className="flex flex-wrap gap-3 text-sm">
                        <div className="flex items-center gap-1 text-gray-600 dark:text-gray-400">
                          <Briefcase className="w-4 h-4" />
                          <span>{job.department}</span>
                        </div>
                        <div className="flex items-center gap-1 text-gray-600 dark:text-gray-400">
                          <MapPin className="w-4 h-4" />
                          <span>{job.location}</span>
                        </div>
                        <div className="flex items-center gap-1 text-gray-600 dark:text-gray-400">
                          <Clock className="w-4 h-4" />
                          <span>{job.type}</span>
                        </div>
                        <div className="flex items-center gap-1 text-gray-600 dark:text-gray-400">
                          <DollarSign className="w-4 h-4" />
                          <span>{job.salary}</span>
                        </div>
                      </div>
                    </div>
                    <button
                      onClick={() => handleViewJob(job)}
                      className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                    >
                      View Position
                      <ChevronRight className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))
            ) : (
              <div className="bg-white dark:bg-gray-800 rounded-lg p-8 text-center">
                <Briefcase className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">No positions found</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Try adjusting your search or filters to find open positions.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Benefits Section */}
      <div className="py-16 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Benefits & Perks</h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              We take care of our team so they can focus on building amazing products
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: 'Competitive Compensation',
                description: 'Salary and equity packages that recognize your value and contribution to our mission.'
              },
              {
                title: 'Health & Wellness',
                description: 'Comprehensive medical, dental, and vision coverage for you and your dependents.'
              },
              {
                title: 'Unlimited PTO',
                description: 'Take the time you need to rest, recharge, and maintain a healthy work-life balance.'
              },
              {
                title: 'Remote-Friendly',
                description: 'Work from anywhere with flexible hours and a results-oriented culture.'
              },
              {
                title: 'Professional Development',
                description: '$5,000 annual budget for conferences, courses, and learning materials.'
              },
              {
                title: 'Latest Equipment',
                description: 'Your choice of top-of-the-line hardware and software to do your best work.'
              },
              {
                title: 'Parental Leave',
                description: '16 weeks of paid parental leave for all new parents.'
              },
              {
                title: 'Retirement Plan',
                description: '401(k) plan with company matching to help you save for the future.'
              },
              {
                title: 'Team Events',
                description: 'Regular team retreats, social events, and opportunities to connect with colleagues.'
              }
            ].map((benefit, index) => (
              <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                <div className="flex items-center gap-3 mb-3">
                  <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white">{benefit.title}</h3>
                </div>
                <p className="text-gray-600 dark:text-gray-400 ml-8">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-16 bg-gradient-to-r from-purple-600 to-blue-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-6">Don't See the Right Position?</h2>
          <p className="text-xl text-purple-100 mb-8 max-w-3xl mx-auto">
            We're always looking for talented individuals to join our team. Send us your resume and we'll keep you in mind for future opportunities.
          </p>
          <Link 
            to="/contact"
            className="inline-flex items-center gap-2 px-8 py-4 bg-white text-purple-600 rounded-xl font-semibold text-lg transition-all hover:bg-gray-100"
          >
            <Send className="w-5 h-5" />
            Send Your Resume
          </Link>
        </div>
      </div>

      {/* Job Modal */}
      {showJobModal && selectedJob && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4 overflow-y-auto">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-3xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">{selectedJob.title}</h2>
                <button 
                  onClick={() => setShowJobModal(false)}
                  className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                  <Briefcase className="w-4 h-4" />
                  <span>{selectedJob.department}</span>
                </div>
                <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                  <MapPin className="w-4 h-4" />
                  <span>{selectedJob.location}</span>
                </div>
                <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                  <Clock className="w-4 h-4" />
                  <span>{selectedJob.type}</span>
                </div>
                <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                  <DollarSign className="w-4 h-4" />
                  <span>{selectedJob.salary}</span>
                </div>
              </div>

              <div className="mb-6">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">About the Role</h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">{selectedJob.description}</p>
                <p className="text-gray-600 dark:text-gray-400">Posted on {formatDate(selectedJob.posted)}</p>
              </div>

              <div className="mb-6">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">Responsibilities</h3>
                <ul className="space-y-2">
                  {selectedJob.responsibilities.map((responsibility, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5" />
                      <span className="text-gray-600 dark:text-gray-400">{responsibility}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="mb-6">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">Requirements</h3>
                <ul className="space-y-2">
                  {selectedJob.requirements.map((requirement, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5" />
                      <span className="text-gray-600 dark:text-gray-400">{requirement}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="mb-8">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">Benefits</h3>
                <ul className="space-y-2">
                  {selectedJob.benefits.map((benefit, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5" />
                      <span className="text-gray-600 dark:text-gray-400">{benefit}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="flex gap-4">
                <button className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                  Apply Now
                </button>
                <button
                  onClick={() => setShowJobModal(false)}
                  className="px-6 py-3 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};