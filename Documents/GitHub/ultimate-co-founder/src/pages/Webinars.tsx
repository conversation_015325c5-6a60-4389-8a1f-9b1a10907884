import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  Zap, 
  Calendar, 
  Clock, 
  Users, 
  Play, 
  ArrowRight,
  Video,
  Download,
  Star,
  CheckCircle,
  User,
  Globe
} from 'lucide-react';

export const Webinars: React.FC = () => {
  const [activeTab, setActiveTab] = useState('upcoming');

  const upcomingWebinars = [
    {
      id: 1,
      title: 'Building Your First AI-Powered Startup',
      description: 'Learn how to leverage AI co-founders to validate your idea, build your MVP, and scale your startup.',
      date: '2024-02-15',
      time: '2:00 PM EST',
      duration: '60 minutes',
      presenter: 'AI Co-founder Team',
      attendees: 1247,
      level: 'Beginner',
      topics: ['AI Strategy', 'MVP Development', 'Product Validation'],
      image: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=2126&q=80'
    },
    {
      id: 2,
      title: 'Advanced Fundraising Strategies with AI',
      description: 'Master the art of fundraising with AI-powered pitch decks, financial modeling, and investor targeting.',
      date: '2024-02-22',
      time: '3:00 PM EST',
      duration: '90 minutes',
      presenter: 'Strategic Co-founder AI',
      attendees: 892,
      level: 'Advanced',
      topics: ['Fundraising', 'Pitch Decks', 'Investor Relations'],
      image: 'https://images.unsplash.com/photo-1559136555-9303baea8ebd?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80'
    }
  ];

  const pastWebinars = [
    {
      id: 3,
      title: 'Product-Market Fit in the AI Era',
      description: 'Discover how AI can help you achieve product-market fit faster and more efficiently.',
      date: '2024-01-25',
      duration: '75 minutes',
      presenter: 'Product Co-founder AI',
      views: 3421,
      rating: 4.8,
      topics: ['Product Strategy', 'Market Research', 'User Validation'],
      image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2015&q=80',
      recordingUrl: '#'
    },
    {
      id: 4,
      title: 'Technical Architecture for Startups',
      description: 'Build scalable technical foundations with AI-guided architecture decisions.',
      date: '2024-01-18',
      duration: '60 minutes',
      presenter: 'Technical Co-founder AI',
      views: 2156,
      rating: 4.9,
      topics: ['Technical Strategy', 'Architecture', 'Scalability'],
      image: 'https://images.unsplash.com/photo-1518432031352-d6fc5c10da5a?ixlib=rb-4.0.3&auto=format&fit=crop&w=2074&q=80',
      recordingUrl: '#'
    },
    {
      id: 5,
      title: 'Go-to-Market Strategies That Work',
      description: 'Learn proven go-to-market strategies and how to execute them with AI assistance.',
      date: '2024-01-11',
      duration: '80 minutes',
      presenter: 'Marketing Co-founder AI',
      views: 4567,
      rating: 4.7,
      topics: ['Marketing', 'Customer Acquisition', 'Growth'],
      image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?ixlib=rb-4.0.3&auto=format&fit=crop&w=2039&q=80',
      recordingUrl: '#'
    }
  ];

  const tutorials = [
    {
      id: 6,
      title: 'Getting Started with AI Co-founders',
      description: 'A step-by-step guide to setting up and working with your AI co-founder team.',
      duration: '15 minutes',
      type: 'Tutorial',
      difficulty: 'Beginner',
      views: 8934,
      thumbnail: 'https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80'
    },
    {
      id: 7,
      title: 'Creating Your First Pitch Deck',
      description: 'Learn how to create compelling pitch decks with AI assistance.',
      duration: '25 minutes',
      type: 'Tutorial',
      difficulty: 'Intermediate',
      views: 5672,
      thumbnail: 'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80'
    },
    {
      id: 8,
      title: 'Advanced Product Strategy',
      description: 'Deep dive into product strategy and roadmap planning with AI insights.',
      duration: '35 minutes',
      type: 'Tutorial',
      difficulty: 'Advanced',
      views: 3421,
      thumbnail: 'https://images.unsplash.com/photo-1553028826-f4804a6dba3b?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <span className="font-bold text-gray-900 dark:text-white">AI Co-founder</span>
              </Link>
              <span className="mx-4 text-gray-300 dark:text-gray-600">|</span>
              <span className="text-gray-600 dark:text-gray-400">Webinars & Tutorials</span>
            </div>
            <Link 
              to="/dashboard" 
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
            >
              Get Started
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            Webinars & Tutorials
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 mb-8 max-w-3xl mx-auto">
            Learn from experts and master the art of building successful startups with AI co-founders through our comprehensive webinars and tutorials.
          </p>
        </div>
      </section>

      {/* Navigation Tabs */}
      <section className="py-8 px-4 sm:px-6 lg:px-8 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-center">
            <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
              {[
                { id: 'upcoming', label: 'Upcoming Webinars', icon: Calendar },
                { id: 'past', label: 'Past Webinars', icon: Video },
                { id: 'tutorials', label: 'Tutorials', icon: Play }
              ].map((tab) => {
                const IconComponent = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-colors ${
                      activeTab === tab.id
                        ? 'bg-white dark:bg-gray-800 text-purple-600 dark:text-purple-400 shadow-sm'
                        : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
                    }`}
                  >
                    <IconComponent size={16} />
                    {tab.label}
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      </section>

      {/* Content Sections */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* Upcoming Webinars */}
          {activeTab === 'upcoming' && (
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-8">Upcoming Webinars</h2>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {upcomingWebinars.map((webinar) => (
                  <div key={webinar.id} className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                    <div className="relative h-48">
                      <img 
                        src={webinar.image} 
                        alt={webinar.title}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute top-4 left-4">
                        <span className="px-3 py-1 bg-green-600 text-white text-sm font-medium rounded-full">
                          Upcoming
                        </span>
                      </div>
                      <div className="absolute top-4 right-4">
                        <span className="px-3 py-1 bg-black/50 text-white text-sm font-medium rounded-full">
                          {webinar.level}
                        </span>
                      </div>
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                        {webinar.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400 mb-4">
                        {webinar.description}
                      </p>
                      
                      <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                        <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                          <Calendar size={16} />
                          {new Date(webinar.date).toLocaleDateString()}
                        </div>
                        <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                          <Clock size={16} />
                          {webinar.time}
                        </div>
                        <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                          <User size={16} />
                          {webinar.presenter}
                        </div>
                        <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                          <Users size={16} />
                          {webinar.attendees} registered
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-2 mb-4">
                        {webinar.topics.map((topic, index) => (
                          <span key={index} className="px-2 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 text-xs rounded-full">
                            {topic}
                          </span>
                        ))}
                      </div>

                      <button className="w-full px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-semibold transition-colors flex items-center justify-center gap-2">
                        Register Now
                        <ArrowRight size={16} />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Past Webinars */}
          {activeTab === 'past' && (
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-8">Past Webinars</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {pastWebinars.map((webinar) => (
                  <div key={webinar.id} className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                    <div className="relative h-48">
                      <img 
                        src={webinar.image} 
                        alt={webinar.title}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
                        <button className="w-16 h-16 bg-white/90 rounded-full flex items-center justify-center hover:bg-white transition-colors">
                          <Play className="w-6 h-6 text-gray-900 ml-1" />
                        </button>
                      </div>
                      <div className="absolute top-4 left-4">
                        <span className="px-3 py-1 bg-blue-600 text-white text-sm font-medium rounded-full">
                          Recording
                        </span>
                      </div>
                    </div>
                    <div className="p-6">
                      <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                        {webinar.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400 mb-4 text-sm">
                        {webinar.description}
                      </p>
                      
                      <div className="flex items-center justify-between mb-4 text-sm text-gray-600 dark:text-gray-400">
                        <div className="flex items-center gap-1">
                          <Star className="w-4 h-4 text-yellow-500" />
                          {webinar.rating}
                        </div>
                        <div className="flex items-center gap-1">
                          <Users size={14} />
                          {webinar.views} views
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock size={14} />
                          {webinar.duration}
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-1 mb-4">
                        {webinar.topics.map((topic, index) => (
                          <span key={index} className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded">
                            {topic}
                          </span>
                        ))}
                      </div>

                      <div className="flex gap-2">
                        <button className="flex-1 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors flex items-center justify-center gap-2">
                          <Play size={14} />
                          Watch
                        </button>
                        <button className="px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors">
                          <Download size={14} />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Tutorials */}
          {activeTab === 'tutorials' && (
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-8">Video Tutorials</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {tutorials.map((tutorial) => (
                  <div key={tutorial.id} className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                    <div className="relative h-48">
                      <img 
                        src={tutorial.thumbnail} 
                        alt={tutorial.title}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
                        <button className="w-16 h-16 bg-white/90 rounded-full flex items-center justify-center hover:bg-white transition-colors">
                          <Play className="w-6 h-6 text-gray-900 ml-1" />
                        </button>
                      </div>
                      <div className="absolute bottom-4 right-4">
                        <span className="px-2 py-1 bg-black/70 text-white text-sm rounded">
                          {tutorial.duration}
                        </span>
                      </div>
                    </div>
                    <div className="p-6">
                      <div className="flex items-center justify-between mb-2">
                        <span className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 text-xs rounded-full">
                          {tutorial.type}
                        </span>
                        <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded-full">
                          {tutorial.difficulty}
                        </span>
                      </div>
                      <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                        {tutorial.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400 mb-4 text-sm">
                        {tutorial.description}
                      </p>
                      <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                        <div className="flex items-center gap-1">
                          <Users size={14} />
                          {tutorial.views} views
                        </div>
                        <button className="text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300 font-medium">
                          Watch Now
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Ready to Start Learning?
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-400 mb-8">
            Join thousands of entrepreneurs who are building successful startups with AI co-founders.
          </p>
          <Link 
            to="/dashboard" 
            className="inline-flex items-center gap-2 px-8 py-4 bg-purple-600 hover:bg-purple-700 text-white rounded-xl font-semibold text-lg transition-all hover:shadow-lg"
          >
            Start Your Journey
            <ArrowRight size={20} />
          </Link>
        </div>
      </section>
    </div>
  );
};
