import React, { useState, useEffect } from 'react';
import { 
  FileText, 
  Presentation, 
  Table, 
  Mic, 
  Download, 
  ExternalLink, 
  Plus,
  Loader2,
  CheckCircle,
  AlertCircle,
  Search,
  Filter,
  Calendar,
  Clock,
  X,
  RefreshCw,
  Trash2,
  Edit,
  Share2,
  Eye,
  Link
} from 'lucide-react';
import { documentGenerator, GeneratedDocument, GenerateDocumentRequest } from '../services/document-generator';
import toast from 'react-hot-toast';
import { Link as RouterLink } from 'react-router-dom';

export const DocumentsPage: React.FC = () => {
  const [documents, setDocuments] = useState<GeneratedDocument[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showGenerateModal, setShowGenerateModal] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<GeneratedDocument | null>(null);
  const [showDocumentModal, setShowDocumentModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Form state for document generation
  const [generateForm, setGenerateForm] = useState<GenerateDocumentRequest>({
    type: 'document',
    title: '',
    prompt: '',
    template: 'standard',
    additionalContext: ''
  });

  useEffect(() => {
    loadDocuments();
  }, []);

  const loadDocuments = async () => {
    setIsLoading(true);
    try {
      const docs = await documentGenerator.listDocuments();
      setDocuments(docs);
    } catch (error) {
      console.error('Error loading documents:', error);
      toast.error('Failed to load documents');
    } finally {
      setIsLoading(false);
    }
  };

  const refreshDocuments = async () => {
    setIsRefreshing(true);
    try {
      await loadDocuments();
      toast.success('Documents refreshed');
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleGenerateDocument = async () => {
    if (!generateForm.title || !generateForm.prompt) {
      toast.error('Title and prompt are required');
      return;
    }

    setIsLoading(true);
    try {
      const newDocument = await documentGenerator.generateDocument(generateForm);
      setDocuments(prev => [newDocument, ...prev]);
      setShowGenerateModal(false);
      toast.success(`${getDocumentTypeName(generateForm.type)} generated successfully`);
      
      // Reset form
      setGenerateForm({
        type: 'document',
        title: '',
        prompt: '',
        template: 'standard',
        additionalContext: ''
      });
    } catch (error) {
      console.error('Error generating document:', error);
      toast.error('Failed to generate document');
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewDocument = (document: GeneratedDocument) => {
    setSelectedDocument(document);
    setShowDocumentModal(true);
  };

  const getDocumentTypeName = (type: string): string => {
    switch (type) {
      case 'document':
        return 'Document';
      case 'slide':
        return 'Presentation';
      case 'sheet':
        return 'Spreadsheet';
      case 'podcast':
        return 'Podcast';
      default:
        return 'File';
    }
  };

  const getDocumentTypeIcon = (type: string) => {
    switch (type) {
      case 'document':
        return <FileText className="w-6 h-6 text-blue-500" />;
      case 'slide':
        return <Presentation className="w-6 h-6 text-orange-500" />;
      case 'sheet':
        return <Table className="w-6 h-6 text-green-500" />;
      case 'podcast':
        return <Mic className="w-6 h-6 text-purple-500" />;
      default:
        return <FileText className="w-6 h-6 text-gray-500" />;
    }
  };

  const filteredDocuments = documents
    .filter(doc => doc.title.toLowerCase().includes(searchQuery.toLowerCase()))
    .filter(doc => !filterType || doc.type === filterType);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <RouterLink to="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <FileText className="w-5 h-5 text-white" />
                </div>
                <span className="font-bold text-gray-900 dark:text-white">Ultimate Co-founder</span>
              </RouterLink>
              <span className="mx-4 text-gray-300 dark:text-gray-600">|</span>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">Documents</h1>
            </div>
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search documents..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 w-64 bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white"
                />
              </div>
              <RouterLink to="/" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                <Link className="w-5 h-5" />
              </RouterLink>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Documents Overview */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
          <div className="flex items-start justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">AI-Generated Documents</h2>
              <p className="text-gray-600 dark:text-gray-400">
                Generate professional documents, presentations, spreadsheets, and podcasts
              </p>
            </div>
            <div className="flex items-center gap-3">
              <button 
                onClick={() => setShowGenerateModal(true)}
                className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white rounded-lg transition-colors"
              >
                <Plus className="w-4 h-4" />
                <span>Generate New</span>
              </button>
              <button 
                onClick={refreshDocuments}
                className="p-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                disabled={isRefreshing}
              >
                {isRefreshing ? (
                  <Loader2 className="w-5 h-5 animate-spin" />
                ) : (
                  <RefreshCw className="w-5 h-5" />
                )}
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 flex items-center gap-4">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
                <FileText className="w-6 h-6 text-blue-600 dark:text-blue-300" />
              </div>
              <div>
                <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">
                  {documents.filter(d => d.type === 'document').length}
                </p>
                <p className="text-sm text-blue-600 dark:text-blue-400">Documents</p>
              </div>
            </div>
            <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 flex items-center gap-4">
              <div className="w-12 h-12 bg-orange-100 dark:bg-orange-800 rounded-full flex items-center justify-center">
                <Presentation className="w-6 h-6 text-orange-600 dark:text-orange-300" />
              </div>
              <div>
                <p className="text-2xl font-bold text-orange-700 dark:text-orange-300">
                  {documents.filter(d => d.type === 'slide').length}
                </p>
                <p className="text-sm text-orange-600 dark:text-orange-400">Presentations</p>
              </div>
            </div>
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 flex items-center gap-4">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center">
                <Table className="w-6 h-6 text-green-600 dark:text-green-300" />
              </div>
              <div>
                <p className="text-2xl font-bold text-green-700 dark:text-green-300">
                  {documents.filter(d => d.type === 'sheet').length}
                </p>
                <p className="text-sm text-green-600 dark:text-green-400">Spreadsheets</p>
              </div>
            </div>
            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 flex items-center gap-4">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center">
                <Mic className="w-6 h-6 text-purple-600 dark:text-purple-300" />
              </div>
              <div>
                <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">
                  {documents.filter(d => d.type === 'podcast').length}
                </p>
                <p className="text-sm text-purple-600 dark:text-purple-400">Podcasts</p>
              </div>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search documents..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
            <select
              value={filterType || ''}
              onChange={(e) => setFilterType(e.target.value || null)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="">All Types</option>
              <option value="document">Documents</option>
              <option value="slide">Presentations</option>
              <option value="sheet">Spreadsheets</option>
              <option value="podcast">Podcasts</option>
            </select>
          </div>

          {/* Documents List */}
          {isLoading && documents.length === 0 ? (
            <div className="text-center py-12">
              <Loader2 className="w-12 h-12 text-indigo-500 mx-auto mb-4 animate-spin" />
              <p className="text-gray-600 dark:text-gray-400">Loading documents...</p>
            </div>
          ) : filteredDocuments.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">No documents found</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                {searchQuery || filterType
                  ? 'Try adjusting your search or filters'
                  : 'Generate your first document to get started'}
              </p>
              {(searchQuery || filterType) ? (
                <button
                  onClick={() => {
                    setSearchQuery('');
                    setFilterType(null);
                  }}
                  className="px-4 py-2 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                >
                  Clear Filters
                </button>
              ) : (
                <button
                  onClick={() => setShowGenerateModal(true)}
                  className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
                >
                  Generate First Document
                </button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredDocuments.map((document) => (
                <div
                  key={document.id}
                  className="bg-white dark:bg-gray-700 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => handleViewDocument(document)}
                >
                  <div className="p-4">
                    <div className="flex items-start gap-3 mb-3">
                      {getDocumentTypeIcon(document.type)}
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-gray-900 dark:text-white truncate">{document.title}</h4>
                        <div className="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400 mt-1">
                          <span className="flex items-center gap-1">
                            <Calendar className="w-3 h-3" />
                            {new Date(document.createdAt).toLocaleDateString()}
                          </span>
                          <span className="px-2 py-0.5 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-700 dark:text-indigo-300 rounded-full capitalize">
                            {document.type}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        {document.status === 'generated' ? (
                          <CheckCircle className="w-4 h-4 text-green-500" />
                        ) : document.status === 'error' ? (
                          <AlertCircle className="w-4 h-4 text-red-500" />
                        ) : (
                          <Loader2 className="w-4 h-4 text-indigo-500 animate-spin" />
                        )}
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-3">
                      {document.content}
                    </p>
                    <div className="flex justify-between items-center">
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date(document.updatedAt).toLocaleTimeString()}
                      </div>
                      <div className="flex gap-1">
                        {document.downloadUrl && (
                          <a
                            href={document.downloadUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Download className="w-4 h-4" />
                          </a>
                        )}
                        {document.previewUrl && (
                          <a
                            href={document.previewUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Eye className="w-4 h-4" />
                          </a>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Generate Document Modal */}
      {showGenerateModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-lg w-full">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">Generate New Content</h3>
                <button 
                  onClick={() => setShowGenerateModal(false)}
                  className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Content Type
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    {[
                      { value: 'document', label: 'Document', icon: FileText },
                      { value: 'slide', label: 'Presentation', icon: Presentation },
                      { value: 'sheet', label: 'Spreadsheet', icon: Table },
                      { value: 'podcast', label: 'Podcast', icon: Mic }
                    ].map((type) => (
                      <button
                        key={type.value}
                        type="button"
                        onClick={() => setGenerateForm(prev => ({ ...prev, type: type.value as any }))}
                        className={`p-3 rounded-lg text-center transition-colors ${
                          generateForm.type === type.value
                            ? 'bg-indigo-100 dark:bg-indigo-900/30 border-2 border-indigo-500'
                            : 'bg-gray-50 dark:bg-gray-700 border-2 border-transparent hover:bg-gray-100 dark:hover:bg-gray-600'
                        }`}
                      >
                        <type.icon className={`w-6 h-6 mx-auto mb-1 ${
                          generateForm.type === type.value
                            ? 'text-indigo-600 dark:text-indigo-400'
                            : 'text-gray-500 dark:text-gray-400'
                        }`} />
                        <span className={`text-sm ${
                          generateForm.type === type.value
                            ? 'font-medium text-indigo-700 dark:text-indigo-300'
                            : 'text-gray-700 dark:text-gray-300'
                        }`}>
                          {type.label}
                        </span>
                      </button>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Title
                  </label>
                  <input
                    type="text"
                    value={generateForm.title}
                    onChange={(e) => setGenerateForm(prev => ({ ...prev, title: e.target.value }))}
                    placeholder={`Enter ${getDocumentTypeName(generateForm.type).toLowerCase()} title`}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Prompt
                  </label>
                  <textarea
                    value={generateForm.prompt}
                    onChange={(e) => setGenerateForm(prev => ({ ...prev, prompt: e.target.value }))}
                    placeholder={`Describe what you want in your ${getDocumentTypeName(generateForm.type).toLowerCase()}`}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    rows={4}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Template
                  </label>
                  <select
                    value={generateForm.template}
                    onChange={(e) => setGenerateForm(prev => ({ ...prev, template: e.target.value }))}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="standard">Standard</option>
                    <option value="professional">Professional</option>
                    <option value="creative">Creative</option>
                    <option value="minimal">Minimal</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Additional Context (Optional)
                  </label>
                  <textarea
                    value={generateForm.additionalContext}
                    onChange={(e) => setGenerateForm(prev => ({ ...prev, additionalContext: e.target.value }))}
                    placeholder="Add any additional details or context"
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    rows={2}
                  />
                </div>
              </div>

              <div className="mt-6 flex gap-3">
                <button
                  onClick={handleGenerateDocument}
                  disabled={isLoading || !generateForm.title || !generateForm.prompt}
                  className="flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-500 text-white rounded-lg font-medium transition-colors disabled:cursor-not-allowed"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-5 h-5 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Plus className="w-5 h-5" />
                      Generate {getDocumentTypeName(generateForm.type)}
                    </>
                  )}
                </button>
                <button
                  onClick={() => setShowGenerateModal(false)}
                  className="px-4 py-3 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Document Details Modal */}
      {showDocumentModal && selectedDocument && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-3xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  {getDocumentTypeIcon(selectedDocument.type)}
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">{selectedDocument.title}</h3>
                </div>
                <button 
                  onClick={() => setShowDocumentModal(false)}
                  className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Content Preview</h4>
                <p className="text-gray-600 dark:text-gray-400 text-sm whitespace-pre-line">
                  {selectedDocument.content}
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Type</p>
                  <p className="font-medium text-gray-900 dark:text-white capitalize">{selectedDocument.type}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Created</p>
                  <p className="font-medium text-gray-900 dark:text-white">
                    {new Date(selectedDocument.createdAt).toLocaleString()}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Status</p>
                  <div className="flex items-center gap-2">
                    {selectedDocument.status === 'generated' ? (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    ) : selectedDocument.status === 'error' ? (
                      <AlertCircle className="w-4 h-4 text-red-500" />
                    ) : (
                      <Loader2 className="w-4 h-4 text-indigo-500 animate-spin" />
                    )}
                    <span className="font-medium text-gray-900 dark:text-white capitalize">
                      {selectedDocument.status}
                    </span>
                  </div>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Last Updated</p>
                  <p className="font-medium text-gray-900 dark:text-white">
                    {new Date(selectedDocument.updatedAt).toLocaleString()}
                  </p>
                </div>
              </div>

              <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-2">
                  {selectedDocument.downloadUrl && (
                    <a
                      href={selectedDocument.downloadUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-2 px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
                    >
                      <Download className="w-4 h-4" />
                      <span>Download</span>
                    </a>
                  )}
                  {selectedDocument.previewUrl && (
                    <a
                      href={selectedDocument.previewUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                    >
                      <ExternalLink className="w-4 h-4" />
                      <span>Preview</span>
                    </a>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <button className="p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
                    <Share2 className="w-4 h-4" />
                  </button>
                  <button className="p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
                    <Edit className="w-4 h-4" />
                  </button>
                  <button className="p-2 text-red-500 hover:text-red-700">
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};