import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  FileText, 
  Code, 
  Video, 
  Book, 
  Zap, 
  ChevronRight, 
  Search,
  ArrowLeft,
  ExternalLink,
  Copy,
  Check,
  ChevronDown,
  Hash,
  Star,
  Clock,
  Eye,
  Download,
  Bookmark,
  Share2
} from 'lucide-react';

export const Documentation: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('guides');
  const [copiedCode, setCopiedCode] = useState<string | null>(null);
  const [activeSection, setActiveSection] = useState<string | null>('getting-started');
  const [recentlyViewed, setRecentlyViewed] = useState<string[]>([]);

  useEffect(() => {
    // Load recently viewed from localStorage
    const storedRecent = localStorage.getItem('recentlyViewedDocs');
    if (storedRecent) {
      setRecentlyViewed(JSON.parse(storedRecent));
    }
  }, []);

  const copyToClipboard = (code: string) => {
    navigator.clipboard.writeText(code);
    setCopiedCode(code);
    setTimeout(() => setCopiedCode(null), 2000);
  };

  const handleSectionClick = (sectionId: string) => {
    setActiveSection(sectionId);
    
    // Update recently viewed
    setRecentlyViewed(prev => {
      const newRecent = [sectionId, ...prev.filter(id => id !== sectionId)].slice(0, 5);
      localStorage.setItem('recentlyViewedDocs', JSON.stringify(newRecent));
      return newRecent;
    });
  };

  const guides = [
    {
      id: 'getting-started',
      title: 'Getting Started',
      description: 'Learn the basics of the platform and how to set up your first project',
      sections: [
        { id: 'introduction', title: 'Introduction' },
        { id: 'account-setup', title: 'Account Setup' },
        { id: 'first-project', title: 'Your First Project' },
        { id: 'agent-communication', title: 'Agent Communication' }
      ]
    },
    {
      id: 'agent-system',
      title: 'AI Co-founder Agents',
      description: 'Detailed documentation on each specialized AI agent',
      sections: [
        { id: 'strategic-agent', title: 'Strategic Co-founder' },
        { id: 'product-agent', title: 'Product Co-founder' },
        { id: 'technical-agent', title: 'Technical Co-founder' },
        { id: 'operations-agent', title: 'Operations Co-founder' },
        { id: 'marketing-agent', title: 'Marketing Co-founder' }
      ]
    },
    {
      id: 'livekit-integration',
      title: 'LiveKit Integration',
      description: 'Real-time video and voice communication with AI agents',
      sections: [
        { id: 'livekit-setup', title: 'Setting Up LiveKit' },
        { id: 'video-sessions', title: 'Video Sessions' },
        { id: 'voice-sessions', title: 'Voice Sessions' },
        { id: 'screen-sharing', title: 'Screen Sharing' },
        { id: 'recording-sessions', title: 'Recording Sessions' }
      ]
    },
    {
      id: 'bolt-integration',
      title: 'Bolt.new Integration',
      description: 'Code generation and deployment with Bolt.new',
      sections: [
        { id: 'bolt-setup', title: 'Setting Up Bolt.new' },
        { id: 'code-generation', title: 'Code Generation' },
        { id: 'project-deployment', title: 'Project Deployment' },
        { id: 'screen-share-deploy', title: 'Screen Share Deployment' }
      ]
    },
    {
      id: 'composio-integration',
      title: 'Composio Integration',
      description: 'Connect with 200+ third-party services',
      sections: [
        { id: 'composio-setup', title: 'Setting Up Composio' },
        { id: 'available-integrations', title: 'Available Integrations' },
        { id: 'custom-workflows', title: 'Custom Workflows' }
      ]
    }
  ];

  const apiEndpoints = [
    {
      category: 'Authentication',
      endpoints: [
        { method: 'POST', path: '/api/v1/auth/login', description: 'Authenticate user and get token' },
        { method: 'POST', path: '/api/v1/auth/register', description: 'Register a new user' },
        { method: 'GET', path: '/api/v1/auth/me', description: 'Get current user info' },
        { method: 'POST', path: '/api/v1/auth/logout', description: 'Logout user' }
      ]
    },
    {
      category: 'Agents',
      endpoints: [
        { method: 'GET', path: '/api/v1/agents', description: 'List all available agents' },
        { method: 'POST', path: '/api/v1/agents/execute', description: 'Execute a task with a single agent' },
        { method: 'POST', path: '/api/v1/agents/execute-multi', description: 'Execute a task with multiple agents' },
        { method: 'GET', path: '/api/v1/agents/{agent_id}', description: 'Get specific agent details' }
      ]
    },
    {
      category: 'LiveKit',
      endpoints: [
        { method: 'POST', path: '/api/v1/livekit/sessions', description: 'Create a new LiveKit session' },
        { method: 'GET', path: '/api/v1/livekit/sessions/{session_id}', description: 'Get session details' },
        { method: 'POST', path: '/api/v1/livekit/sessions/{session_id}/start', description: 'Start a LiveKit session' },
        { method: 'POST', path: '/api/v1/livekit/sessions/{session_id}/end', description: 'End a LiveKit session' }
      ]
    },
    {
      category: 'Bolt.new',
      endpoints: [
        { method: 'POST', path: '/api/v1/bolt/scaffold', description: 'Generate code scaffold from prompt' },
        { method: 'POST', path: '/api/v1/bolt/projects', description: 'Create a new Bolt.new project' },
        { method: 'GET', path: '/api/v1/bolt/projects/{project_id}', description: 'Get project details' },
        { method: 'POST', path: '/api/v1/bolt/projects/{project_id}/deploy', description: 'Deploy a Bolt.new project' }
      ]
    }
  ];

  const tutorials = [
    {
      id: 'getting-started-video',
      title: 'Getting Started with Ultimate Co-founder',
      description: 'Learn the basics of using the platform and communicating with AI agents',
      duration: '10:23',
      date: 'June 25, 2025',
      thumbnail: 'https://images.pexels.com/photos/8386440/pexels-photo-8386440.jpeg?auto=compress&cs=tinysrgb&w=600'
    },
    {
      id: 'livekit-sessions',
      title: 'LiveKit Video Sessions with AI Co-founders',
      description: 'How to conduct effective video sessions with your AI team',
      duration: '15:47',
      date: 'June 28, 2025',
      thumbnail: 'https://images.pexels.com/photos/8386434/pexels-photo-8386434.jpeg?auto=compress&cs=tinysrgb&w=600'
    },
    {
      id: 'building-mvp',
      title: 'Building an MVP with Technical Co-founder',
      description: 'Step-by-step guide to creating your first MVP with AI assistance',
      duration: '20:12',
      date: 'July 2, 2025',
      thumbnail: 'https://images.pexels.com/photos/3861969/pexels-photo-3861969.jpeg?auto=compress&cs=tinysrgb&w=600'
    },
    {
      id: 'market-analysis',
      title: 'Market Analysis with Strategic Co-founder',
      description: 'How to conduct comprehensive market research with AI assistance',
      duration: '18:35',
      date: 'July 5, 2025',
      thumbnail: 'https://images.pexels.com/photos/7947541/pexels-photo-7947541.jpeg?auto=compress&cs=tinysrgb&w=600'
    },
    {
      id: 'product-roadmap',
      title: 'Creating a Product Roadmap',
      description: 'Learn how to build a comprehensive product roadmap with your Product Co-founder',
      duration: '22:18',
      date: 'July 8, 2025',
      thumbnail: 'https://images.pexels.com/photos/8867482/pexels-photo-8867482.jpeg?auto=compress&cs=tinysrgb&w=600'
    },
    {
      id: 'marketing-strategy',
      title: 'Developing a Marketing Strategy',
      description: 'Create a comprehensive marketing plan with your Marketing Co-founder',
      duration: '19:45',
      date: 'July 12, 2025',
      thumbnail: 'https://images.pexels.com/photos/7688336/pexels-photo-7688336.jpeg?auto=compress&cs=tinysrgb&w=600'
    }
  ];

  const examples = [
    {
      id: 'restaurant-app',
      title: 'Restaurant Management App',
      description: 'A complete example of building a restaurant management application with reservation system, menu management, and analytics dashboard',
      thumbnail: 'https://images.pexels.com/photos/262978/pexels-photo-262978.jpeg?auto=compress&cs=tinysrgb&w=600',
      components: [
        { title: 'Strategic Analysis', description: 'Market sizing and competitive landscape' },
        { title: 'Product Roadmap', description: 'Feature prioritization and user flows' },
        { title: 'Technical Architecture', description: 'System design and implementation' }
      ]
    },
    {
      id: 'saas-analytics',
      title: 'SaaS Analytics Platform',
      description: 'Building a B2B SaaS analytics platform from concept to launch with comprehensive guidance from all co-founders',
      thumbnail: 'https://images.pexels.com/photos/590022/pexels-photo-590022.jpeg?auto=compress&cs=tinysrgb&w=600',
      components: [
        { title: 'Business Model', description: 'Pricing strategy and revenue projections' },
        { title: 'Operations Plan', description: 'Legal setup and team structure' },
        { title: 'Marketing Strategy', description: 'Customer acquisition and growth tactics' }
      ]
    },
    {
      id: 'ecommerce-marketplace',
      title: 'E-commerce Marketplace',
      description: 'Creating a two-sided marketplace with comprehensive planning and implementation guidance',
      thumbnail: 'https://images.pexels.com/photos/230544/pexels-photo-230544.jpeg?auto=compress&cs=tinysrgb&w=600',
      components: [
        { title: 'Platform Architecture', description: 'Scalable system design for marketplace' },
        { title: 'User Acquisition', description: 'Strategies for both sides of marketplace' },
        { title: 'Financial Projections', description: 'Revenue model and unit economics' }
      ]
    },
    {
      id: 'mobile-fitness',
      title: 'Mobile Fitness App',
      description: 'Building a mobile fitness application with workout tracking, social features, and subscription model',
      thumbnail: 'https://images.pexels.com/photos/4498362/pexels-photo-4498362.jpeg?auto=compress&cs=tinysrgb&w=600',
      components: [
        { title: 'User Experience', description: 'Mobile-first design and engagement strategy' },
        { title: 'Subscription Model', description: 'Pricing tiers and retention tactics' },
        { title: 'Technical Stack', description: 'Cross-platform development approach' }
      ]
    }
  ];

  const filteredGuides = guides.filter(guide => 
    guide.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    guide.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredTutorials = tutorials.filter(tutorial => 
    tutorial.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    tutorial.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredExamples = examples.filter(example => 
    example.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    example.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <span className="font-bold text-gray-900 dark:text-white">Ultimate Co-founder</span>
              </Link>
              <span className="mx-4 text-gray-300 dark:text-gray-600">|</span>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">Documentation</h1>
            </div>
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search docs..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 w-64 bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
              <Link to="/" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                <ArrowLeft className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col md:flex-row gap-8">
          {/* Sidebar */}
          <div className="w-full md:w-64 flex-shrink-0">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 sticky top-24">
              <h2 className="font-bold text-gray-900 dark:text-white mb-4">Documentation</h2>
              
              <div className="space-y-1 mb-6">
                <button
                  onClick={() => setActiveTab('guides')}
                  className={`w-full flex items-center justify-between p-2 rounded-lg text-left ${
                    activeTab === 'guides'
                      ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <Book className="w-4 h-4" />
                    <span>Guides</span>
                  </div>
                  {activeTab === 'guides' && <ChevronRight className="w-4 h-4" />}
                </button>
                <button
                  onClick={() => setActiveTab('api')}
                  className={`w-full flex items-center justify-between p-2 rounded-lg text-left ${
                    activeTab === 'api'
                      ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <Code className="w-4 h-4" />
                    <span>API Reference</span>
                  </div>
                  {activeTab === 'api' && <ChevronRight className="w-4 h-4" />}
                </button>
                <button
                  onClick={() => setActiveTab('tutorials')}
                  className={`w-full flex items-center justify-between p-2 rounded-lg text-left ${
                    activeTab === 'tutorials'
                      ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <Video className="w-4 h-4" />
                    <span>Tutorials</span>
                  </div>
                  {activeTab === 'tutorials' && <ChevronRight className="w-4 h-4" />}
                </button>
                <button
                  onClick={() => setActiveTab('examples')}
                  className={`w-full flex items-center justify-between p-2 rounded-lg text-left ${
                    activeTab === 'examples'
                      ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <FileText className="w-4 h-4" />
                    <span>Examples</span>
                  </div>
                  {activeTab === 'examples' && <ChevronRight className="w-4 h-4" />}
                </button>
              </div>

              {activeTab === 'guides' && (
                <div className="space-y-4">
                  <h3 className="font-medium text-gray-900 dark:text-white mb-2">Guide Topics</h3>
                  <div className="space-y-1">
                    {guides.map((guide) => (
                      <button
                        key={guide.id}
                        onClick={() => handleSectionClick(guide.id)}
                        className={`w-full text-left p-2 text-sm rounded-lg transition-colors ${
                          activeSection === guide.id
                            ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300'
                            : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                        }`}
                      >
                        {guide.title}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {recentlyViewed.length > 0 && (
                <div className="mt-6">
                  <h3 className="font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-2">
                    <Clock className="w-4 h-4 text-gray-500" />
                    Recently Viewed
                  </h3>
                  <div className="space-y-1">
                    {recentlyViewed.map(id => {
                      const guide = guides.find(g => g.id === id);
                      if (!guide) return null;
                      return (
                        <button
                          key={id}
                          onClick={() => handleSectionClick(id)}
                          className="w-full text-left p-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                        >
                          {guide.title}
                        </button>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              {activeTab === 'guides' && (
                <div>
                  {searchQuery ? (
                    <>
                      <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                        Search Results for "{searchQuery}"
                      </h2>
                      {filteredGuides.length === 0 ? (
                        <div className="text-center py-12">
                          <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No results found</h3>
                          <p className="text-gray-600 dark:text-gray-400">
                            Try adjusting your search query or browse the guides below
                          </p>
                        </div>
                      ) : (
                        <div className="space-y-6">
                          {filteredGuides.map((guide) => (
                            <div 
                              key={guide.id}
                              className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer"
                              onClick={() => handleSectionClick(guide.id)}
                            >
                              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-1">{guide.title}</h3>
                              <p className="text-gray-600 dark:text-gray-400 mb-2">{guide.description}</p>
                              <div className="flex items-center gap-2 text-sm text-purple-600 dark:text-purple-400">
                                <span>Read guide</span>
                                <ChevronRight className="w-4 h-4" />
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </>
                  ) : (
                    <>
                      {guides.map((guide) => (
                        <div key={guide.id} className={activeSection === guide.id ? 'block' : 'hidden'}>
                          <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400 mb-4">
                            <Book className="w-4 h-4" />
                            <span>Guides</span>
                            <ChevronRight className="w-4 h-4" />
                            <span>{guide.title}</span>
                          </div>
                          
                          <div className="flex items-center justify-between mb-6">
                            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">{guide.title}</h2>
                            <div className="flex items-center gap-2">
                              <button className="p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
                                <Bookmark className="w-4 h-4" />
                              </button>
                              <button className="p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
                                <Share2 className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                          
                          <p className="text-gray-600 dark:text-gray-400 mb-8">{guide.description}</p>
                          
                          <div className="prose dark:prose-invert max-w-none">
                            {guide.id === 'getting-started' && (
                              <>
                                <h3 id="introduction" className="flex items-center gap-2 group">
                                  Introduction
                                  <a href="#introduction" className="opacity-0 group-hover:opacity-100 transition-opacity">
                                    <Hash className="w-4 h-4 text-gray-400" />
                                  </a>
                                </h3>
                                <p>
                                  Welcome to the Ultimate Co-founder documentation! This guide will help you get started with our AI-powered startup co-founder platform.
                                </p>
                                <p>
                                  Ultimate Co-founder provides a team of specialized AI agents to help you build and scale your startup. Each agent has unique expertise in strategic planning, product development, technical implementation, operations, and marketing.
                                </p>

                                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 my-6">
                                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Quick Start</h4>
                                  <ol className="list-decimal list-inside space-y-2 text-gray-700 dark:text-gray-300">
                                    <li>Create an account or sign in</li>
                                    <li>Describe your startup idea in the chat</li>
                                    <li>Receive comprehensive analysis from all five co-founders</li>
                                    <li>Dive deeper with specialized agents for specific domains</li>
                                    <li>Use LiveKit for real-time video/voice collaboration</li>
                                  </ol>
                                </div>

                                <h3 id="account-setup" className="flex items-center gap-2 group mt-8">
                                  Account Setup
                                  <a href="#account-setup" className="opacity-0 group-hover:opacity-100 transition-opacity">
                                    <Hash className="w-4 h-4 text-gray-400" />
                                  </a>
                                </h3>
                                <p>
                                  To get started with Ultimate Co-founder, you'll need to create an account. Here's how:
                                </p>
                                <ol>
                                  <li>Visit the <a href="/" className="text-purple-600 dark:text-purple-400 hover:underline">homepage</a> and click "Sign Up"</li>
                                  <li>Enter your email address and create a password</li>
                                  <li>Verify your email address</li>
                                  <li>Complete your profile with your name and startup information</li>
                                </ol>
                                <p>
                                  Once your account is set up, you'll have access to all five AI co-founder agents and can start collaborating on your startup idea.
                                </p>

                                <h3 id="first-project" className="flex items-center gap-2 group mt-8">
                                  Your First Project
                                  <a href="#first-project" className="opacity-0 group-hover:opacity-100 transition-opacity">
                                    <Hash className="w-4 h-4 text-gray-400" />
                                  </a>
                                </h3>
                                <p>
                                  Creating your first project is simple:
                                </p>
                                <ol>
                                  <li>From your dashboard, click "New Project"</li>
                                  <li>Enter a name and brief description for your project</li>
                                  <li>Select which AI co-founders you want to include (default: all five)</li>
                                  <li>Click "Create Project" to begin</li>
                                </ol>
                                <p>
                                  Your project will serve as a container for all conversations, documents, and resources related to your startup idea.
                                </p>

                                <h3 id="agent-communication" className="flex items-center gap-2 group mt-8">
                                  Agent Communication
                                  <a href="#agent-communication" className="opacity-0 group-hover:opacity-100 transition-opacity">
                                    <Hash className="w-4 h-4 text-gray-400" />
                                  </a>
                                </h3>
                                <p>
                                  You can communicate with all co-founders at once or select a specific agent for specialized guidance:
                                </p>

                                <ul className="list-disc list-inside space-y-2 text-gray-700 dark:text-gray-300">
                                  <li><strong>Strategic Co-founder:</strong> Market analysis, competitive research, and business strategy</li>
                                  <li><strong>Product Co-founder:</strong> User research, feature prioritization, and product roadmapping</li>
                                  <li><strong>Technical Co-founder:</strong> Architecture design, technology selection, and implementation guidance</li>
                                  <li><strong>Operations Co-founder:</strong> Legal setup, financial planning, and operational efficiency</li>
                                  <li><strong>Marketing Co-founder:</strong> Brand strategy, customer acquisition, and growth tactics</li>
                                </ul>

                                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 my-6 relative">
                                  <button 
                                    onClick={() => copyToClipboard(`import { liveKit } from 'ultimate-cofounder';

// Start a video session with specific agents
const session = await liveKit.createSession(
  ['strategic', 'product'], // agent IDs
  'video' // session type: 'video', 'voice', or 'screen-share'
);

// Connect to the room
await liveKit.connectToRoom(session);`)}
                                    className="absolute top-2 right-2 p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                                  >
                                    {copiedCode === `import { liveKit } from 'ultimate-cofounder';

// Start a video session with specific agents
const session = await liveKit.createSession(
  ['strategic', 'product'], // agent IDs
  'video' // session type: 'video', 'voice', or 'screen-share'
);

// Connect to the room
await liveKit.connectToRoom(session);` ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                                  </button>
                                  <pre className="text-sm overflow-x-auto p-2 text-gray-800 dark:text-gray-200">
{`import { liveKit } from 'ultimate-cofounder';

// Start a video session with specific agents
const session = await liveKit.createSession(
  ['strategic', 'product'], // agent IDs
  'video' // session type: 'video', 'voice', or 'screen-share'
);

// Connect to the room
await liveKit.connectToRoom(session);`}
                                  </pre>
                                </div>
                              </>
                            )}

                            {guide.id === 'livekit-integration' && (
                              <>
                                <h3 id="livekit-setup" className="flex items-center gap-2 group">
                                  Setting Up LiveKit
                                  <a href="#livekit-setup" className="opacity-0 group-hover:opacity-100 transition-opacity">
                                    <Hash className="w-4 h-4 text-gray-400" />
                                  </a>
                                </h3>
                                <p>
                                  LiveKit integration allows you to have real-time video and voice conversations with your AI co-founders. This creates a more natural and interactive experience, similar to meeting with human co-founders.
                                </p>
                                
                                <h4 className="font-medium text-gray-900 dark:text-white mt-6 mb-2">Requirements</h4>
                                <ul className="list-disc list-inside space-y-1 text-gray-700 dark:text-gray-300 mb-4">
                                  <li>A modern browser with WebRTC support (Chrome, Firefox, Safari, Edge)</li>
                                  <li>Camera and microphone permissions enabled</li>
                                  <li>Stable internet connection (minimum 1 Mbps upload/download)</li>
                                </ul>
                                
                                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 my-4">
                                  <h4 className="font-medium text-blue-900 dark:text-blue-200 mb-2 flex items-center gap-2">
                                    <Video className="w-4 h-4" />
                                    LiveKit Session Types
                                  </h4>
                                  <ul className="list-disc list-inside space-y-1 text-blue-800 dark:text-blue-300">
                                    <li><strong>Video:</strong> Full video and audio communication</li>
                                    <li><strong>Voice:</strong> Audio-only communication</li>
                                    <li><strong>Screen Share:</strong> Share your screen with AI agents</li>
                                  </ul>
                                </div>

                                <h3 id="video-sessions" className="flex items-center gap-2 group mt-8">
                                  Video Sessions
                                  <a href="#video-sessions" className="opacity-0 group-hover:opacity-100 transition-opacity">
                                    <Hash className="w-4 h-4 text-gray-400" />
                                  </a>
                                </h3>
                                <p>
                                  Video sessions provide the most immersive experience, allowing you to see and hear your AI co-founders. Here's how to start a video session:
                                </p>

                                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 my-6 relative">
                                  <button 
                                    onClick={() => copyToClipboard(`import { liveKit } from 'ultimate-cofounder';

// Start a video session with all co-founders
const session = await liveKit.createSession(
  ['strategic', 'product', 'technical', 'operations', 'marketing'],
  'video'
);

// Connect to the room
await liveKit.connectToRoom(session);

// End the session when done
await liveKit.endSession(session.id);`)}
                                    className="absolute top-2 right-2 p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                                  >
                                    {copiedCode === `import { liveKit } from 'ultimate-cofounder';

// Start a video session with all co-founders
const session = await liveKit.createSession(
  ['strategic', 'product', 'technical', 'operations', 'marketing'],
  'video'
);

// Connect to the room
await liveKit.connectToRoom(session);

// End the session when done
await liveKit.endSession(session.id);` ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                                  </button>
                                  <pre className="text-sm overflow-x-auto p-2 text-gray-800 dark:text-gray-200">
{`import { liveKit } from 'ultimate-cofounder';

// Start a video session with all co-founders
const session = await liveKit.createSession(
  ['strategic', 'product', 'technical', 'operations', 'marketing'],
  'video'
);

// Connect to the room
await liveKit.connectToRoom(session);

// End the session when done
await liveKit.endSession(session.id);`}
                                  </pre>
                                </div>
                              </>
                            )}

                            {guide.id === 'bolt-integration' && (
                              <>
                                <h3 id="bolt-setup" className="flex items-center gap-2 group">
                                  Setting Up Bolt.new
                                  <a href="#bolt-setup" className="opacity-0 group-hover:opacity-100 transition-opacity">
                                    <Hash className="w-4 h-4 text-gray-400" />
                                  </a>
                                </h3>
                                <p>
                                  Bolt.new integration allows you to quickly scaffold and deploy your startup's MVP. This integration works seamlessly with the Technical Co-founder agent to generate production-ready code.
                                </p>
                                
                                <h4 className="font-medium text-gray-900 dark:text-white mt-6 mb-2">Integration Methods</h4>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                    <h5 className="font-medium text-gray-900 dark:text-white mb-2">Screen Share Mode</h5>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                      Use Puppeteer + LiveKit to automate bolt.new via screen sharing. Available now.
                                    </p>
                                  </div>
                                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                    <h5 className="font-medium text-gray-900 dark:text-white mb-2">API Mode</h5>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                      Direct integration via Bolt.new API for seamless deployment. Coming soon.
                                    </p>
                                  </div>
                                </div>

                                <h3 id="code-generation" className="flex items-center gap-2 group mt-8">
                                  Code Generation
                                  <a href="#code-generation" className="opacity-0 group-hover:opacity-100 transition-opacity">
                                    <Hash className="w-4 h-4 text-gray-400" />
                                  </a>
                                </h3>
                                <p>
                                  Generate code scaffolds from natural language descriptions:
                                </p>

                                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 my-6 relative">
                                  <button 
                                    onClick={() => copyToClipboard(`import { boltIntegration } from 'ultimate-cofounder';

// Generate code scaffold from your idea
const files = await boltIntegration.generateScaffold(
  "Restaurant management app with reservation system",
  "React + Node.js + PostgreSQL"
);

// Create and deploy a project
const project = await boltIntegration.createProject(
  "restaurant-manager",
  "AI-powered restaurant management platform",
  files
);`)}
                                    className="absolute top-2 right-2 p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                                  >
                                    {copiedCode === `import { boltIntegration } from 'ultimate-cofounder';

// Generate code scaffold from your idea
const files = await boltIntegration.generateScaffold(
  "Restaurant management app with reservation system",
  "React + Node.js + PostgreSQL"
);

// Create and deploy a project
const project = await boltIntegration.createProject(
  "restaurant-manager",
  "AI-powered restaurant management platform",
  files
);` ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                                  </button>
                                  <pre className="text-sm overflow-x-auto p-2 text-gray-800 dark:text-gray-200">
{`import { boltIntegration } from 'ultimate-cofounder';

// Generate code scaffold from your idea
const files = await boltIntegration.generateScaffold(
  "Restaurant management app with reservation system",
  "React + Node.js + PostgreSQL"
);

// Create and deploy a project
const project = await boltIntegration.createProject(
  "restaurant-manager",
  "AI-powered restaurant management platform",
  files
);`}
                                  </pre>
                                </div>
                              </>
                            )}
                          </div>

                          {/* Section Navigation */}
                          <div className="mt-8 border-t border-gray-200 dark:border-gray-700 pt-6">
                            <h4 className="font-medium text-gray-900 dark:text-white mb-3">In This Guide</h4>
                            <div className="space-y-1">
                              {guide.sections.map((section) => (
                                <a
                                  key={section.id}
                                  href={`#${section.id}`}
                                  className="flex items-center gap-2 p-2 text-sm text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
                                >
                                  <ChevronRight className="w-4 h-4" />
                                  {section.title}
                                </a>
                              ))}
                            </div>
                          </div>
                        </div>
                      ))}
                    </>
                  )}
                </div>
              )}

              {activeTab === 'api' && (
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">API Reference</h2>
                  
                  <div className="prose dark:prose-invert max-w-none">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="flex-1">
                        <p className="text-gray-600 dark:text-gray-400">
                          The Ultimate Co-founder API allows you to integrate our AI co-founder capabilities into your own applications and workflows.
                        </p>
                      </div>
                      <a 
                        href="#" 
                        className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                      >
                        <Download className="w-4 h-4" />
                        OpenAPI Spec
                      </a>
                    </div>

                    <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 mb-8">
                      <h3 className="font-medium text-purple-900 dark:text-purple-200 mb-2">Base URL</h3>
                      <div className="bg-white dark:bg-gray-800 p-3 rounded-lg flex items-center justify-between">
                        <code className="text-purple-700 dark:text-purple-300 font-mono">https://api.aicofounder.site/v1</code>
                        <button 
                          onClick={() => copyToClipboard('https://api.aicofounder.site/v1')}
                          className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                        >
                          {copiedCode === 'https://api.aicofounder.site/v1' ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                        </button>
                      </div>
                    </div>

                    <div className="space-y-8">
                      {apiEndpoints.map((category, index) => (
                        <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                          <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                            <h3 className="font-medium text-gray-900 dark:text-white">{category.category}</h3>
                          </div>
                          <div className="divide-y divide-gray-200 dark:divide-gray-700">
                            {category.endpoints.map((endpoint, endpointIndex) => (
                              <div key={endpointIndex} className="p-4 hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors">
                                <div className="flex items-start justify-between mb-2">
                                  <div className="flex items-center gap-3">
                                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                                      endpoint.method === 'GET' 
                                        ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300' 
                                        : 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                                    }`}>
                                      {endpoint.method}
                                    </span>
                                    <code className="font-mono text-gray-900 dark:text-white">{endpoint.path}</code>
                                  </div>
                                  <button 
                                    onClick={() => copyToClipboard(endpoint.path)}
                                    className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                                  >
                                    {copiedCode === endpoint.path ? <Check size={14} className="text-green-500" /> : <Copy size={14} />}
                                  </button>
                                </div>
                                <p className="text-sm text-gray-600 dark:text-gray-400 ml-14">{endpoint.description}</p>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>

                    <div className="mt-8 bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                      <h3 className="font-medium text-gray-900 dark:text-white mb-4">Authentication</h3>
                      <p className="text-gray-600 dark:text-gray-400 mb-4">
                        All API requests require authentication using a Bearer token:
                      </p>

                      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg relative">
                        <button 
                          onClick={() => copyToClipboard(`curl -X POST https://api.aicofounder.site/v1/auth/login \\
  -H "Content-Type: application/json" \\
  -d '{"email": "<EMAIL>", "password": "your-password"}'`)}
                          className="absolute top-2 right-2 p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                        >
                          {copiedCode === `curl -X POST https://api.aicofounder.site/v1/auth/login \\
  -H "Content-Type: application/json" \\
  -d '{"email": "<EMAIL>", "password": "your-password"}'` ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                        </button>
                        <pre className="text-sm overflow-x-auto text-gray-800 dark:text-gray-200">
{`curl -X POST https://api.aicofounder.site/v1/auth/login \\
  -H "Content-Type: application/json" \\
  -d '{"email": "<EMAIL>", "password": "your-password"}'`}
                        </pre>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'tutorials' && (
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Video Tutorials</h2>
                  
                  {searchQuery && filteredTutorials.length === 0 ? (
                    <div className="text-center py-12">
                      <Video className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No tutorials found</h3>
                      <p className="text-gray-600 dark:text-gray-400">
                        Try adjusting your search query or browse all tutorials
                      </p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {(searchQuery ? filteredTutorials : tutorials).map((tutorial) => (
                        <div 
                          key={tutorial.id}
                          className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow"
                        >
                          <div className="relative aspect-video bg-gray-200 dark:bg-gray-700 overflow-hidden">
                            <img 
                              src={tutorial.thumbnail} 
                              alt={tutorial.title}
                              className="w-full h-full object-cover"
                            />
                            <div className="absolute inset-0 bg-black/40 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center">
                              <button className="p-4 bg-white/20 rounded-full backdrop-blur-sm">
                                <Play className="w-8 h-8 text-white" />
                              </button>
                            </div>
                            <div className="absolute bottom-2 right-2 px-2 py-1 bg-black/60 text-white text-xs rounded">
                              {tutorial.duration}
                            </div>
                          </div>
                          <div className="p-4">
                            <h3 className="font-medium text-gray-900 dark:text-white mb-2">{tutorial.title}</h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{tutorial.description}</p>
                            <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                              <span>{tutorial.date}</span>
                              <div className="flex items-center gap-2">
                                <Eye className="w-3 h-3" />
                                <span>{Math.floor(Math.random() * 1000) + 100} views</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'examples' && (
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Example Projects</h2>
                  
                  {searchQuery && filteredExamples.length === 0 ? (
                    <div className="text-center py-12">
                      <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No examples found</h3>
                      <p className="text-gray-600 dark:text-gray-400">
                        Try adjusting your search query or browse all examples
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-8">
                      {(searchQuery ? filteredExamples : examples).map((example) => (
                        <div 
                          key={example.id}
                          className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow"
                        >
                          <div className="md:flex">
                            <div className="md:w-1/3 h-48 md:h-auto bg-gray-200 dark:bg-gray-700">
                              <img 
                                src={example.thumbnail} 
                                alt={example.title}
                                className="w-full h-full object-cover"
                              />
                            </div>
                            <div className="p-6 md:w-2/3">
                              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{example.title}</h3>
                              <p className="text-gray-600 dark:text-gray-400 mb-4">{example.description}</p>
                              
                              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                {example.components.map((component, index) => (
                                  <div key={index} className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                    <h4 className="font-medium text-gray-900 dark:text-white mb-1">{component.title}</h4>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">{component.description}</p>
                                  </div>
                                ))}
                              </div>
                              
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  <Star className="w-4 h-4 text-yellow-500" />
                                  <span className="text-sm text-gray-600 dark:text-gray-400">{Math.floor(Math.random() * 500) + 100} stars</span>
                                </div>
                                <a 
                                  href={`#${example.id}`}
                                  className="flex items-center gap-1 text-purple-600 dark:text-purple-400 hover:underline"
                                >
                                  <span>View example</span>
                                  <ChevronRight className="w-4 h-4" />
                                </a>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};