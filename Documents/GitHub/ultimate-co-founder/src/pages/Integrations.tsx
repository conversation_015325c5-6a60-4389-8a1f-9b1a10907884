import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  Zap, 
  ArrowLeft, 
  Search, 
  Plus, 
  CheckCircle, 
  AlertCircle,
  Settings,
  ExternalLink,
  RefreshCw,
  Slack,
  Github,
  FileText,
  Mail,
  Calendar,
  Database,
  CreditCard,
  BarChart3,
  Phone,
  MessageSquare,
  Video,
  X,
  Loader2,
  Check,
  Clock,
  Filter,
  Globe,
  Lock,
  Shield,
  Users as UsersIcon,
  Bookmark,
  Star
} from 'lucide-react';

interface Integration {
  id: string;
  name: string;
  description: string;
  category: 'Communication' | 'Development' | 'Productivity' | 'Storage' | 'Analytics' | 'Finance' | 'CRM' | 'Security';
  icon: React.ElementType;
  status: 'connected' | 'disconnected' | 'error';
  lastSync?: string;
  features: string[];
  popular?: boolean;
  new?: boolean;
}

export const Integrations: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [showConnectModal, setShowConnectModal] = useState(false);
  const [selectedIntegration, setSelectedIntegration] = useState<Integration | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [favoriteIntegrations, setFavoriteIntegrations] = useState<string[]>([]);

  // Mock integrations data
  const [integrations, setIntegrations] = useState<Integration[]>([
    {
      id: 'slack',
      name: 'Slack',
      description: 'Team communication and notifications',
      category: 'Communication',
      icon: Slack,
      status: 'connected',
      lastSync: '2025-06-28T14:30:00Z',
      features: ['Send notifications', 'Create channels', 'Post messages', 'Manage users'],
      popular: true
    },
    {
      id: 'github',
      name: 'GitHub',
      description: 'Code repository and version control',
      category: 'Development',
      icon: Github,
      status: 'connected',
      lastSync: '2025-06-27T10:15:00Z',
      features: ['Create repositories', 'Manage issues', 'Pull requests', 'Code reviews'],
      popular: true
    },
    {
      id: 'notion',
      name: 'Notion',
      description: 'Documentation and knowledge base',
      category: 'Productivity',
      icon: FileText,
      status: 'disconnected',
      features: ['Create pages', 'Manage databases', 'Team wikis', 'Project tracking'],
      popular: true
    },
    {
      id: 'google-drive',
      name: 'Google Drive',
      description: 'File storage and collaboration',
      category: 'Storage',
      icon: Database,
      status: 'connected',
      lastSync: '2025-06-26T16:45:00Z',
      features: ['File storage', 'Document sharing', 'Collaborative editing', 'Version history']
    },
    {
      id: 'gmail',
      name: 'Gmail',
      description: 'Email communication',
      category: 'Communication',
      icon: Mail,
      status: 'disconnected',
      features: ['Send emails', 'Read inbox', 'Manage drafts', 'Email templates']
    },
    {
      id: 'google-calendar',
      name: 'Google Calendar',
      description: 'Schedule management',
      category: 'Productivity',
      icon: Calendar,
      status: 'disconnected',
      features: ['Create events', 'Manage schedules', 'Set reminders', 'Team calendars']
    },
    {
      id: 'stripe',
      name: 'Stripe',
      description: 'Payment processing',
      category: 'Finance',
      icon: CreditCard,
      status: 'disconnected',
      features: ['Process payments', 'Manage subscriptions', 'Invoice customers', 'Financial reporting']
    },
    {
      id: 'google-analytics',
      name: 'Google Analytics',
      description: 'Website analytics',
      category: 'Analytics',
      icon: BarChart3,
      status: 'disconnected',
      features: ['Track visitors', 'Analyze behavior', 'Conversion tracking', 'Custom reports']
    },
    {
      id: 'twilio',
      name: 'Twilio',
      description: 'SMS and voice communication',
      category: 'Communication',
      icon: Phone,
      status: 'disconnected',
      features: ['Send SMS', 'Voice calls', 'Verification codes', 'Automated messages']
    },
    {
      id: 'discord',
      name: 'Discord',
      description: 'Community chat platform',
      category: 'Communication',
      icon: MessageSquare,
      status: 'disconnected',
      features: ['Server management', 'Channel creation', 'Bot integration', 'Role management']
    },
    {
      id: 'zoom',
      name: 'Zoom',
      description: 'Video conferencing',
      category: 'Communication',
      icon: Video,
      status: 'disconnected',
      features: ['Schedule meetings', 'Join calls', 'Webinar hosting', 'Recording management']
    },
    {
      id: 'hubspot',
      name: 'HubSpot',
      description: 'Customer relationship management',
      category: 'CRM',
      icon: UsersIcon,
      status: 'disconnected',
      features: ['Contact management', 'Deal tracking', 'Marketing automation', 'Sales pipeline'],
      new: true
    },
    {
      id: 'auth0',
      name: 'Auth0',
      description: 'Authentication and authorization',
      category: 'Security',
      icon: Lock,
      status: 'disconnected',
      features: ['User authentication', 'Single sign-on', 'Multi-factor authentication', 'User management'],
      new: true
    },
    {
      id: 'cloudflare',
      name: 'Cloudflare',
      description: 'Web security and performance',
      category: 'Security',
      icon: Shield,
      status: 'disconnected',
      features: ['DDoS protection', 'CDN', 'DNS management', 'Web application firewall'],
      new: true
    },
    {
      id: 'shopify',
      name: 'Shopify',
      description: 'E-commerce platform',
      category: 'Finance',
      icon: CreditCard,
      status: 'disconnected',
      features: ['Product management', 'Order processing', 'Payment integration', 'Inventory tracking'],
      new: true
    }
  ]);

  useEffect(() => {
    // Simulate loading data
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);
    
    // Load favorites from localStorage
    const storedFavorites = localStorage.getItem('favoriteIntegrations');
    if (storedFavorites) {
      setFavoriteIntegrations(JSON.parse(storedFavorites));
    }
    
    return () => clearTimeout(timer);
  }, []);

  const categories = Array.from(new Set(integrations.map(i => i.category)));

  const filteredIntegrations = integrations
    .filter(integration => 
      integration.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      integration.description.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .filter(integration => 
      !selectedCategory || integration.category === selectedCategory
    );

  const connectedIntegrations = filteredIntegrations.filter(i => i.status === 'connected');
  const availableIntegrations = filteredIntegrations.filter(i => i.status === 'disconnected');
  const favoriteIntegrationsList = filteredIntegrations.filter(i => favoriteIntegrations.includes(i.id));

  const handleConnectIntegration = (integration: Integration) => {
    setSelectedIntegration(integration);
    setShowConnectModal(true);
  };

  const handleConnect = () => {
    if (!selectedIntegration) return;
    
    // Simulate connection process
    setShowConnectModal(false);
    
    // Update integration status
    setIntegrations(prev => prev.map(i => 
      i.id === selectedIntegration.id 
        ? { 
            ...i, 
            status: 'connected', 
            lastSync: new Date().toISOString() 
          } 
        : i
    ));
    
    // Show success modal
    setShowSuccessModal(true);
    
    // Auto-hide success modal after 3 seconds
    setTimeout(() => {
      setShowSuccessModal(false);
    }, 3000);
  };

  const handleDisconnect = (integrationId: string) => {
    // Update integration status
    setIntegrations(prev => prev.map(i => 
      i.id === integrationId 
        ? { 
            ...i, 
            status: 'disconnected',
            lastSync: undefined
          } 
        : i
    ));
  };

  const handleRefreshAll = () => {
    setIsRefreshing(true);
    
    // Simulate refresh process
    setTimeout(() => {
      setIntegrations(prev => prev.map(i => 
        i.status === 'connected' 
          ? { 
              ...i, 
              lastSync: new Date().toISOString() 
            } 
          : i
      ));
      setIsRefreshing(false);
    }, 1500);
  };

  const toggleFavorite = (integrationId: string) => {
    const newFavorites = favoriteIntegrations.includes(integrationId)
      ? favoriteIntegrations.filter(id => id !== integrationId)
      : [...favoriteIntegrations, integrationId];
    
    setFavoriteIntegrations(newFavorites);
    localStorage.setItem('favoriteIntegrations', JSON.stringify(newFavorites));
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Communication':
        return <MessageSquare className="w-4 h-4" />;
      case 'Development':
        return <Github className="w-4 h-4" />;
      case 'Productivity':
        return <Calendar className="w-4 h-4" />;
      case 'Storage':
        return <Database className="w-4 h-4" />;
      case 'Analytics':
        return <BarChart3 className="w-4 h-4" />;
      case 'Finance':
        return <CreditCard className="w-4 h-4" />;
      case 'CRM':
        return <UsersIcon className="w-4 h-4" />;
      case 'Security':
        return <Shield className="w-4 h-4" />;
      default:
        return <Zap className="w-4 h-4" />;
    }
  };

  const formatLastSync = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.round(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins === 1 ? '' : 's'} ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    if (diffDays < 7) return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;
    
    return date.toLocaleDateString();
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <span className="font-bold text-gray-900 dark:text-white">Ultimate Co-founder</span>
              </Link>
              <span className="mx-4 text-gray-300 dark:text-gray-600">|</span>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">Integrations</h1>
            </div>
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search integrations..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 w-64 bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
              <Link to="/" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                <ArrowLeft className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Integrations Overview */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
          <div className="flex items-start justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Integrations</h2>
              <p className="text-gray-600 dark:text-gray-400">
                Connect your favorite tools and services to enhance your co-founder experience
              </p>
            </div>
            <div className="flex items-center gap-3">
              <button 
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                className="p-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                title={viewMode === 'grid' ? 'Switch to list view' : 'Switch to grid view'}
              >
                {viewMode === 'grid' ? (
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="3" y1="6" x2="21" y2="6"></line>
                    <line x1="3" y1="12" x2="21" y2="12"></line>
                    <line x1="3" y1="18" x2="21" y2="18"></line>
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="3" y="3" width="7" height="7"></rect>
                    <rect x="14" y="3" width="7" height="7"></rect>
                    <rect x="14" y="14" width="7" height="7"></rect>
                    <rect x="3" y="14" width="7" height="7"></rect>
                  </svg>
                )}
              </button>
              <button 
                onClick={() => handleRefreshAll()}
                className="p-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                disabled={isRefreshing}
                title="Refresh all integrations"
              >
                {isRefreshing ? (
                  <Loader2 className="w-5 h-5 animate-spin" />
                ) : (
                  <RefreshCw className="w-5 h-5" />
                )}
              </button>
              <button 
                onClick={() => {
                  const newIntegration = integrations.find(i => i.status === 'disconnected');
                  if (newIntegration) {
                    handleConnectIntegration(newIntegration);
                  }
                }}
                className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
              >
                <Plus className="w-4 h-4" />
                <span>Add Integration</span>
              </button>
            </div>
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <Loader2 className="w-12 h-12 text-purple-600 mx-auto mb-4 animate-spin" />
                <p className="text-gray-600 dark:text-gray-400">Loading integrations...</p>
              </div>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 flex items-center gap-4">
                  <div className="w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center">
                    <Zap className="w-6 h-6 text-purple-600 dark:text-purple-300" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">{integrations.length}</p>
                    <p className="text-sm text-purple-600 dark:text-purple-400">Available Integrations</p>
                  </div>
                </div>
                <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 flex items-center gap-4">
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-300" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-green-700 dark:text-green-300">{integrations.filter(i => i.status === 'connected').length}</p>
                    <p className="text-sm text-green-600 dark:text-green-400">Connected</p>
                  </div>
                </div>
                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 flex items-center gap-4">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
                    <Globe className="w-6 h-6 text-blue-600 dark:text-blue-300" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">{categories.length}</p>
                    <p className="text-sm text-blue-600 dark:text-blue-400">Categories</p>
                  </div>
                </div>
                <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4 flex items-center gap-4">
                  <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-800 rounded-full flex items-center justify-center">
                    <Star className="w-6 h-6 text-yellow-600 dark:text-yellow-300" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-yellow-700 dark:text-yellow-300">{favoriteIntegrations.length}</p>
                    <p className="text-sm text-yellow-600 dark:text-yellow-400">Favorites</p>
                  </div>
                </div>
              </div>

              {/* Category Filters */}
              <div className="mb-8">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-bold text-gray-900 dark:text-white">Categories</h3>
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <Filter className="w-4 h-4" />
                    <span>Filter by category</span>
                  </div>
                </div>
                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={() => setSelectedCategory(null)}
                    className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                      selectedCategory === null
                        ? 'bg-purple-600 text-white'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                    }`}
                  >
                    All
                  </button>
                  {categories.map((category) => (
                    <button
                      key={category}
                      onClick={() => setSelectedCategory(category)}
                      className={`flex items-center gap-1.5 px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                        selectedCategory === category
                          ? 'bg-purple-600 text-white'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                      }`}
                    >
                      {getCategoryIcon(category)}
                      {category}
                    </button>
                  ))}
                </div>
              </div>

              {/* Favorites Section */}
              {favoriteIntegrationsList.length > 0 && (
                <div className="mb-8">
                  <div className="flex items-center gap-2 mb-4">
                    <Star className="w-5 h-5 text-yellow-500" />
                    <h3 className="font-bold text-gray-900 dark:text-white">Favorites</h3>
                  </div>
                  
                  <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4' : 'space-y-4'}>
                    {favoriteIntegrationsList.map((integration) => {
                      const IconComponent = integration.icon;
                      return viewMode === 'grid' ? (
                        <div
                          key={integration.id}
                          className="bg-white dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600 shadow-sm hover:shadow-md transition-shadow relative"
                        >
                          {integration.new && (
                            <span className="absolute top-2 right-2 px-2 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-xs font-medium">
                              New
                            </span>
                          )}
                          {integration.popular && !integration.new && (
                            <span className="absolute top-2 right-2 px-2 py-0.5 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-full text-xs font-medium">
                              Popular
                            </span>
                          )}
                          
                          <div className="flex items-start gap-4">
                            <div className="w-10 h-10 bg-white dark:bg-gray-800 rounded-lg flex items-center justify-center shadow-sm">
                              <IconComponent className="w-6 h-6 text-gray-700 dark:text-gray-300" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between mb-1">
                                <h4 className="font-medium text-gray-900 dark:text-white truncate">{integration.name}</h4>
                                <button 
                                  onClick={() => toggleFavorite(integration.id)}
                                  className="p-1 text-yellow-500 hover:text-yellow-600 transition-colors"
                                >
                                  <Star className="w-4 h-4 fill-current" />
                                </button>
                              </div>
                              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">{integration.description}</p>
                              <div className="flex items-center justify-between">
                                <span className="px-2 py-0.5 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full text-xs">
                                  {integration.category}
                                </span>
                                {integration.status === 'connected' ? (
                                  <div className="flex items-center gap-1 text-green-600 dark:text-green-400 text-xs">
                                    <CheckCircle className="w-3 h-3" />
                                    <span>Connected</span>
                                  </div>
                                ) : (
                                  <button
                                    onClick={() => handleConnectIntegration(integration)}
                                    className="px-2 py-1 bg-purple-600 hover:bg-purple-700 text-white rounded text-xs transition-colors"
                                  >
                                    Connect
                                  </button>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div
                          key={integration.id}
                          className="bg-white dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600 shadow-sm hover:shadow-md transition-shadow"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4">
                              <div className="w-10 h-10 bg-white dark:bg-gray-800 rounded-lg flex items-center justify-center shadow-sm">
                                <IconComponent className="w-6 h-6 text-gray-700 dark:text-gray-300" />
                              </div>
                              <div>
                                <div className="flex items-center gap-2">
                                  <h4 className="font-medium text-gray-900 dark:text-white">{integration.name}</h4>
                                  {integration.new && (
                                    <span className="px-2 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-xs font-medium">
                                      New
                                    </span>
                                  )}
                                  {integration.popular && !integration.new && (
                                    <span className="px-2 py-0.5 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-full text-xs font-medium">
                                      Popular
                                    </span>
                                  )}
                                </div>
                                <p className="text-sm text-gray-600 dark:text-gray-400">{integration.description}</p>
                              </div>
                            </div>
                            <div className="flex items-center gap-3">
                              <span className="px-2 py-0.5 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full text-xs">
                                {integration.category}
                              </span>
                              <button 
                                onClick={() => toggleFavorite(integration.id)}
                                className="p-1 text-yellow-500 hover:text-yellow-600 transition-colors"
                              >
                                <Star className="w-4 h-4 fill-current" />
                              </button>
                              {integration.status === 'connected' ? (
                                <div className="flex items-center gap-1 text-green-600 dark:text-green-400 text-xs">
                                  <CheckCircle className="w-3 h-3" />
                                  <span>Connected</span>
                                </div>
                              ) : (
                                <button
                                  onClick={() => handleConnectIntegration(integration)}
                                  className="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white rounded text-xs transition-colors"
                                >
                                  Connect
                                </button>
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Connected Integrations */}
              {connectedIntegrations.length > 0 && (
                <div className="mb-8">
                  <div className="flex items-center gap-2 mb-4">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <h3 className="font-bold text-gray-900 dark:text-white">Connected Integrations</h3>
                  </div>
                  
                  <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4' : 'space-y-4'}>
                    {connectedIntegrations.map((integration) => {
                      const IconComponent = integration.icon;
                      return viewMode === 'grid' ? (
                        <div
                          key={integration.id}
                          className="bg-white dark:bg-gray-700 rounded-lg p-4 border-2 border-green-500 shadow-sm hover:shadow-md transition-shadow"
                        >
                          <div className="flex items-start gap-4">
                            <div className="w-10 h-10 bg-white dark:bg-gray-800 rounded-lg flex items-center justify-center shadow-sm">
                              <IconComponent className="w-6 h-6 text-gray-700 dark:text-gray-300" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between mb-1">
                                <h4 className="font-medium text-gray-900 dark:text-white">{integration.name}</h4>
                                <button 
                                  onClick={() => toggleFavorite(integration.id)}
                                  className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                                >
                                  <Star className={`w-4 h-4 ${
                                    favoriteIntegrations.includes(integration.id) 
                                      ? 'text-yellow-500 fill-yellow-500' 
                                      : ''
                                  }`} />
                                </button>
                              </div>
                              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{integration.description}</p>
                              <div className="flex items-center justify-between text-xs">
                                <span className="text-gray-500 dark:text-gray-400 flex items-center gap-1">
                                  <Clock className="w-3 h-3" />
                                  {formatLastSync(integration.lastSync!)}
                                </span>
                                <div className="flex items-center gap-2">
                                  <button 
                                    onClick={() => {
                                      // Simulate sync
                                      setIntegrations(prev => prev.map(i => 
                                        i.id === integration.id 
                                          ? { ...i, lastSync: new Date().toISOString() } 
                                          : i
                                      ));
                                    }}
                                    className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                                  >
                                    <RefreshCw className="w-3 h-3" />
                                  </button>
                                  <button 
                                    onClick={() => handleDisconnect(integration.id)}
                                    className="p-1 text-red-500 hover:text-red-700 transition-colors"
                                  >
                                    <X className="w-3 h-3" />
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div
                          key={integration.id}
                          className="bg-white dark:bg-gray-700 rounded-lg p-4 border-l-4 border-green-500 shadow-sm hover:shadow-md transition-shadow"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4">
                              <div className="w-10 h-10 bg-white dark:bg-gray-800 rounded-lg flex items-center justify-center shadow-sm">
                                <IconComponent className="w-6 h-6 text-gray-700 dark:text-gray-300" />
                              </div>
                              <div>
                                <h4 className="font-medium text-gray-900 dark:text-white">{integration.name}</h4>
                                <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                                  <span>{integration.category}</span>
                                  <span>•</span>
                                  <span className="flex items-center gap-1">
                                    <Clock className="w-3 h-3" />
                                    {formatLastSync(integration.lastSync!)}
                                  </span>
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <button 
                                onClick={() => toggleFavorite(integration.id)}
                                className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                              >
                                <Star className={`w-4 h-4 ${
                                  favoriteIntegrations.includes(integration.id) 
                                    ? 'text-yellow-500 fill-yellow-500' 
                                    : ''
                                }`} />
                              </button>
                              <button 
                                onClick={() => {
                                  // Simulate sync
                                  setIntegrations(prev => prev.map(i => 
                                    i.id === integration.id 
                                      ? { ...i, lastSync: new Date().toISOString() } 
                                      : i
                                  ));
                                }}
                                className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                              >
                                <RefreshCw className="w-4 h-4" />
                              </button>
                              <button 
                                onClick={() => handleDisconnect(integration.id)}
                                className="p-1 text-red-500 hover:text-red-700 transition-colors"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Available Integrations */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-bold text-gray-900 dark:text-white">Available Integrations</h3>
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <span>{availableIntegrations.length} available</span>
                  </div>
                </div>
                
                {availableIntegrations.length === 0 ? (
                  <div className="bg-white dark:bg-gray-700 rounded-lg p-8 text-center">
                    <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No integrations found</h3>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                      {searchQuery 
                        ? 'Try adjusting your search or filters' 
                        : 'All available integrations are already connected'}
                    </p>
                    {searchQuery && (
                      <button
                        onClick={() => {
                          setSearchQuery('');
                          setSelectedCategory(null);
                        }}
                        className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                      >
                        Clear Filters
                      </button>
                    )}
                  </div>
                ) : viewMode === 'grid' ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {availableIntegrations.map((integration) => {
                      const IconComponent = integration.icon;
                      return (
                        <div
                          key={integration.id}
                          className="bg-white dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600 shadow-sm hover:shadow-md transition-shadow relative"
                        >
                          {integration.new && (
                            <span className="absolute top-2 right-2 px-2 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-xs font-medium">
                              New
                            </span>
                          )}
                          {integration.popular && !integration.new && (
                            <span className="absolute top-2 right-2 px-2 py-0.5 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-full text-xs font-medium">
                              Popular
                            </span>
                          )}
                          
                          <div className="flex items-start gap-4">
                            <div className="w-10 h-10 bg-white dark:bg-gray-800 rounded-lg flex items-center justify-center shadow-sm">
                              <IconComponent className="w-6 h-6 text-gray-700 dark:text-gray-300" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between mb-1">
                                <h4 className="font-medium text-gray-900 dark:text-white truncate">{integration.name}</h4>
                                <button 
                                  onClick={() => toggleFavorite(integration.id)}
                                  className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                                >
                                  <Star className={`w-4 h-4 ${
                                    favoriteIntegrations.includes(integration.id) 
                                      ? 'text-yellow-500 fill-yellow-500' 
                                      : ''
                                  }`} />
                                </button>
                              </div>
                              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">{integration.description}</p>
                              <div className="flex items-center justify-between">
                                <span className="px-2 py-0.5 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full text-xs">
                                  {integration.category}
                                </span>
                                <button
                                  onClick={() => handleConnectIntegration(integration)}
                                  className="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white rounded text-xs transition-colors"
                                >
                                  Connect
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {availableIntegrations.map((integration) => {
                      const IconComponent = integration.icon;
                      return (
                        <div
                          key={integration.id}
                          className="bg-white dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600 shadow-sm hover:shadow-md transition-shadow"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4">
                              <div className="w-10 h-10 bg-white dark:bg-gray-800 rounded-lg flex items-center justify-center shadow-sm">
                                <IconComponent className="w-6 h-6 text-gray-700 dark:text-gray-300" />
                              </div>
                              <div>
                                <div className="flex items-center gap-2">
                                  <h4 className="font-medium text-gray-900 dark:text-white">{integration.name}</h4>
                                  {integration.new && (
                                    <span className="px-2 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-xs font-medium">
                                      New
                                    </span>
                                  )}
                                  {integration.popular && !integration.new && (
                                    <span className="px-2 py-0.5 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-full text-xs font-medium">
                                      Popular
                                    </span>
                                  )}
                                </div>
                                <p className="text-sm text-gray-600 dark:text-gray-400">{integration.description}</p>
                              </div>
                            </div>
                            <div className="flex items-center gap-3">
                              <span className="px-2 py-0.5 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full text-xs">
                                {integration.category}
                              </span>
                              <button 
                                onClick={() => toggleFavorite(integration.id)}
                                className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                              >
                                <Star className={`w-4 h-4 ${
                                  favoriteIntegrations.includes(integration.id) 
                                    ? 'text-yellow-500 fill-yellow-500' 
                                    : ''
                                }`} />
                              </button>
                              <button
                                onClick={() => handleConnectIntegration(integration)}
                                className="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white rounded text-xs transition-colors"
                              >
                                Connect
                              </button>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </>
          )}
        </div>

        {/* Integration Marketplace Banner */}
        <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg p-6 text-white mb-8">
          <div className="flex flex-col md:flex-row items-center justify-between gap-6">
            <div>
              <h3 className="text-xl font-bold mb-2">Composio Integration Marketplace</h3>
              <p className="text-purple-100 mb-4 max-w-xl">
                Access over 200 third-party integrations through our partnership with Composio. Connect your favorite tools and automate your workflow.
              </p>
              <div className="flex flex-wrap gap-2">
                <div className="px-3 py-1 bg-white/20 rounded-full text-sm backdrop-blur-sm">Slack</div>
                <div className="px-3 py-1 bg-white/20 rounded-full text-sm backdrop-blur-sm">GitHub</div>
                <div className="px-3 py-1 bg-white/20 rounded-full text-sm backdrop-blur-sm">Notion</div>
                <div className="px-3 py-1 bg-white/20 rounded-full text-sm backdrop-blur-sm">Google Drive</div>
                <div className="px-3 py-1 bg-white/20 rounded-full text-sm backdrop-blur-sm">+195 more</div>
              </div>
            </div>
            <a 
              href="https://composio.dev" 
              target="_blank" 
              rel="noopener noreferrer"
              className="px-6 py-3 bg-white text-purple-600 rounded-lg font-medium hover:bg-gray-100 transition-colors flex items-center gap-2"
            >
              <span>Explore Marketplace</span>
              <ExternalLink className="w-4 h-4" />
            </a>
          </div>
        </div>

        {/* Popular Integrations */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Popular Integrations</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {integrations
              .filter(i => i.popular)
              .map((integration) => {
                const IconComponent = integration.icon;
                return (
                  <div
                    key={integration.id}
                    className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-center gap-3 mb-3">
                      <div className="w-10 h-10 bg-white dark:bg-gray-800 rounded-lg flex items-center justify-center shadow-sm">
                        <IconComponent className="w-6 h-6 text-gray-700 dark:text-gray-300" />
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white">{integration.name}</h4>
                        <p className="text-xs text-gray-600 dark:text-gray-400">{integration.category}</p>
                      </div>
                    </div>
                    
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">{integration.description}</p>
                    
                    <div className="flex items-center justify-between">
                      {integration.status === 'connected' ? (
                        <div className="flex items-center gap-1 text-green-600 dark:text-green-400 text-xs">
                          <CheckCircle className="w-3 h-3" />
                          <span>Connected</span>
                        </div>
                      ) : (
                        <button
                          onClick={() => handleConnectIntegration(integration)}
                          className="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white rounded text-xs transition-colors"
                        >
                          Connect
                        </button>
                      )}
                      <button 
                        onClick={() => toggleFavorite(integration.id)}
                        className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                      >
                        <Star className={`w-4 h-4 ${
                          favoriteIntegrations.includes(integration.id) 
                            ? 'text-yellow-500 fill-yellow-500' 
                            : ''
                        }`} />
                      </button>
                    </div>
                  </div>
                );
              })}
          </div>
        </div>

        {/* New Integrations */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center gap-2 mb-6">
            <div className="px-2 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-xs font-medium">
              New
            </div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white">Recently Added</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {integrations
              .filter(i => i.new)
              .map((integration) => {
                const IconComponent = integration.icon;
                return (
                  <div
                    key={integration.id}
                    className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-center gap-3 mb-3">
                      <div className="w-8 h-8 bg-white dark:bg-gray-800 rounded-lg flex items-center justify-center shadow-sm">
                        <IconComponent className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                      </div>
                      <h4 className="font-medium text-gray-900 dark:text-white">{integration.name}</h4>
                    </div>
                    
                    <p className="text-xs text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">{integration.description}</p>
                    
                    <button
                      onClick={() => handleConnectIntegration(integration)}
                      className="w-full px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white rounded text-xs transition-colors"
                    >
                      Connect
                    </button>
                  </div>
                );
              })}
          </div>
        </div>
      </div>

      {/* Connect Integration Modal */}
      {showConnectModal && selectedIntegration && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4 overflow-y-auto">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-white dark:bg-gray-700 rounded-lg flex items-center justify-center shadow-sm">
                    {React.createElement(selectedIntegration.icon, { className: "w-6 h-6 text-gray-700 dark:text-gray-300" })}
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">Connect {selectedIntegration.name}</h3>
                </div>
                <button 
                  onClick={() => setShowConnectModal(false)}
                  className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Connect your {selectedIntegration.name} account to enable these features:
              </p>

              <div className="space-y-2 mb-6">
                {selectedIntegration.features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                  </div>
                ))}
              </div>

              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 mb-6">
                <div className="flex items-center gap-2 mb-2">
                  <Shield className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  <h4 className="font-medium text-blue-900 dark:text-blue-200">Data Access</h4>
                </div>
                <p className="text-sm text-blue-700 dark:text-blue-300 mb-2">
                  Ultimate Co-founder will request the following permissions:
                </p>
                <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1 list-disc list-inside">
                  <li>Read access to your {selectedIntegration.name} data</li>
                  <li>Write access to create and update resources</li>
                  <li>Integration can be revoked at any time</li>
                </ul>
              </div>

              <div className="flex gap-3">
                <button
                  onClick={handleConnect}
                  className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                >
                  <ExternalLink className="w-4 h-4" />
                  Connect {selectedIntegration.name}
                </button>
                <button
                  onClick={() => setShowConnectModal(false)}
                  className="px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Success Modal */}
      {showSuccessModal && selectedIntegration && (
        <div className="fixed bottom-4 right-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 max-w-md w-full border-l-4 border-green-500 animate-slide-up">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
              <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-gray-900 dark:text-white">{selectedIntegration.name} Connected</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Integration successfully connected and ready to use
              </p>
            </div>
            <button 
              onClick={() => setShowSuccessModal(false)}
              className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};