import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  Code, 
  Copy, 
  Check, 
  ExternalLink, 
  ChevronDown, 
  ChevronRight,
  Zap,
  ArrowLeft,
  Key,
  Lock,
  Globe,
  Server,
  Database,
  Video,
  FileText,
  Search,
  Users as UsersIcon,
  BookOpen,
  Play,
  Download,
  Bookmark,
  Star,
  Clock,
  Filter,
  AlertCircle,
  CheckCircle,
  X
} from 'lucide-react';

interface ApiEndpoint {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  path: string;
  description: string;
  parameters?: {
    name: string;
    type: string;
    required: boolean;
    description: string;
  }[];
  responses?: {
    status: number;
    description: string;
    example?: string;
  }[];
  authentication?: boolean;
}

interface ApiCategory {
  name: string;
  description: string;
  icon: React.ElementType;
  endpoints: ApiEndpoint[];
}

export const API: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedSection, setExpandedSection] = useState<string | null>('authentication');
  const [expandedEndpoint, setExpandedEndpoint] = useState<string | null>(null);
  const [copiedCode, setCopiedCode] = useState<string | null>(null);
  const [selectedLanguage, setSelectedLanguage] = useState<'curl' | 'javascript' | 'python'>('javascript');
  const [bookmarkedEndpoints, setBookmarkedEndpoints] = useState<string[]>([]);
  const [showApiKeyModal, setShowApiKeyModal] = useState(false);
  const [apiKey, setApiKey] = useState('');
  const [recentlyViewed, setRecentlyViewed] = useState<string[]>([]);

  useEffect(() => {
    // Load bookmarks from localStorage
    const storedBookmarks = localStorage.getItem('apiBookmarks');
    if (storedBookmarks) {
      setBookmarkedEndpoints(JSON.parse(storedBookmarks));
    }

    // Load recently viewed from localStorage
    const storedRecent = localStorage.getItem('recentlyViewedEndpoints');
    if (storedRecent) {
      setRecentlyViewed(JSON.parse(storedRecent));
    }
  }, []);

  const copyToClipboard = (code: string) => {
    navigator.clipboard.writeText(code);
    setCopiedCode(code);
    setTimeout(() => setCopiedCode(null), 2000);
  };

  const toggleSection = (section: string) => {
    if (expandedSection === section) {
      setExpandedSection(null);
    } else {
      setExpandedSection(section);
    }
  };

  const toggleEndpoint = (endpoint: string) => {
    // Update recently viewed
    if (endpoint !== expandedEndpoint && endpoint) {
      const newRecent = [endpoint, ...recentlyViewed.filter(ep => ep !== endpoint)].slice(0, 5);
      setRecentlyViewed(newRecent);
      localStorage.setItem('recentlyViewedEndpoints', JSON.stringify(newRecent));
    }

    if (expandedEndpoint === endpoint) {
      setExpandedEndpoint(null);
    } else {
      setExpandedEndpoint(endpoint);
    }
  };

  const toggleBookmark = (endpoint: string) => {
    const newBookmarks = bookmarkedEndpoints.includes(endpoint)
      ? bookmarkedEndpoints.filter(ep => ep !== endpoint)
      : [...bookmarkedEndpoints, endpoint];
    
    setBookmarkedEndpoints(newBookmarks);
    localStorage.setItem('apiBookmarks', JSON.stringify(newBookmarks));
  };

  const apiCategories: ApiCategory[] = [
    {
      name: 'Authentication',
      description: 'Endpoints for user authentication and authorization',
      icon: Key,
      endpoints: [
        {
          method: 'POST',
          path: '/api/v1/auth/login',
          description: 'Authenticate user and get access token',
          parameters: [
            { name: 'email', type: 'string', required: true, description: 'User email address' },
            { name: 'password', type: 'string', required: true, description: 'User password' }
          ],
          responses: [
            { 
              status: 200, 
              description: 'Successful authentication',
              example: `{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}`
            },
            { 
              status: 401, 
              description: 'Invalid credentials',
              example: `{
  "detail": "Incorrect email or password"
}`
            }
          ],
          authentication: false
        },
        {
          method: 'POST',
          path: '/api/v1/auth/register',
          description: 'Register a new user',
          parameters: [
            { name: 'email', type: 'string', required: true, description: 'User email address' },
            { name: 'password', type: 'string', required: true, description: 'User password' },
            { name: 'name', type: 'string', required: true, description: 'User full name' }
          ],
          responses: [
            { 
              status: 200, 
              description: 'User successfully registered',
              example: `{
  "id": "user_123",
  "email": "<EMAIL>",
  "name": "John Doe",
  "created_at": "2025-06-25T13:30:00Z",
  "is_active": true
}`
            },
            { 
              status: 400, 
              description: 'Email already registered',
              example: `{
  "detail": "Email already registered"
}`
            }
          ],
          authentication: false
        },
        {
          method: 'GET',
          path: '/api/v1/auth/me',
          description: 'Get current user information',
          responses: [
            { 
              status: 200, 
              description: 'Current user information',
              example: `{
  "id": "user_123",
  "email": "<EMAIL>",
  "name": "John Doe",
  "created_at": "2025-06-25T13:30:00Z",
  "is_active": true
}`
            },
            { 
              status: 401, 
              description: 'Not authenticated',
              example: `{
  "detail": "Could not validate credentials"
}`
            }
          ],
          authentication: true
        },
        {
          method: 'POST',
          path: '/api/v1/auth/logout',
          description: 'Logout user',
          responses: [
            { 
              status: 200, 
              description: 'Successfully logged out',
              example: `{
  "message": "Successfully logged out"
}`
            }
          ],
          authentication: true
        }
      ]
    },
    {
      name: 'Agents',
      description: 'Endpoints for interacting with AI co-founder agents',
      icon: UsersIcon,
      endpoints: [
        {
          method: 'GET',
          path: '/api/v1/agents',
          description: 'List all available agents',
          responses: [
            { 
              status: 200, 
              description: 'List of available agents',
              example: `{
  "agents": [
    {
      "id": "strategic",
      "name": "Alex Strategic",
      "role": "Strategic Co-founder",
      "status": "active",
      "description": "Strategic planning, market analysis, and business direction",
      "capabilities": ["Market Research", "Strategic Planning", "Competitive Analysis"]
    },
    {
      "id": "product",
      "name": "Sam Product",
      "role": "Product Co-founder",
      "status": "active",
      "description": "Product strategy, user experience, and feature prioritization",
      "capabilities": ["Product Strategy", "UX Design", "Feature Planning"]
    }
  ]
}`
            }
          ],
          authentication: true
        },
        {
          method: 'POST',
          path: '/api/v1/agents/execute',
          description: 'Execute a task with a single agent',
          parameters: [
            { name: 'agent_id', type: 'string', required: true, description: 'ID of the agent to execute the task' },
            { name: 'description', type: 'string', required: true, description: 'Task description' },
            { name: 'context', type: 'string', required: false, description: 'Additional context for the task' }
          ],
          responses: [
            { 
              status: 200, 
              description: 'Task execution result',
              example: `{
  "task_id": "task_123",
  "agent_id": "strategic",
  "result": "Based on my analysis of the market...",
  "metadata": {
    "execution_time": 2500,
    "model": "gpt-4",
    "status": "completed"
  }
}`
            },
            { 
              status: 400, 
              description: 'Invalid agent ID or task description',
              example: `{
  "detail": "Agent strategic not found"
}`
            }
          ],
          authentication: true
        },
        {
          method: 'POST',
          path: '/api/v1/agents/execute-multi',
          description: 'Execute a task with multiple agents',
          parameters: [
            { name: 'description', type: 'string', required: true, description: 'Task description' },
            { name: 'agent_ids', type: 'array', required: false, description: 'Array of agent IDs to include (defaults to all)' }
          ],
          responses: [
            { 
              status: 200, 
              description: 'Task execution results from multiple agents',
              example: `{
  "results": [
    {
      "task_id": "task_123",
      "agent_id": "strategic",
      "result": "Based on my analysis of the market...",
      "metadata": {
        "execution_time": 2500,
        "model": "gpt-4",
        "status": "completed"
      }
    },
    {
      "task_id": "task_124",
      "agent_id": "product",
      "result": "From a product perspective...",
      "metadata": {
        "execution_time": 2300,
        "model": "gpt-4",
        "status": "completed"
      }
    }
  ]
}`
            }
          ],
          authentication: true
        },
        {
          method: 'GET',
          path: '/api/v1/agents/{agent_id}',
          description: 'Get specific agent details',
          parameters: [
            { name: 'agent_id', type: 'string', required: true, description: 'ID of the agent to retrieve' }
          ],
          responses: [
            { 
              status: 200, 
              description: 'Agent details',
              example: `{
  "id": "strategic",
  "role": "Strategic Co-founder",
  "goal": "Analyze market opportunities and develop comprehensive business strategies",
  "backstory": "Former McKinsey consultant with expertise in market analysis",
  "status": "active"
}`
            },
            { 
              status: 404, 
              description: 'Agent not found',
              example: `{
  "detail": "Agent strategic not found"
}`
            }
          ],
          authentication: true
        }
      ]
    },
    {
      name: 'LiveKit',
      description: 'Endpoints for managing real-time video/voice sessions',
      icon: Video,
      endpoints: [
        {
          method: 'POST',
          path: '/api/v1/livekit/sessions',
          description: 'Create a new LiveKit session',
          parameters: [
            { name: 'agent_ids', type: 'array', required: true, description: 'Array of agent IDs to include in the session' },
            { name: 'session_type', type: 'string', required: false, description: 'Session type: "video", "voice", or "screen-share" (default: "video")' }
          ],
          responses: [
            { 
              status: 200, 
              description: 'Session created successfully',
              example: `{
  "id": "session_123",
  "room_name": "cofounder_session_123",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "url": "wss://ultimate-cofounder.livekit.cloud",
  "status": "pending",
  "session_type": "video",
  "participants": [
    {
      "id": "user",
      "name": "You",
      "role": "user",
      "is_local": true,
      "audio_enabled": true,
      "video_enabled": true,
      "screen_share_enabled": false
    },
    {
      "id": "strategic",
      "name": "Alex Strategic",
      "role": "agent",
      "is_local": false,
      "audio_enabled": true,
      "video_enabled": true,
      "screen_share_enabled": false
    }
  ],
  "duration": 0,
  "created_at": "2025-06-25T13:30:00Z",
  "agent_ids": ["strategic", "product"]
}`
            }
          ],
          authentication: true
        },
        {
          method: 'GET',
          path: '/api/v1/livekit/sessions/{session_id}',
          description: 'Get session details',
          parameters: [
            { name: 'session_id', type: 'string', required: true, description: 'ID of the session to retrieve' }
          ],
          responses: [
            { 
              status: 200, 
              description: 'Session details',
              example: `{
  "id": "session_123",
  "room_name": "cofounder_session_123",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "url": "wss://ultimate-cofounder.livekit.cloud",
  "status": "active",
  "session_type": "video",
  "participants": [...],
  "duration": 120,
  "created_at": "2025-06-25T13:30:00Z",
  "started_at": "2025-06-25T13:31:00Z"
}`
            },
            { 
              status: 404, 
              description: 'Session not found',
              example: `{
  "detail": "Session not found"
}`
            }
          ],
          authentication: true
        },
        {
          method: 'POST',
          path: '/api/v1/livekit/sessions/{session_id}/start',
          description: 'Start a LiveKit session',
          parameters: [
            { name: 'session_id', type: 'string', required: true, description: 'ID of the session to start' }
          ],
          responses: [
            { 
              status: 200, 
              description: 'Session started successfully',
              example: `{
  "message": "Session started",
  "session": {
    "id": "session_123",
    "status": "active",
    "started_at": "2025-06-25T13:31:00Z"
  }
}`
            }
          ],
          authentication: true
        },
        {
          method: 'POST',
          path: '/api/v1/livekit/sessions/{session_id}/end',
          description: 'End a LiveKit session',
          parameters: [
            { name: 'session_id', type: 'string', required: true, description: 'ID of the session to end' }
          ],
          responses: [
            { 
              status: 200, 
              description: 'Session ended successfully',
              example: `{
  "message": "Session ended",
  "session": {
    "id": "session_123",
    "status": "completed",
    "completed_at": "2025-06-25T13:45:00Z",
    "duration": 840,
    "recording_url": "https://recordings.livekit.cloud/session_123.mp4"
  }
}`
            }
          ],
          authentication: true
        }
      ]
    },
    {
      name: 'Bolt.new',
      description: 'Endpoints for code generation and deployment',
      icon: Code,
      endpoints: [
        {
          method: 'POST',
          path: '/api/v1/bolt/scaffold',
          description: 'Generate code scaffold from prompt',
          parameters: [
            { name: 'prompt', type: 'string', required: true, description: 'Natural language description of the application' },
            { name: 'tech_stack', type: 'string', required: true, description: 'Technology stack to use (e.g., "React + Node.js + PostgreSQL")' }
          ],
          responses: [
            { 
              status: 200, 
              description: 'Generated code scaffold',
              example: `{
  "files": [
    {
      "path": "package.json",
      "content": "{\\"name\\":\\"test-app\\",\\"version\\":\\"1.0.0\\",\\"type\\":\\"module\\",\\"scripts\\":{...}}",
      "type": "file",
      "size": 1024
    },
    {
      "path": "src/App.tsx",
      "content": "import React from 'react';\\n\\nfunction App() {\\n  return (\\n    <div>\\n      <h1>Test App</h1>\\n    </div>\\n  );\\n}\\n\\nexport default App;",
      "type": "file",
      "size": 512
    }
  ]
}`
            }
          ],
          authentication: true
        },
        {
          method: 'POST',
          path: '/api/v1/bolt/projects',
          description: 'Create a new Bolt.new project',
          parameters: [
            { name: 'name', type: 'string', required: true, description: 'Project name' },
            { name: 'description', type: 'string', required: true, description: 'Project description' },
            { name: 'files', type: 'array', required: true, description: 'Array of file objects with path, content, and type' }
          ],
          responses: [
            { 
              status: 200, 
              description: 'Project created successfully',
              example: `{
  "id": "bolt_123abc",
  "name": "test-app",
  "description": "Test project description",
  "status": "ready",
  "url": "https://test-app.bolt.new",
  "downloadUrl": "https://api.bolt.new/v1/projects/bolt_123abc/download",
  "files": [...],
  "createdAt": "2025-06-25T13:30:00Z",
  "lastModified": "2025-06-25T13:30:00Z"
}`
            }
          ],
          authentication: true
        },
        {
          method: 'GET',
          path: '/api/v1/bolt/projects/{project_id}',
          description: 'Get project details',
          parameters: [
            { name: 'project_id', type: 'string', required: true, description: 'ID of the project to retrieve' }
          ],
          responses: [
            { 
              status: 200, 
              description: 'Project details',
              example: `{
  "id": "bolt_123abc",
  "name": "test-app",
  "description": "Test project description",
  "status": "ready",
  "url": "https://test-app.bolt.new",
  "downloadUrl": "https://api.bolt.new/v1/projects/bolt_123abc/download",
  "files": [...],
  "createdAt": "2025-06-25T13:30:00Z",
  "lastModified": "2025-06-25T13:30:00Z"
}`
            },
            { 
              status: 404, 
              description: 'Project not found',
              example: `{
  "detail": "Project not found"
}`
            }
          ],
          authentication: true
        },
        {
          method: 'POST',
          path: '/api/v1/bolt/projects/{project_id}/deploy',
          description: 'Deploy a Bolt.new project',
          parameters: [
            { name: 'project_id', type: 'string', required: true, description: 'ID of the project to deploy' }
          ],
          responses: [
            { 
              status: 200, 
              description: 'Deployment initiated',
              example: `{
  "id": "deploy_123abc",
  "projectId": "bolt_123abc",
  "status": "building",
  "logs": ["Starting deployment...", "Building project..."],
  "createdAt": "2025-06-25T13:30:00Z"
}`
            }
          ],
          authentication: true
        }
      ]
    },
    {
      name: 'Orchestrator',
      description: 'Endpoints for coordinating multiple AI agents',
      icon: Zap,
      endpoints: [
        {
          method: 'POST',
          path: '/api/v1/orchestrator/process',
          description: 'Process user input with AI orchestrator',
          parameters: [
            { name: 'description', type: 'string', required: true, description: 'User input to process' },
            { name: 'agent_ids', type: 'array', required: false, description: 'Array of agent IDs to include (defaults to all)' },
            { name: 'include_livekit', type: 'boolean', required: false, description: 'Whether to create a LiveKit session' }
          ],
          responses: [
            { 
              status: 200, 
              description: 'Processing results',
              example: `{
  "responses": [
    {
      "task_id": "task_123",
      "agent_id": "strategic",
      "result": "Based on my analysis...",
      "metadata": {
        "execution_time": 2500,
        "model": "gpt-4",
        "status": "completed"
      }
    }
  ],
  "suggestions": [
    {
      "id": "suggestion_123",
      "agentId": "strategic",
      "agentName": "Strategic",
      "title": "Conduct Market Analysis",
      "description": "Deep dive into TAM/SAM/SOM, competitive landscape, and market opportunities",
      "action": "market_analysis",
      "priority": "high",
      "estimatedTime": "15 min",
      "parameters": {
        "analysis_type": "comprehensive",
        "include_competitors": true
      }
    }
  ],
  "livekit_session": null,
  "timestamp": "2025-06-25T13:30:00Z"
}`
            }
          ],
          authentication: true
        },
        {
          method: 'POST',
          path: '/api/v1/orchestrator/execute-suggestion',
          description: 'Execute a suggested action',
          parameters: [
            { name: 'suggestion_id', type: 'string', required: true, description: 'ID of the suggestion to execute' },
            { name: 'agent_id', type: 'string', required: true, description: 'ID of the agent that created the suggestion' },
            { name: 'action', type: 'string', required: true, description: 'Action to execute' },
            { name: 'parameters', type: 'object', required: false, description: 'Parameters for the action' }
          ],
          responses: [
            { 
              status: 200, 
              description: 'Execution result',
              example: `{
  "suggestion_id": "suggestion_123",
  "result": {
    "task_id": "task_124",
    "agent_id": "strategic",
    "result": "I've completed the market analysis...",
    "metadata": {
      "execution_time": 3500,
      "model": "gpt-4",
      "status": "completed"
    }
  },
  "executed_at": "2025-06-25T13:35:00Z"
}`
            }
          ],
          authentication: true
        }
      ]
    }
  ];

  const codeExamples = {
    authentication: {
      curl: `curl -X POST https://api.aicofounder.site/v1/auth/login \\
  -H "Content-Type: application/json" \\
  -d '{"email": "<EMAIL>", "password": "your-password"}'`,
      javascript: `import axios from 'axios';

const login = async (email, password) => {
  try {
    const response = await axios.post('https://api.aicofounder.site/v1/auth/login', {
      email,
      password
    });
    
    const { access_token } = response.data;
    
    // Store the token for future requests
    localStorage.setItem('access_token', access_token);
    
    return response.data;
  } catch (error) {
    console.error('Authentication failed:', error);
    throw error;
  }
};`,
      python: `import requests

def login(email, password):
    try:
        response = requests.post(
            'https://api.aicofounder.site/v1/auth/login',
            json={'email': email, 'password': password}
        )
        response.raise_for_status()
        
        data = response.json()
        access_token = data['access_token']
        
        # Store the token for future requests
        return data
    except Exception as e:
        print(f"Authentication failed: {e}")
        raise`
    },
    agents: {
      curl: `curl -X POST https://api.aicofounder.site/v1/agents/execute \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \\
  -d '{
    "agent_id": "strategic",
    "description": "Analyze the market for a SaaS project management tool",
    "context": "Targeting small to medium businesses"
  }'`,
      javascript: `import axios from 'axios';

const executeAgentTask = async (agentId, description, context = null) => {
  try {
    const token = localStorage.getItem('access_token');
    
    const response = await axios.post(
      'https://api.aicofounder.site/v1/agents/execute',
      {
        agent_id: agentId,
        description,
        context
      },
      {
        headers: {
          'Authorization': \`Bearer \${token}\`
        }
      }
    );
    
    return response.data;
  } catch (error) {
    console.error('Task execution failed:', error);
    throw error;
  }
};`,
      python: `import requests

def execute_agent_task(agent_id, description, context=None, access_token=None):
    try:
        headers = {
            'Authorization': f'Bearer {access_token}'
        }
        
        payload = {
            'agent_id': agent_id,
            'description': description
        }
        
        if context:
            payload['context'] = context
            
        response = requests.post(
            'https://api.aicofounder.site/v1/agents/execute',
            json=payload,
            headers=headers
        )
        response.raise_for_status()
        
        return response.json()
    except Exception as e:
        print(f"Task execution failed: {e}")
        raise`
    },
    livekit: {
      curl: `curl -X POST https://api.aicofounder.site/v1/livekit/sessions \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \\
  -d '{
    "agent_ids": ["strategic", "product"],
    "session_type": "video"
  }'`,
      javascript: `import axios from 'axios';

const createLiveKitSession = async (agentIds, sessionType = 'video') => {
  try {
    const token = localStorage.getItem('access_token');
    
    const response = await axios.post(
      'https://api.aicofounder.site/v1/livekit/sessions',
      {
        agent_ids: agentIds,
        session_type: sessionType
      },
      {
        headers: {
          'Authorization': \`Bearer \${token}\`
        }
      }
    );
    
    return response.data;
  } catch (error) {
    console.error('Failed to create LiveKit session:', error);
    throw error;
  }
};`,
      python: `import requests

def create_livekit_session(agent_ids, session_type='video', access_token=None):
    try:
        headers = {
            'Authorization': f'Bearer {access_token}'
        }
        
        payload = {
            'agent_ids': agent_ids,
            'session_type': session_type
        }
            
        response = requests.post(
            'https://api.aicofounder.site/v1/livekit/sessions',
            json=payload,
            headers=headers
        )
        response.raise_for_status()
        
        return response.json()
    except Exception as e:
        print(f"Failed to create LiveKit session: {e}")
        raise`
    },
    bolt: {
      curl: `curl -X POST https://api.aicofounder.site/v1/bolt/scaffold \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \\
  -d '{
    "prompt": "Restaurant management app with reservation system",
    "tech_stack": "React + Node.js + PostgreSQL"
  }'`,
      javascript: `import axios from 'axios';

const generateScaffold = async (prompt, techStack) => {
  try {
    const token = localStorage.getItem('access_token');
    
    const response = await axios.post(
      'https://api.aicofounder.site/v1/bolt/scaffold',
      {
        prompt,
        tech_stack: techStack
      },
      {
        headers: {
          'Authorization': \`Bearer \${token}\`
        }
      }
    );
    
    return response.data.files;
  } catch (error) {
    console.error('Failed to generate scaffold:', error);
    throw error;
  }
};`,
      python: `import requests

def generate_scaffold(prompt, tech_stack, access_token=None):
    try:
        headers = {
            'Authorization': f'Bearer {access_token}'
        }
        
        payload = {
            'prompt': prompt,
            'tech_stack': tech_stack
        }
            
        response = requests.post(
            'https://api.aicofounder.site/v1/bolt/scaffold',
            json=payload,
            headers=headers
        )
        response.raise_for_status()
        
        return response.json()['files']
    except Exception as e:
        print(f"Failed to generate scaffold: {e}")
        raise`
    },
    orchestrator: {
      curl: `curl -X POST https://api.aicofounder.site/v1/orchestrator/process \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \\
  -d '{
    "description": "I want to build a restaurant management app",
    "agent_ids": ["strategic", "product", "technical"],
    "include_livekit": false
  }'`,
      javascript: `import axios from 'axios';

const processUserInput = async (description, agentIds = null, includeLiveKit = false) => {
  try {
    const token = localStorage.getItem('access_token');
    
    const response = await axios.post(
      'https://api.aicofounder.site/v1/orchestrator/process',
      {
        description,
        agent_ids: agentIds,
        include_livekit: includeLiveKit
      },
      {
        headers: {
          'Authorization': \`Bearer \${token}\`
        }
      }
    );
    
    return response.data;
  } catch (error) {
    console.error('Failed to process input:', error);
    throw error;
  }
};`,
      python: `import requests

def process_user_input(description, agent_ids=None, include_livekit=False, access_token=None):
    try:
        headers = {
            'Authorization': f'Bearer {access_token}'
        }
        
        payload = {
            'description': description,
            'include_livekit': include_livekit
        }
        
        if agent_ids:
            payload['agent_ids'] = agent_ids
            
        response = requests.post(
            'https://api.aicofounder.site/v1/orchestrator/process',
            json=payload,
            headers=headers
        )
        response.raise_for_status()
        
        return response.json()
    except Exception as e:
        print(f"Failed to process input: {e}")
        raise`
    }
  };

  // Filter categories and endpoints based on search
  const filteredCategories = apiCategories.map(category => ({
    ...category,
    endpoints: category.endpoints.filter(endpoint => 
      endpoint.path.toLowerCase().includes(searchQuery.toLowerCase()) ||
      endpoint.description.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })).filter(category => category.endpoints.length > 0);

  // Get bookmarked endpoints
  const bookmarkedEndpointsList = apiCategories
    .flatMap(category => category.endpoints)
    .filter(endpoint => bookmarkedEndpoints.includes(endpoint.path));

  // Get recently viewed endpoints
  const recentlyViewedEndpointsList = apiCategories
    .flatMap(category => category.endpoints)
    .filter(endpoint => recentlyViewed.includes(endpoint.path));

  const generateApiKey = () => {
    const key = `uc_${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`;
    setApiKey(key);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <span className="font-bold text-gray-900 dark:text-white">Ultimate Co-founder</span>
              </Link>
              <span className="mx-4 text-gray-300 dark:text-gray-600">|</span>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">API Reference</h1>
            </div>
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search API docs..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 w-64 bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
              <Link to="/" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                <ArrowLeft className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col md:flex-row gap-8">
          {/* Sidebar */}
          <div className="w-full md:w-64 flex-shrink-0">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 sticky top-24">
              <h2 className="font-bold text-gray-900 dark:text-white mb-4">API Reference</h2>
              
              <div className="space-y-1 mb-6">
                {apiCategories.map((category) => (
                  <button
                    key={category.name}
                    onClick={() => toggleSection(category.name)}
                    className={`w-full flex items-center justify-between p-2 rounded-lg text-left ${
                      expandedSection === category.name
                        ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300'
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      <category.icon className="w-4 h-4" />
                      <span>{category.name}</span>
                    </div>
                    {expandedSection === category.name ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
                  </button>
                ))}
              </div>

              {bookmarkedEndpointsList.length > 0 && (
                <div className="mb-6">
                  <h3 className="font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-2">
                    <Bookmark className="w-4 h-4 text-yellow-500" />
                    Bookmarked
                  </h3>
                  <div className="space-y-1">
                    {bookmarkedEndpointsList.map((endpoint) => (
                      <button
                        key={endpoint.path}
                        onClick={() => toggleEndpoint(endpoint.path)}
                        className="w-full text-left p-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center gap-2"
                      >
                        <span className={`px-1.5 py-0.5 text-xs font-medium rounded ${
                          endpoint.method === 'GET' 
                            ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300' 
                            : 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                        }`}>
                          {endpoint.method}
                        </span>
                        <span className="truncate">{endpoint.path.split('/').pop()}</span>
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {recentlyViewedEndpointsList.length > 0 && (
                <div className="mb-6">
                  <h3 className="font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-2">
                    <Clock className="w-4 h-4 text-gray-500" />
                    Recently Viewed
                  </h3>
                  <div className="space-y-1">
                    {recentlyViewedEndpointsList.map((endpoint) => (
                      <button
                        key={endpoint.path}
                        onClick={() => toggleEndpoint(endpoint.path)}
                        className="w-full text-left p-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center gap-2"
                      >
                        <span className={`px-1.5 py-0.5 text-xs font-medium rounded ${
                          endpoint.method === 'GET' 
                            ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300' 
                            : 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                        }`}>
                          {endpoint.method}
                        </span>
                        <span className="truncate">{endpoint.path.split('/').pop()}</span>
                      </button>
                    ))}
                  </div>
                </div>
              )}

              <h3 className="font-medium text-gray-900 dark:text-white mb-2">Resources</h3>
              <ul className="space-y-1 text-sm">
                <li>
                  <a 
                    href="#" 
                    className="flex items-center gap-1 p-2 text-purple-600 dark:text-purple-400 hover:underline hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  >
                    <FileText className="w-4 h-4" />
                    <span>API Status</span>
                  </a>
                </li>
                <li>
                  <a 
                    href="#" 
                    className="flex items-center gap-1 p-2 text-purple-600 dark:text-purple-400 hover:underline hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  >
                    <Download className="w-4 h-4" />
                    <span>OpenAPI Spec</span>
                  </a>
                </li>
                <li>
                  <a 
                    href="#" 
                    className="flex items-center gap-1 p-2 text-purple-600 dark:text-purple-400 hover:underline hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  >
                    <Server className="w-4 h-4" />
                    <span>Rate Limits</span>
                  </a>
                </li>
                <li>
                  <a 
                    href="#" 
                    className="flex items-center gap-1 p-2 text-purple-600 dark:text-purple-400 hover:underline hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  >
                    <BookOpen className="w-4 h-4" />
                    <span>SDK Documentation</span>
                  </a>
                </li>
              </ul>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              {searchQuery ? (
                <div className="mb-8">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Search Results: "{searchQuery}"</h2>
                  
                  {filteredCategories.length === 0 ? (
                    <div className="text-center py-12">
                      <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No results found</h3>
                      <p className="text-gray-600 dark:text-gray-400">
                        Try adjusting your search query or browse the API reference
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-8">
                      {filteredCategories.map((category) => (
                        <div key={category.name}>
                          <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                            <category.icon className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                            {category.name}
                          </h3>
                          <div className="space-y-4">
                            {category.endpoints.map((endpoint) => (
                              <div 
                                key={endpoint.path}
                                className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden"
                              >
                                <div 
                                  className="bg-gray-50 dark:bg-gray-700 px-4 py-3 flex items-center justify-between cursor-pointer"
                                  onClick={() => toggleEndpoint(endpoint.path)}
                                >
                                  <div className="flex items-center gap-3">
                                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                                      endpoint.method === 'GET' 
                                        ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300' 
                                        : endpoint.method === 'POST'
                                        ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                                        : endpoint.method === 'PUT'
                                        ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300'
                                        : 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300'
                                    }`}>
                                      {endpoint.method}
                                    </span>
                                    <code className="font-mono text-gray-900 dark:text-white">{endpoint.path}</code>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <button 
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        toggleBookmark(endpoint.path);
                                      }}
                                      className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                                    >
                                      <Star className={`w-4 h-4 ${
                                        bookmarkedEndpoints.includes(endpoint.path) 
                                          ? 'text-yellow-500 fill-yellow-500' 
                                          : ''
                                      }`} />
                                    </button>
                                    {expandedEndpoint === endpoint.path ? (
                                      <ChevronDown className="w-4 h-4 text-gray-500" />
                                    ) : (
                                      <ChevronRight className="w-4 h-4 text-gray-500" />
                                    )}
                                  </div>
                                </div>
                                
                                {expandedEndpoint === endpoint.path && (
                                  <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                                    <p className="text-gray-600 dark:text-gray-400 mb-4">{endpoint.description}</p>
                                    
                                    {endpoint.authentication && (
                                      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-4">
                                        <Lock className="w-4 h-4 text-yellow-500" />
                                        <span>Requires authentication</span>
                                      </div>
                                    )}
                                    
                                    {endpoint.parameters && endpoint.parameters.length > 0 && (
                                      <div className="mb-4">
                                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">Parameters</h4>
                                        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden">
                                          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                                            <thead>
                                              <tr className="bg-gray-100 dark:bg-gray-600">
                                                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Name</th>
                                                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Type</th>
                                                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Required</th>
                                                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Description</th>
                                              </tr>
                                            </thead>
                                            <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
                                              {endpoint.parameters.map((param, index) => (
                                                <tr key={index} className="hover:bg-gray-100 dark:hover:bg-gray-650">
                                                  <td className="px-4 py-2 text-sm font-medium text-gray-900 dark:text-white">{param.name}</td>
                                                  <td className="px-4 py-2 text-sm text-gray-600 dark:text-gray-400">{param.type}</td>
                                                  <td className="px-4 py-2 text-sm">
                                                    {param.required ? (
                                                      <span className="text-red-600 dark:text-red-400">Yes</span>
                                                    ) : (
                                                      <span className="text-gray-500 dark:text-gray-400">No</span>
                                                    )}
                                                  </td>
                                                  <td className="px-4 py-2 text-sm text-gray-600 dark:text-gray-400">{param.description}</td>
                                                </tr>
                                              ))}
                                            </tbody>
                                          </table>
                                        </div>
                                      </div>
                                    )}
                                    
                                    {endpoint.responses && endpoint.responses.length > 0 && (
                                      <div className="mb-4">
                                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">Responses</h4>
                                        <div className="space-y-3">
                                          {endpoint.responses.map((response, index) => (
                                            <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                                              <div className="bg-gray-50 dark:bg-gray-700 px-4 py-2 flex items-center justify-between">
                                                <div className="flex items-center gap-2">
                                                  <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${
                                                    response.status >= 200 && response.status < 300
                                                      ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                                                      : response.status >= 400
                                                      ? 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300'
                                                      : 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300'
                                                  }`}>
                                                    {response.status}
                                                  </span>
                                                  <span className="text-sm text-gray-700 dark:text-gray-300">{response.description}</span>
                                                </div>
                                              </div>
                                              {response.example && (
                                                <div className="p-4 relative">
                                                  <button 
                                                    onClick={() => copyToClipboard(response.example)}
                                                    className="absolute top-2 right-2 p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                                                  >
                                                    {copiedCode === response.example ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                                                  </button>
                                                  <pre className="text-sm overflow-x-auto text-gray-800 dark:text-gray-200 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                                                    {response.example}
                                                  </pre>
                                                </div>
                                              )}
                                            </div>
                                          ))}
                                        </div>
                                      </div>
                                    )}
                                    
                                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden">
                                      <div className="flex border-b border-gray-200 dark:border-gray-600">
                                        {(['curl', 'javascript', 'python'] as const).map((lang) => (
                                          <button
                                            key={lang}
                                            onClick={() => setSelectedLanguage(lang)}
                                            className={`px-4 py-2 text-sm font-medium ${
                                              selectedLanguage === lang
                                                ? 'bg-white dark:bg-gray-800 text-purple-600 dark:text-purple-400'
                                                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                                            }`}
                                          >
                                            {lang === 'curl' ? 'cURL' : lang === 'javascript' ? 'JavaScript' : 'Python'}
                                          </button>
                                        ))}
                                      </div>
                                      <div className="p-4 relative">
                                        <button 
                                          onClick={() => {
                                            const codeCategory = category.name.toLowerCase();
                                            const code = codeExamples[codeCategory]?.[selectedLanguage] || '';
                                            copyToClipboard(code);
                                          }}
                                          className="absolute top-2 right-2 p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                                        >
                                          {copiedCode === codeExamples[category.name.toLowerCase()]?.[selectedLanguage] ? 
                                            <Check size={16} className="text-green-500" /> : 
                                            <Copy size={16} />
                                          }
                                        </button>
                                        <pre className="text-sm overflow-x-auto text-gray-800 dark:text-gray-200">
                                          {codeExamples[category.name.toLowerCase()]?.[selectedLanguage] || 'Code example not available'}
                                        </pre>
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <div className="mb-8">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Ultimate Co-founder API</h2>
                  
                  <div className="flex items-center gap-4 mb-6">
                    <div className="flex-1">
                      <p className="text-gray-600 dark:text-gray-400">
                        Our REST API gives you programmatic access to the entire Ultimate Co-founder platform. Build custom integrations, automate workflows, and extend the capabilities of your AI co-founder team.
                      </p>
                    </div>
                    <a 
                      href="#" 
                      className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                    >
                      <Download className="w-4 h-4" />
                      OpenAPI Spec
                    </a>
                  </div>

                  <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 mb-8">
                    <h3 className="font-medium text-purple-900 dark:text-purple-200 mb-2">Base URL</h3>
                    <div className="bg-white dark:bg-gray-800 p-3 rounded-lg flex items-center justify-between">
                      <code className="text-purple-700 dark:text-purple-300 font-mono">https://api.aicofounder.site/v1</code>
                      <button 
                        onClick={() => copyToClipboard('https://api.aicofounder.site/v1')}
                        className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                      >
                        {copiedCode === 'https://api.aicofounder.site/v1' ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                      </button>
                    </div>
                  </div>

                  {expandedSection === 'Authentication' && (
                    <div className="mb-8">
                      <div className="flex items-center gap-3 mb-4">
                        <Key className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                        <h2 className="text-xl font-bold text-gray-900 dark:text-white">Authentication</h2>
                      </div>
                      
                      <p className="text-gray-600 dark:text-gray-400 mb-4">
                        The Ultimate Co-founder API uses bearer token authentication. You'll need to obtain an access token by authenticating with your email and password.
                      </p>

                      <div className="mb-6">
                        <div className="flex items-center gap-2 mb-3">
                          <h3 className="font-medium text-gray-900 dark:text-white">Login Endpoint</h3>
                          <span className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-xs">POST</span>
                        </div>
                        <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg mb-3">
                          <code className="text-gray-800 dark:text-gray-200 font-mono">/auth/login</code>
                        </div>
                        <p className="text-gray-600 dark:text-gray-400 mb-3">
                          This endpoint returns an access token that you'll use for all subsequent API requests.
                        </p>

                        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden mb-4">
                          <div className="flex border-b border-gray-200 dark:border-gray-600">
                            {(['curl', 'javascript', 'python'] as const).map((lang) => (
                              <button
                                key={lang}
                                onClick={() => setSelectedLanguage(lang)}
                                className={`px-4 py-2 text-sm font-medium ${
                                  selectedLanguage === lang
                                    ? 'bg-white dark:bg-gray-800 text-purple-600 dark:text-purple-400'
                                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                                }`}
                              >
                                {lang === 'curl' ? 'cURL' : lang === 'javascript' ? 'JavaScript' : 'Python'}
                              </button>
                            ))}
                          </div>
                          <div className="p-4 relative">
                            <button 
                              onClick={() => copyToClipboard(codeExamples.authentication[selectedLanguage])}
                              className="absolute top-2 right-2 p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                            >
                              {copiedCode === codeExamples.authentication[selectedLanguage] ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                            </button>
                            <pre className="text-sm overflow-x-auto text-gray-800 dark:text-gray-200">
                              {codeExamples.authentication[selectedLanguage]}
                            </pre>
                          </div>
                        </div>

                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">Response</h4>
                        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg relative">
                          <button 
                            onClick={() => copyToClipboard(`{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}`)}
                            className="absolute top-2 right-2 p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                          >
                            {copiedCode === `{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}` ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                          </button>
                          <pre className="text-sm overflow-x-auto text-gray-800 dark:text-gray-200">
{`{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}`}
                          </pre>
                        </div>
                      </div>

                      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                        <h4 className="font-medium text-blue-900 dark:text-blue-200 mb-2 flex items-center gap-2">
                          <Lock className="w-4 h-4" />
                          Using Your Token
                        </h4>
                        <p className="text-blue-700 dark:text-blue-300 text-sm mb-3">
                          Include the token in the Authorization header for all API requests:
                        </p>
                        <div className="bg-white dark:bg-gray-800 p-3 rounded-lg">
                          <code className="text-blue-700 dark:text-blue-300 font-mono">
                            Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                          </code>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* API Keys Section */}
                  <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg p-6 mt-8 text-white">
                    <div className="flex items-center gap-3 mb-4">
                      <Key className="w-6 h-6 text-white" />
                      <h2 className="text-xl font-bold">Get Your API Key</h2>
                    </div>
                    <p className="mb-6 text-purple-100">
                      To use the Ultimate Co-founder API, you'll need to generate an API key. API keys provide secure access to our API endpoints and allow us to track usage.
                    </p>
                    <button 
                      onClick={() => setShowApiKeyModal(true)}
                      className="inline-flex items-center gap-2 px-4 py-2 bg-white text-purple-600 rounded-lg font-medium transition-colors hover:bg-gray-100"
                    >
                      <Key className="w-4 h-4" />
                      <span>Generate API Key</span>
                    </button>
                  </div>

                  {/* SDK Section */}
                  <div className="mt-8">
                    <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Client SDKs</h2>
                    <p className="text-gray-600 dark:text-gray-400 mb-6">
                      We provide official client libraries for several popular programming languages to make integrating with our API even easier.
                    </p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <a 
                        href="#" 
                        className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-3"
                      >
                        <div className="w-10 h-10 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-yellow-600 dark:text-yellow-400">
                            <path d="M12 19c-4.3 0-7.8-3.4-7.8-7.8 0-4.3 3.4-7.8 7.8-7.8 4.3 0 7.8 3.4 7.8 7.8 0 4.3-3.4 7.8-7.8 7.8z"></path>
                            <path d="M12 19v-7.8"></path>
                            <path d="M12 11.2V3.4"></path>
                            <path d="M12 3.4c-4.3 0-7.8 3.4-7.8 7.8"></path>
                          </svg>
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900 dark:text-white">JavaScript</h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">npm install @ultimate-cofounder/js</p>
                        </div>
                      </a>
                      <a 
                        href="#" 
                        className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-3"
                      >
                        <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600 dark:text-blue-400">
                            <path d="M12 9H7.5a2.5 2.5 0 0 1 0-5H12v18"></path>
                            <path d="M12 12h4.5a2.5 2.5 0 0 0 0-5H12"></path>
                          </svg>
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900 dark:text-white">Python</h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">pip install ultimate-cofounder</p>
                        </div>
                      </a>
                      <a 
                        href="#" 
                        className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-3"
                      >
                        <div className="w-10 h-10 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-red-600 dark:text-red-400">
                            <path d="M16 12a4 4 0 1 0-8 0 4 4 0 0 0 8 0z"></path>
                            <path d="M12 21.5a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19z"></path>
                          </svg>
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900 dark:text-white">Ruby</h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">gem install ultimate_cofounder</p>
                        </div>
                      </a>
                    </div>
                  </div>

                  {/* Getting Started */}
                  <div className="mt-8">
                    <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Getting Started</h2>
                    <p className="text-gray-600 dark:text-gray-400 mb-6">
                      Follow these steps to start using the Ultimate Co-founder API:
                    </p>
                    
                    <div className="space-y-4">
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center text-purple-600 dark:text-purple-400 font-medium">1</div>
                        <div>
                          <h3 className="font-medium text-gray-900 dark:text-white mb-1">Generate an API Key</h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            Create an API key from your dashboard or by clicking the "Generate API Key" button above.
                          </p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center text-purple-600 dark:text-purple-400 font-medium">2</div>
                        <div>
                          <h3 className="font-medium text-gray-900 dark:text-white mb-1">Authenticate</h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            Use the <code className="text-purple-600 dark:text-purple-400 font-mono">/auth/login</code> endpoint to get an access token.
                          </p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center text-purple-600 dark:text-purple-400 font-medium">3</div>
                        <div>
                          <h3 className="font-medium text-gray-900 dark:text-white mb-1">Make API Requests</h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            Include your access token in the Authorization header for all API requests.
                          </p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center text-purple-600 dark:text-purple-400 font-medium">4</div>
                        <div>
                          <h3 className="font-medium text-gray-900 dark:text-white mb-1">Handle Responses</h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            Process the API responses according to your application's needs.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* API Key Modal */}
      {showApiKeyModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">Generate API Key</h3>
                <button 
                  onClick={() => setShowApiKeyModal(false)}
                  className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Your API key provides access to the Ultimate Co-founder API. Keep it secure and never share it publicly.
              </p>

              {apiKey ? (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Your API Key
                  </label>
                  <div className="flex">
                    <input
                      type="text"
                      value={apiKey}
                      readOnly
                      className="flex-1 p-3 border border-gray-300 dark:border-gray-600 rounded-l-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                    <button
                      onClick={() => copyToClipboard(apiKey)}
                      className="px-3 bg-gray-100 dark:bg-gray-600 border border-l-0 border-gray-300 dark:border-gray-600 rounded-r-lg text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors"
                    >
                      {copiedCode === apiKey ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                    </button>
                  </div>
                  <p className="text-sm text-red-600 dark:text-red-400 mt-2">
                    <AlertCircle className="w-4 h-4 inline mr-1" />
                    Save this key now. You won't be able to see it again!
                  </p>
                </div>
              ) : (
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                  <div className="flex items-center gap-3 mb-2">
                    <Shield className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                    <h4 className="font-medium text-gray-900 dark:text-white">API Key Information</h4>
                  </div>
                  <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>Full access to all API endpoints</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>Rate limit of 100 requests per minute</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>Usage analytics in your dashboard</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <AlertCircle className="w-4 h-4 text-yellow-500 mt-0.5" />
                      <span>Store securely and never share publicly</span>
                    </li>
                  </ul>
                </div>
              )}

              <div className="flex gap-3">
                {apiKey ? (
                  <button
                    onClick={() => setShowApiKeyModal(false)}
                    className="flex-1 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                  >
                    Done
                  </button>
                ) : (
                  <button
                    onClick={generateApiKey}
                    className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                  >
                    <Key className="w-4 h-4" />
                    Generate API Key
                  </button>
                )}
                <button
                  onClick={() => setShowApiKeyModal(false)}
                  className="px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};