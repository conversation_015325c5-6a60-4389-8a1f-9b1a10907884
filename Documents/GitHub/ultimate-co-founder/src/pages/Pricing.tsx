import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  Zap, 
  ArrowLeft, 
  Check, 
  X, 
  HelpCircle,
  CreditCard,
  Users,
  Video,
  Code,
  Headphones,
  Building2,
  ChevronRight,
  CheckCircle
} from 'lucide-react';

interface PricingTier {
  name: string;
  price: number;
  description: string;
  features: Array<{
    name: string;
    included: boolean;
    highlight?: boolean;
  }>;
  cta: string;
  popular?: boolean;
}

export const Pricing: React.FC = () => {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'annual'>('monthly');

  const pricingTiers: PricingTier[] = [
    {
      name: 'Starter',
      price: billingCycle === 'monthly' ? 29 : 290,
      description: 'Perfect for solo entrepreneurs and early-stage startups',
      features: [
        { name: '2 AI Co-founder Agents (Strategic + Product)', included: true },
        { name: '5 LiveKit sessions per month', included: true },
        { name: '3 projects with basic analytics', included: true },
        { name: 'Standard email support', included: true },
        { name: 'Basic integrations (Slack, GitHub)', included: true },
        { name: 'Community access', included: true },
        { name: 'Standard templates', included: true },
        { name: 'Custom agent training', included: false },
        { name: 'API access', included: false },
        { name: 'White-label customization', included: false }
      ],
      cta: 'Start Free Trial'
    },
    {
      name: 'Professional',
      price: billingCycle === 'monthly' ? 99 : 990,
      description: 'Comprehensive solution for growing startups and small teams',
      features: [
        { name: 'All 5 AI Co-founder Agents', included: true, highlight: true },
        { name: '25 LiveKit sessions per month', included: true, highlight: true },
        { name: 'Unlimited projects with advanced analytics', included: true },
        { name: 'Priority email + chat support', included: true },
        { name: 'Full Composio integration suite', included: true },
        { name: 'Custom agent training', included: true },
        { name: 'Advanced templates & workflows', included: true },
        { name: 'Team collaboration features', included: true },
        { name: 'API access (1000 calls/month)', included: true },
        { name: 'White-label customization', included: false }
      ],
      cta: 'Start Free Trial',
      popular: true
    },
    {
      name: 'Enterprise',
      price: billingCycle === 'monthly' ? 299 : 2990,
      description: 'Full-scale solution for established companies and agencies',
      features: [
        { name: 'All 5 AI Co-founder Agents + Custom Agents', included: true },
        { name: 'Unlimited LiveKit sessions', included: true },
        { name: 'Unlimited projects with enterprise analytics', included: true },
        { name: '24/7 phone + dedicated support', included: true, highlight: true },
        { name: 'White-label customization', included: true, highlight: true },
        { name: 'Advanced security & compliance', included: true },
        { name: 'Custom integrations & workflows', included: true },
        { name: 'Multi-team management', included: true },
        { name: 'Unlimited API access', included: true },
        { name: 'On-premise deployment option', included: true }
      ],
      cta: 'Contact Sales'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <span className="font-bold text-gray-900 dark:text-white">Ultimate Co-founder</span>
              </Link>
              <span className="mx-4 text-gray-300 dark:text-gray-600">|</span>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">Pricing</h1>
            </div>
            <div className="flex items-center gap-4">
              <Link to="/" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                <ArrowLeft className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Pricing Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">Simple, Transparent Pricing</h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto mb-8">
            Choose the perfect plan for your startup journey with our AI co-founder team
          </p>

          {/* Billing Toggle */}
          <div className="flex items-center justify-center gap-4 mb-8">
            <span className={`text-sm font-medium ${billingCycle === 'monthly' ? 'text-gray-900 dark:text-white' : 'text-gray-500 dark:text-gray-400'}`}>
              Monthly
            </span>
            <button
              onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'annual' : 'monthly')}
              className="relative inline-flex h-6 w-11 items-center rounded-full bg-purple-600"
            >
              <span className="sr-only">Toggle billing cycle</span>
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${
                  billingCycle === 'annual' ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`text-sm font-medium ${billingCycle === 'annual' ? 'text-gray-900 dark:text-white' : 'text-gray-500 dark:text-gray-400'}`}>
              Annual <span className="text-green-600 dark:text-green-400">(Save 17%)</span>
            </span>
          </div>
        </div>

        {/* Pricing Tiers */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {pricingTiers.map((tier, index) => (
            <div
              key={index}
              className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border-2 ${
                tier.popular
                  ? 'border-purple-500 shadow-lg shadow-purple-100 dark:shadow-none relative'
                  : 'border-gray-200 dark:border-gray-700'
              }`}
            >
              {tier.popular && (
                <div className="absolute -top-4 left-0 right-0 flex justify-center">
                  <span className="bg-purple-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                </div>
              )}

              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{tier.name}</h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">{tier.description}</p>
                
                <div className="mb-6">
                  <span className="text-4xl font-bold text-gray-900 dark:text-white">${tier.price}</span>
                  <span className="text-gray-600 dark:text-gray-400">/{billingCycle === 'monthly' ? 'month' : 'year'}</span>
                </div>
                
                <button
                  className={`w-full py-3 rounded-lg font-medium transition-colors ${
                    tier.popular
                      ? 'bg-purple-600 hover:bg-purple-700 text-white'
                      : 'bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-white'
                  }`}
                >
                  {tier.cta}
                </button>
              </div>

              <div className="border-t border-gray-200 dark:border-gray-700 p-6">
                <h4 className="font-medium text-gray-900 dark:text-white mb-4">Features</h4>
                <ul className="space-y-3">
                  {tier.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start gap-3">
                      {feature.included ? (
                        <CheckCircle className={`w-5 h-5 mt-0.5 ${feature.highlight ? 'text-purple-600 dark:text-purple-400' : 'text-green-600 dark:text-green-400'}`} />
                      ) : (
                        <X className="w-5 h-5 mt-0.5 text-gray-400" />
                      )}
                      <span className={`text-sm ${feature.included ? feature.highlight ? 'text-gray-900 dark:text-white font-medium' : 'text-gray-700 dark:text-gray-300' : 'text-gray-500 dark:text-gray-500'}`}>
                        {feature.name}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>

        {/* Enterprise Section */}
        <div className="mt-16 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl p-8 text-white">
          <div className="flex flex-col md:flex-row items-center justify-between gap-8">
            <div>
              <div className="flex items-center gap-3 mb-4">
                <Building2 className="w-6 h-6" />
                <h2 className="text-2xl font-bold">Enterprise Solutions</h2>
              </div>
              <p className="text-purple-100 mb-6 max-w-xl">
                Need a custom solution for your organization? Our enterprise plan includes dedicated support, custom integrations, and advanced security features.
              </p>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Check className="w-5 h-5 text-purple-200" />
                  <span>Custom AI agent development</span>
                </div>
                <div className="flex items-center gap-3">
                  <Check className="w-5 h-5 text-purple-200" />
                  <span>Dedicated account manager</span>
                </div>
                <div className="flex items-center gap-3">
                  <Check className="w-5 h-5 text-purple-200" />
                  <span>Advanced security & compliance</span>
                </div>
                <div className="flex items-center gap-3">
                  <Check className="w-5 h-5 text-purple-200" />
                  <span>Custom SLAs and priority support</span>
                </div>
              </div>
            </div>
            <div>
              <Link 
                to="/contact"
                className="inline-flex items-center gap-2 px-6 py-3 bg-white text-purple-600 rounded-lg font-medium hover:bg-gray-100 transition-colors"
              >
                Contact Sales
                <ChevronRight className="w-4 h-4" />
              </Link>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mt-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white text-center mb-8">Frequently Asked Questions</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[
              {
                question: 'How does the free trial work?',
                answer: 'Our 14-day free trial gives you full access to all features of your selected plan. No credit card required to start, and you can cancel anytime before the trial ends.'
              },
              {
                question: 'Can I switch plans later?',
                answer: "Yes, you can upgrade or downgrade your plan at any time. When upgrading, you'll be charged the prorated difference. When downgrading, the new rate will apply at the start of your next billing cycle."
              },
              {
                question: 'What happens when I reach my session limit?',
                answer: 'If you reach your monthly LiveKit session limit, you can purchase additional sessions or upgrade to a higher tier plan with more included sessions.'
              },
              {
                question: 'Do you offer discounts for startups?',
                answer: 'Yes, we offer special pricing for startups in accelerator programs like Y Combinator, Techstars, and 500 Startups. Contact our sales team for details.'
              },
              {
                question: 'How does billing work?',
                answer: 'We bill monthly or annually depending on your preference. All plans are auto-renewed unless canceled. You can update your billing information in your account settings.'
              },
              {
                question: 'What payment methods do you accept?',
                answer: 'We accept all major credit cards (Visa, Mastercard, American Express) as well as PayPal. Enterprise customers can also pay by invoice.'
              }
            ].map((faq, index) => (
              <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3 flex items-start gap-3">
                  <HelpCircle className="w-5 h-5 text-purple-600 dark:text-purple-400 flex-shrink-0 mt-0.5" />
                  <span>{faq.question}</span>
                </h3>
                <p className="text-gray-600 dark:text-gray-400 ml-8">{faq.answer}</p>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Ready to Get Started?</h2>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto mb-8">
            Join thousands of founders who are building successful startups with our AI co-founder team
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <button className="px-8 py-4 bg-purple-600 hover:bg-purple-700 text-white rounded-xl font-semibold text-lg transition-all">
              Start Free Trial
            </button>
            <Link 
              to="/contact"
              className="px-8 py-4 bg-white border-2 border-gray-300 hover:border-gray-400 text-gray-900 dark:text-white dark:border-gray-700 dark:hover:border-gray-600 rounded-xl font-semibold text-lg transition-all"
            >
              Talk to Sales
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};