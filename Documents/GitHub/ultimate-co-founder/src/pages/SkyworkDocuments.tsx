import React, { useState, useEffect } from 'react';
import { 
  FileText, 
  Presentation, 
  Table, 
  Mic, 
  Download, 
  ExternalLink, 
  Plus,
  Loader2,
  CheckCircle,
  AlertCircle,
  Search,
  Filter,
  Calendar,
  Clock,
  X,
  RefreshCw,
  Trash2,
  Edit,
  Share2,
  Eye,
  Zap,
  ArrowLeft,
  Star,
  MoreHorizontal,
  ChevronDown,
  ChevronUp,
  Users,
  Tag,
  Bookmark,
  Link
} from 'lucide-react';
import { Link as RouterLink } from 'react-router-dom';
import { skyworkAI, SkyworkDocument, GenerateDocumentRequest } from '../services/skywork-ai';
import toast from 'react-hot-toast';

export const SkyworkDocuments: React.FC = () => {
  const [documents, setDocuments] = useState<SkyworkDocument[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showGenerateModal, setShowGenerateModal] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<SkyworkDocument | null>(null);
  const [showDocumentModal, setShowDocumentModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [sortBy, setSortBy] = useState<'date' | 'title' | 'type'>('date');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [starredDocuments, setStarredDocuments] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Form state for document generation
  const [generateForm, setGenerateForm] = useState<GenerateDocumentRequest>({
    type: 'document',
    title: '',
    prompt: '',
    template: 'standard',
    additionalContext: ''
  });

  // Mock tags for documents
  const [tags, setTags] = useState<Record<string, string[]>>({
    'doc_1687824000000': ['Business Plan', 'Startup', 'Finance'],
    'slide_1687867200000': ['Pitch Deck', 'Investor', 'Presentation'],
    'sheet_1687910400000': ['Financial', 'Projections', 'Budget']
  });

  // Mock document sharing
  const [sharedWith, setSharedWith] = useState<Record<string, string[]>>({
    'doc_1687824000000': ['Team', 'Investors'],
    'slide_1687867200000': ['Investors'],
    'sheet_1687910400000': ['Team', 'Advisors']
  });

  useEffect(() => {
    loadDocuments();
    
    // Load starred documents from localStorage
    const storedStarred = localStorage.getItem('starredDocuments');
    if (storedStarred) {
      setStarredDocuments(JSON.parse(storedStarred));
    }
  }, []);

  const loadDocuments = async () => {
    setIsLoading(true);
    try {
      // Mock documents since the API call is failing
      const mockDocuments: SkyworkDocument[] = [
        {
          id: 'doc_1687824000000',
          type: 'document',
          title: 'Business Plan',
          content: 'Comprehensive business plan for startup.',
          createdAt: new Date(Date.now() - 86400000).toISOString(),
          updatedAt: new Date(Date.now() - 86400000).toISOString(),
          status: 'generated',
          downloadUrl: 'https://github.com/SkyworkAI/business-plan.docx',
          previewUrl: 'https://github.com/SkyworkAI/business-plan-preview.html'
        },
        {
          id: 'slide_1687867200000',
          type: 'slide',
          title: 'Investor Pitch Deck',
          content: 'Pitch deck for investor presentation.',
          createdAt: new Date(Date.now() - 43200000).toISOString(),
          updatedAt: new Date(Date.now() - 43200000).toISOString(),
          status: 'generated',
          downloadUrl: 'https://github.com/SkyworkAI/investor-pitch.pptx',
          previewUrl: 'https://github.com/SkyworkAI/investor-pitch-preview.html'
        },
        {
          id: 'sheet_1687910400000',
          type: 'sheet',
          title: 'Financial Projections',
          content: 'Three-year financial projections.',
          createdAt: new Date(Date.now() - 21600000).toISOString(),
          updatedAt: new Date(Date.now() - 21600000).toISOString(),
          status: 'generated',
          downloadUrl: 'https://github.com/SkyworkAI/financial-projections.xlsx',
          previewUrl: 'https://github.com/SkyworkAI/financial-projections-preview.html'
        },
        {
          id: 'podcast_1687953600000',
          type: 'podcast',
          title: 'Startup Journey Episode 1',
          content: 'First episode of startup journey podcast.',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          status: 'generated',
          downloadUrl: 'https://github.com/SkyworkAI/startup-journey-ep1.mp3',
          previewUrl: 'https://github.com/SkyworkAI/startup-journey-ep1-preview.html'
        },
        {
          id: 'doc_1687996800000',
          type: 'document',
          title: 'Marketing Strategy',
          content: 'Comprehensive marketing strategy document.',
          createdAt: new Date(Date.now() - 129600000).toISOString(),
          updatedAt: new Date(Date.now() - 129600000).toISOString(),
          status: 'generated',
          downloadUrl: 'https://github.com/SkyworkAI/marketing-strategy.docx',
          previewUrl: 'https://github.com/SkyworkAI/marketing-strategy-preview.html'
        },
        {
          id: 'slide_1688040000000',
          type: 'slide',
          title: 'Product Roadmap',
          content: 'Product development roadmap presentation.',
          createdAt: new Date(Date.now() - 172800000).toISOString(),
          updatedAt: new Date(Date.now() - 172800000).toISOString(),
          status: 'generated',
          downloadUrl: 'https://github.com/SkyworkAI/product-roadmap.pptx',
          previewUrl: 'https://github.com/SkyworkAI/product-roadmap-preview.html'
        },
        {
          id: 'doc_1688083200000',
          type: 'document',
          title: 'Competitive Analysis',
          content: 'In-depth analysis of market competitors.',
          createdAt: new Date(Date.now() - 259200000).toISOString(),
          updatedAt: new Date(Date.now() - 259200000).toISOString(),
          status: 'generated',
          downloadUrl: 'https://github.com/SkyworkAI/competitive-analysis.docx',
          previewUrl: 'https://github.com/SkyworkAI/competitive-analysis-preview.html'
        },
        {
          id: 'sheet_1688126400000',
          type: 'sheet',
          title: 'Customer Acquisition Model',
          content: 'Detailed customer acquisition cost analysis.',
          createdAt: new Date(Date.now() - 345600000).toISOString(),
          updatedAt: new Date(Date.now() - 345600000).toISOString(),
          status: 'generated',
          downloadUrl: 'https://github.com/SkyworkAI/customer-acquisition.xlsx',
          previewUrl: 'https://github.com/SkyworkAI/customer-acquisition-preview.html'
        }
      ];
      
      setDocuments(mockDocuments);
    } catch (error) {
      console.error('Error loading documents:', error);
      toast.error('Failed to load documents');
    } finally {
      setIsLoading(false);
    }
  };

  const refreshDocuments = async () => {
    setIsRefreshing(true);
    try {
      await loadDocuments();
      toast.success('Documents refreshed');
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleGenerateDocument = async () => {
    if (!generateForm.title || !generateForm.prompt) {
      toast.error('Title and prompt are required');
      return;
    }

    setIsLoading(true);
    try {
      const newDocument = await skyworkAI.generateDocument(generateForm);
      setDocuments(prev => [newDocument, ...prev]);
      setShowGenerateModal(false);
      toast.success(`${getDocumentTypeName(generateForm.type)} generated successfully`);
      
      // Reset form
      setGenerateForm({
        type: 'document',
        title: '',
        prompt: '',
        template: 'standard',
        additionalContext: ''
      });
    } catch (error) {
      console.error('Error generating document:', error);
      toast.error('Failed to generate document');
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewDocument = (document: SkyworkDocument) => {
    setSelectedDocument(document);
    setShowDocumentModal(true);
  };

  const handleDeleteDocument = (documentId: string) => {
    setDocuments(prev => prev.filter(doc => doc.id !== documentId));
    if (selectedDocument?.id === documentId) {
      setShowDocumentModal(false);
    }
    toast.success('Document deleted');
  };

  const toggleStar = (documentId: string) => {
    const newStarred = starredDocuments.includes(documentId)
      ? starredDocuments.filter(id => id !== documentId)
      : [...starredDocuments, documentId];
    
    setStarredDocuments(newStarred);
    localStorage.setItem('starredDocuments', JSON.stringify(newStarred));
  };

  const getDocumentTypeName = (type: string): string => {
    switch (type) {
      case 'document':
        return 'Document';
      case 'slide':
        return 'Presentation';
      case 'sheet':
        return 'Spreadsheet';
      case 'podcast':
        return 'Podcast';
      default:
        return 'File';
    }
  };

  const getDocumentTypeIcon = (type: string) => {
    switch (type) {
      case 'document':
        return <FileText className="w-6 h-6 text-blue-500" />;
      case 'slide':
        return <Presentation className="w-6 h-6 text-orange-500" />;
      case 'sheet':
        return <Table className="w-6 h-6 text-green-500" />;
      case 'podcast':
        return <Mic className="w-6 h-6 text-purple-500" />;
      default:
        return <FileText className="w-6 h-6 text-gray-500" />;
    }
  };

  const handleSort = (field: 'date' | 'title' | 'type') => {
    if (sortBy === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortDirection('asc');
    }
  };

  // Filter and sort documents
  const filteredDocuments = documents
    .filter(doc => 
      doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doc.content.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .filter(doc => !filterType || doc.type === filterType)
    .sort((a, b) => {
      if (sortBy === 'date') {
        return sortDirection === 'asc'
          ? new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          : new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      } else if (sortBy === 'title') {
        return sortDirection === 'asc'
          ? a.title.localeCompare(b.title)
          : b.title.localeCompare(a.title);
      } else if (sortBy === 'type') {
        return sortDirection === 'asc'
          ? a.type.localeCompare(b.type)
          : b.type.localeCompare(a.type);
      }
      return 0;
    });

  // Get starred documents
  const starredDocumentsList = documents.filter(doc => starredDocuments.includes(doc.id));

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <RouterLink to="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <span className="font-bold text-gray-900 dark:text-white">Ultimate Co-founder</span>
              </RouterLink>
              <span className="mx-4 text-gray-300 dark:text-gray-600">|</span>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">SkyworkAI Documents</h1>
            </div>
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search documents..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 w-64 bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
              <RouterLink to="/" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                <ArrowLeft className="w-5 h-5" />
              </RouterLink>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Documents Overview */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
          <div className="flex items-start justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">SkyworkAI Documents</h2>
              <p className="text-gray-600 dark:text-gray-400">
                Generate professional documents, presentations, spreadsheets, and podcasts
              </p>
            </div>
            <div className="flex items-center gap-3">
              <button 
                onClick={() => setShowGenerateModal(true)}
                className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white rounded-lg transition-colors"
              >
                <Plus className="w-4 h-4" />
                <span>Generate New</span>
              </button>
              <button 
                onClick={refreshDocuments}
                className="p-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                disabled={isRefreshing}
              >
                {isRefreshing ? (
                  <Loader2 className="w-5 h-5 animate-spin" />
                ) : (
                  <RefreshCw className="w-5 h-5" />
                )}
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 flex items-center gap-4">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
                <FileText className="w-6 h-6 text-blue-600 dark:text-blue-300" />
              </div>
              <div>
                <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">
                  {documents.filter(d => d.type === 'document').length}
                </p>
                <p className="text-sm text-blue-600 dark:text-blue-400">Documents</p>
              </div>
            </div>
            <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 flex items-center gap-4">
              <div className="w-12 h-12 bg-orange-100 dark:bg-orange-800 rounded-full flex items-center justify-center">
                <Presentation className="w-6 h-6 text-orange-600 dark:text-orange-300" />
              </div>
              <div>
                <p className="text-2xl font-bold text-orange-700 dark:text-orange-300">
                  {documents.filter(d => d.type === 'slide').length}
                </p>
                <p className="text-sm text-orange-600 dark:text-orange-400">Presentations</p>
              </div>
            </div>
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 flex items-center gap-4">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center">
                <Table className="w-6 h-6 text-green-600 dark:text-green-300" />
              </div>
              <div>
                <p className="text-2xl font-bold text-green-700 dark:text-green-300">
                  {documents.filter(d => d.type === 'sheet').length}
                </p>
                <p className="text-sm text-green-600 dark:text-green-400">Spreadsheets</p>
              </div>
            </div>
            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 flex items-center gap-4">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center">
                <Mic className="w-6 h-6 text-purple-600 dark:text-purple-300" />
              </div>
              <div>
                <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">
                  {documents.filter(d => d.type === 'podcast').length}
                </p>
                <p className="text-sm text-purple-600 dark:text-purple-400">Podcasts</p>
              </div>
            </div>
          </div>

          {/* View Controls */}
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-1 border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 ${
                    viewMode === 'grid'
                      ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300'
                      : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600'
                  }`}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="3" y="3" width="7" height="7"></rect>
                    <rect x="14" y="3" width="7" height="7"></rect>
                    <rect x="14" y="14" width="7" height="7"></rect>
                    <rect x="3" y="14" width="7" height="7"></rect>
                  </svg>
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 ${
                    viewMode === 'list'
                      ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300'
                      : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600'
                  }`}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="3" y1="6" x2="21" y2="6"></line>
                    <line x1="3" y1="12" x2="21" y2="12"></line>
                    <line x1="3" y1="18" x2="21" y2="18"></line>
                  </svg>
                </button>
              </div>
              
              <select
                value={filterType || ''}
                onChange={(e) => setFilterType(e.target.value || null)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="">All Types</option>
                <option value="document">Documents</option>
                <option value="slide">Presentations</option>
                <option value="sheet">Spreadsheets</option>
                <option value="podcast">Podcasts</option>
              </select>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                <span>Sort by:</span>
                <button
                  onClick={() => handleSort('date')}
                  className={`flex items-center gap-1 px-2 py-1 rounded ${
                    sortBy === 'date' ? 'bg-gray-200 dark:bg-gray-700 font-medium' : ''
                  }`}
                >
                  <span>Date</span>
                  {sortBy === 'date' && (
                    sortDirection === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                  )}
                </button>
                <button
                  onClick={() => handleSort('title')}
                  className={`flex items-center gap-1 px-2 py-1 rounded ${
                    sortBy === 'title' ? 'bg-gray-200 dark:bg-gray-700 font-medium' : ''
                  }`}
                >
                  <span>Title</span>
                  {sortBy === 'title' && (
                    sortDirection === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                  )}
                </button>
                <button
                  onClick={() => handleSort('type')}
                  className={`flex items-center gap-1 px-2 py-1 rounded ${
                    sortBy === 'type' ? 'bg-gray-200 dark:bg-gray-700 font-medium' : ''
                  }`}
                >
                  <span>Type</span>
                  {sortBy === 'type' && (
                    sortDirection === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Starred Documents */}
          {starredDocumentsList.length > 0 && (
            <div className="mb-8">
              <div className="flex items-center gap-2 mb-4">
                <Star className="w-5 h-5 text-yellow-500" />
                <h3 className="font-bold text-gray-900 dark:text-white">Starred Documents</h3>
              </div>
              
              <div className={viewMode === 'grid' 
                ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4' 
                : 'space-y-3'
              }>
                {starredDocumentsList.map((document) => (
                  viewMode === 'grid' ? (
                    <div
                      key={document.id}
                      className="bg-white dark:bg-gray-700 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer border border-gray-200 dark:border-gray-600 overflow-hidden"
                      onClick={() => handleViewDocument(document)}
                    >
                      <div className="p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-start gap-3">
                            {getDocumentTypeIcon(document.type)}
                            <div>
                              <h4 className="font-medium text-gray-900 dark:text-white line-clamp-1">{document.title}</h4>
                              <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400 mt-1">
                                <span className="capitalize">{document.type}</span>
                                <span>•</span>
                                <span>{new Date(document.createdAt).toLocaleDateString()}</span>
                              </div>
                            </div>
                          </div>
                          <button 
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleStar(document.id);
                            }}
                            className="text-yellow-500"
                          >
                            <Star className="w-4 h-4 fill-current" />
                          </button>
                        </div>
                        
                        <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-3">
                          {document.content}
                        </p>
                        
                        {tags[document.id] && (
                          <div className="flex flex-wrap gap-1 mb-3">
                            {tags[document.id].slice(0, 2).map((tag, i) => (
                              <span key={i} className="px-2 py-0.5 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full text-xs">
                                {tag}
                              </span>
                            ))}
                            {tags[document.id].length > 2 && (
                              <span className="px-2 py-0.5 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full text-xs">
                                +{tags[document.id].length - 2}
                              </span>
                            )}
                          </div>
                        )}
                        
                        <div className="flex justify-between items-center">
                          {document.status === 'generated' ? (
                            <span className="flex items-center gap-1 text-green-600 dark:text-green-400 text-xs">
                              <CheckCircle className="w-3 h-3" />
                              Ready
                            </span>
                          ) : (
                            <span className="flex items-center gap-1 text-yellow-600 dark:text-yellow-400 text-xs">
                              <Loader2 className="w-3 h-3 animate-spin" />
                              Processing
                            </span>
                          )}
                          
                          <div className="flex gap-1" onClick={e => e.stopPropagation()}>
                            {document.downloadUrl && (
                              <a
                                href={document.downloadUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                              >
                                <Download className="w-4 h-4" />
                              </a>
                            )}
                            {document.previewUrl && (
                              <a
                                href={document.previewUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                              >
                                <Eye className="w-4 h-4" />
                              </a>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div
                      key={document.id}
                      className="bg-white dark:bg-gray-700 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer border border-gray-200 dark:border-gray-600 p-4"
                      onClick={() => handleViewDocument(document)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          {getDocumentTypeIcon(document.type)}
                          <div>
                            <h4 className="font-medium text-gray-900 dark:text-white">{document.title}</h4>
                            <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                              <span className="capitalize">{document.type}</span>
                              <span>•</span>
                              <span>{new Date(document.createdAt).toLocaleDateString()}</span>
                              {tags[document.id] && (
                                <>
                                  <span>•</span>
                                  <div className="flex items-center gap-1">
                                    <Tag className="w-3 h-3" />
                                    <span>{tags[document.id].length} tags</span>
                                  </div>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2" onClick={e => e.stopPropagation()}>
                          <button 
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleStar(document.id);
                            }}
                            className="text-yellow-500"
                          >
                            <Star className="w-4 h-4 fill-current" />
                          </button>
                          
                          {document.downloadUrl && (
                            <a
                              href={document.downloadUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                            >
                              <Download className="w-4 h-4" />
                            </a>
                          )}
                          
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteDocument(document.id);
                            }}
                            className="p-1 text-red-500 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  )
                ))}
              </div>
            </div>
          )}

          {/* Documents List */}
          {isLoading && documents.length === 0 ? (
            <div className="text-center py-12">
              <Loader2 className="w-12 h-12 text-indigo-500 mx-auto mb-4 animate-spin" />
              <p className="text-gray-600 dark:text-gray-400">Loading documents...</p>
            </div>
          ) : filteredDocuments.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">No documents found</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                {searchQuery || filterType
                  ? 'Try adjusting your search or filters'
                  : 'Generate your first document to get started'}
              </p>
              {(searchQuery || filterType) ? (
                <button
                  onClick={() => {
                    setSearchQuery('');
                    setFilterType(null);
                  }}
                  className="px-4 py-2 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                >
                  Clear Filters
                </button>
              ) : (
                <button
                  onClick={() => setShowGenerateModal(true)}
                  className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
                >
                  Generate First Document
                </button>
              )}
            </div>
          ) : viewMode === 'grid' ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {filteredDocuments.map((document) => (
                <div
                  key={document.id}
                  className="bg-white dark:bg-gray-700 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer border border-gray-200 dark:border-gray-600 overflow-hidden"
                  onClick={() => handleViewDocument(document)}
                >
                  <div className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-start gap-3">
                        {getDocumentTypeIcon(document.type)}
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-white line-clamp-1">{document.title}</h4>
                          <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400 mt-1">
                            <span className="capitalize">{document.type}</span>
                            <span>•</span>
                            <span>{new Date(document.createdAt).toLocaleDateString()}</span>
                          </div>
                        </div>
                      </div>
                      <button 
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleStar(document.id);
                        }}
                        className={`text-gray-400 hover:text-yellow-500 ${
                          starredDocuments.includes(document.id) ? 'text-yellow-500' : ''
                        }`}
                      >
                        <Star className={`w-4 h-4 ${starredDocuments.includes(document.id) ? 'fill-current' : ''}`} />
                      </button>
                    </div>
                    
                    <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-3">
                      {document.content}
                    </p>
                    
                    {tags[document.id] && (
                      <div className="flex flex-wrap gap-1 mb-3">
                        {tags[document.id].slice(0, 2).map((tag, i) => (
                          <span key={i} className="px-2 py-0.5 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full text-xs">
                            {tag}
                          </span>
                        ))}
                        {tags[document.id].length > 2 && (
                          <span className="px-2 py-0.5 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full text-xs">
                            +{tags[document.id].length - 2}
                          </span>
                        )}
                      </div>
                    )}
                    
                    <div className="flex justify-between items-center">
                      {document.status === 'generated' ? (
                        <span className="flex items-center gap-1 text-green-600 dark:text-green-400 text-xs">
                          <CheckCircle className="w-3 h-3" />
                          Ready
                        </span>
                      ) : (
                        <span className="flex items-center gap-1 text-yellow-600 dark:text-yellow-400 text-xs">
                          <Loader2 className="w-3 h-3 animate-spin" />
                          Processing
                        </span>
                      )}
                      
                      <div className="flex gap-1" onClick={e => e.stopPropagation()}>
                        {document.downloadUrl && (
                          <a
                            href={document.downloadUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                          >
                            <Download className="w-4 h-4" />
                          </a>
                        )}
                        {document.previewUrl && (
                          <a
                            href={document.previewUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                          >
                            <Eye className="w-4 h-4" />
                          </a>
                        )}
                        <div className="relative group">
                          <button className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
                            <MoreHorizontal className="w-4 h-4" />
                          </button>
                          <div className="absolute right-0 mt-1 w-36 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1 hidden group-hover:block z-10">
                            <button 
                              className="w-full text-left px-3 py-1 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                              onClick={(e) => {
                                e.stopPropagation();
                                // Edit functionality
                              }}
                            >
                              <Edit className="w-4 h-4 inline mr-2" />
                              Edit
                            </button>
                            <button 
                              className="w-full text-left px-3 py-1 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                              onClick={(e) => {
                                e.stopPropagation();
                                // Share functionality
                              }}
                            >
                              <Share2 className="w-4 h-4 inline mr-2" />
                              Share
                            </button>
                            <button 
                              className="w-full text-left px-3 py-1 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteDocument(document.id);
                              }}
                            >
                              <Trash2 className="w-4 h-4 inline mr-2" />
                              Delete
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-3">
              {filteredDocuments.map((document) => (
                <div
                  key={document.id}
                  className="bg-white dark:bg-gray-700 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer border border-gray-200 dark:border-gray-600 p-4"
                  onClick={() => handleViewDocument(document)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getDocumentTypeIcon(document.type)}
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white">{document.title}</h4>
                        <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                          <span className="capitalize">{document.type}</span>
                          <span>•</span>
                          <span>{new Date(document.createdAt).toLocaleDateString()}</span>
                          {tags[document.id] && (
                            <>
                              <span>•</span>
                              <div className="flex items-center gap-1">
                                <Tag className="w-3 h-3" />
                                <span>{tags[document.id].length} tags</span>
                              </div>
                            </>
                          )}
                          {sharedWith[document.id] && (
                            <>
                              <span>•</span>
                              <div className="flex items-center gap-1">
                                <Users className="w-3 h-3" />
                                <span>Shared with {sharedWith[document.id].length}</span>
                              </div>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2" onClick={e => e.stopPropagation()}>
                      <button 
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleStar(document.id);
                        }}
                        className={`text-gray-400 hover:text-yellow-500 ${
                          starredDocuments.includes(document.id) ? 'text-yellow-500' : ''
                        }`}
                      >
                        <Star className={`w-4 h-4 ${starredDocuments.includes(document.id) ? 'fill-current' : ''}`} />
                      </button>
                      
                      {document.status === 'generated' ? (
                        <span className="flex items-center gap-1 text-green-600 dark:text-green-400 text-xs">
                          <CheckCircle className="w-3 h-3" />
                          Ready
                        </span>
                      ) : (
                        <span className="flex items-center gap-1 text-yellow-600 dark:text-yellow-400 text-xs">
                          <Loader2 className="w-3 h-3 animate-spin" />
                          Processing
                        </span>
                      )}
                      
                      {document.downloadUrl && (
                        <a
                          href={document.downloadUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                        >
                          <Download className="w-4 h-4" />
                        </a>
                      )}
                      
                      {document.previewUrl && (
                        <a
                          href={document.previewUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                        >
                          <Eye className="w-4 h-4" />
                        </a>
                      )}
                      
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteDocument(document.id);
                        }}
                        className="p-1 text-red-500 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* SkyworkAI Features */}
        <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg p-6 text-white mb-8">
          <div className="flex flex-col md:flex-row items-center justify-between gap-6">
            <div>
              <h3 className="text-xl font-bold mb-2">SkyworkAI Document Generation</h3>
              <p className="text-indigo-100 mb-4 max-w-xl">
                Create professional-quality documents, presentations, spreadsheets, and podcasts with AI assistance. Perfect for business plans, pitch decks, financial models, and more.
              </p>
              <div className="flex flex-wrap gap-2">
                <div className="px-3 py-1 bg-white/20 rounded-full text-sm backdrop-blur-sm">Business Plans</div>
                <div className="px-3 py-1 bg-white/20 rounded-full text-sm backdrop-blur-sm">Pitch Decks</div>
                <div className="px-3 py-1 bg-white/20 rounded-full text-sm backdrop-blur-sm">Financial Models</div>
                <div className="px-3 py-1 bg-white/20 rounded-full text-sm backdrop-blur-sm">Marketing Strategies</div>
              </div>
            </div>
            <button 
              onClick={() => setShowGenerateModal(true)}
              className="px-6 py-3 bg-white text-indigo-600 rounded-lg font-medium hover:bg-gray-100 transition-colors flex items-center gap-2"
            >
              <Plus className="w-5 h-5" />
              <span>Generate New Document</span>
            </button>
          </div>
        </div>

        {/* Recent Templates */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Popular Templates</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[
              {
                title: 'Business Plan',
                description: 'Comprehensive business plan with executive summary, market analysis, and financial projections',
                type: 'document',
                icon: FileText,
                color: 'bg-blue-500'
              },
              {
                title: 'Investor Pitch Deck',
                description: 'Professional pitch deck for investor presentations with key metrics and growth projections',
                type: 'slide',
                icon: Presentation,
                color: 'bg-orange-500'
              },
              {
                title: 'Financial Model',
                description: 'Detailed 3-year financial model with revenue projections, expenses, and cash flow analysis',
                type: 'sheet',
                icon: Table,
                color: 'bg-green-500'
              }
            ].map((template, index) => {
              const TemplateIcon = template.icon;
              return (
                <div
                  key={index}
                  className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => {
                    setGenerateForm({
                      ...generateForm,
                      type: template.type as any,
                      title: template.title
                    });
                    setShowGenerateModal(true);
                  }}
                >
                  <div className="flex items-center gap-3 mb-3">
                    <div className={`w-10 h-10 ${template.color} rounded-lg flex items-center justify-center text-white`}>
                      <TemplateIcon className="w-5 h-5" />
                    </div>
                    <h4 className="font-medium text-gray-900 dark:text-white">{template.title}</h4>
                  </div>
                  
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                    {template.description}
                  </p>
                  
                  <button className="text-sm text-indigo-600 dark:text-indigo-400 hover:underline flex items-center gap-1">
                    <span>Use template</span>
                    <ArrowLeft className="w-3 h-3 rotate-180" />
                  </button>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Generate Document Modal */}
      {showGenerateModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4 overflow-y-auto">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-lg w-full">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">Generate New Content</h3>
                <button 
                  onClick={() => setShowGenerateModal(false)}
                  className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Content Type
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    {[
                      { value: 'document', label: 'Document', icon: FileText },
                      { value: 'slide', label: 'Presentation', icon: Presentation },
                      { value: 'sheet', label: 'Spreadsheet', icon: Table },
                      { value: 'podcast', label: 'Podcast', icon: Mic }
                    ].map((type) => (
                      <button
                        key={type.value}
                        type="button"
                        onClick={() => setGenerateForm(prev => ({ ...prev, type: type.value as any }))}
                        className={`p-3 rounded-lg text-center transition-colors ${
                          generateForm.type === type.value
                            ? 'bg-indigo-100 dark:bg-indigo-900/30 border-2 border-indigo-500'
                            : 'bg-gray-50 dark:bg-gray-700 border-2 border-transparent hover:bg-gray-100 dark:hover:bg-gray-600'
                        }`}
                      >
                        <type.icon className={`w-6 h-6 mx-auto mb-1 ${
                          generateForm.type === type.value
                            ? 'text-indigo-600 dark:text-indigo-400'
                            : 'text-gray-500 dark:text-gray-400'
                        }`} />
                        <span className={`text-sm ${
                          generateForm.type === type.value
                            ? 'font-medium text-indigo-700 dark:text-indigo-300'
                            : 'text-gray-700 dark:text-gray-300'
                        }`}>
                          {type.label}
                        </span>
                      </button>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Title
                  </label>
                  <input
                    type="text"
                    value={generateForm.title}
                    onChange={(e) => setGenerateForm(prev => ({ ...prev, title: e.target.value }))}
                    placeholder={`Enter ${getDocumentTypeName(generateForm.type).toLowerCase()} title`}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Prompt
                  </label>
                  <textarea
                    value={generateForm.prompt}
                    onChange={(e) => setGenerateForm(prev => ({ ...prev, prompt: e.target.value }))}
                    placeholder={`Describe what you want in your ${getDocumentTypeName(generateForm.type).toLowerCase()}`}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    rows={4}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Template
                  </label>
                  <select
                    value={generateForm.template}
                    onChange={(e) => setGenerateForm(prev => ({ ...prev, template: e.target.value }))}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  >
                    <option value="standard">Standard</option>
                    <option value="professional">Professional</option>
                    <option value="creative">Creative</option>
                    <option value="minimal">Minimal</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Additional Context (Optional)
                  </label>
                  <textarea
                    value={generateForm.additionalContext}
                    onChange={(e) => setGenerateForm(prev => ({ ...prev, additionalContext: e.target.value }))}
                    placeholder="Add any additional details or context"
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    rows={2}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Tags (Optional)
                  </label>
                  <div className="flex flex-wrap gap-2 p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 min-h-[42px]">
                    <input
                      type="text"
                      placeholder="Add tags..."
                      className="flex-1 min-w-[100px] bg-transparent text-gray-900 dark:text-white focus:outline-none"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                          // Add tag functionality
                          e.currentTarget.value = '';
                        }
                      }}
                    />
                  </div>
                </div>
              </div>

              <div className="mt-6 flex gap-3">
                <button
                  onClick={handleGenerateDocument}
                  disabled={isLoading || !generateForm.title || !generateForm.prompt}
                  className="flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-500 text-white rounded-lg font-medium transition-colors disabled:cursor-not-allowed"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-5 h-5 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Plus className="w-5 h-5" />
                      Generate {getDocumentTypeName(generateForm.type)}
                    </>
                  )}
                </button>
                <button
                  onClick={() => setShowGenerateModal(false)}
                  className="px-4 py-3 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Document Details Modal */}
      {showDocumentModal && selectedDocument && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4 overflow-y-auto">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-3xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  {getDocumentTypeIcon(selectedDocument.type)}
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white">{selectedDocument.title}</h3>
                    <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                      <span className="capitalize">{selectedDocument.type}</span>
                      <span>•</span>
                      <span>{new Date(selectedDocument.createdAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <button 
                    onClick={() => toggleStar(selectedDocument.id)}
                    className={`p-1 text-gray-500 hover:text-yellow-500 ${
                      starredDocuments.includes(selectedDocument.id) ? 'text-yellow-500' : ''
                    }`}
                  >
                    <Star className={`w-5 h-5 ${starredDocuments.includes(selectedDocument.id) ? 'fill-current' : ''}`} />
                  </button>
                  <button 
                    onClick={() => setShowDocumentModal(false)}
                    className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Content Preview</h4>
                <p className="text-gray-600 dark:text-gray-400 text-sm whitespace-pre-line">
                  {selectedDocument.content}
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Type</p>
                  <p className="font-medium text-gray-900 dark:text-white capitalize">{selectedDocument.type}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Created</p>
                  <p className="font-medium text-gray-900 dark:text-white">
                    {new Date(selectedDocument.createdAt).toLocaleString()}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Status</p>
                  <div className="flex items-center gap-2">
                    {selectedDocument.status === 'generated' ? (
                      <>
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        <span className="font-medium text-gray-900 dark:text-white capitalize">
                          Ready
                        </span>
                      </>
                    ) : selectedDocument.status === 'error' ? (
                      <>
                        <AlertCircle className="w-4 h-4 text-red-500" />
                        <span className="font-medium text-gray-900 dark:text-white capitalize">
                          Error
                        </span>
                      </>
                    ) : (
                      <>
                        <Loader2 className="w-4 h-4 text-indigo-500 animate-spin" />
                        <span className="font-medium text-gray-900 dark:text-white capitalize">
                          Processing
                        </span>
                      </>
                    )}
                  </div>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Last Updated</p>
                  <p className="font-medium text-gray-900 dark:text-white">
                    {new Date(selectedDocument.updatedAt).toLocaleString()}
                  </p>
                </div>
              </div>

              {/* Tags Section */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Tags</p>
                  <button className="text-xs text-indigo-600 dark:text-indigo-400 hover:underline">
                    Edit Tags
                  </button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {tags[selectedDocument.id] ? (
                    tags[selectedDocument.id].map((tag, index) => (
                      <span 
                        key={index}
                        className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-xs flex items-center gap-1"
                      >
                        {tag}
                        <button className="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
                          <X className="w-3 h-3" />
                        </button>
                      </span>
                    ))
                  ) : (
                    <span className="text-sm text-gray-500 dark:text-gray-400">No tags</span>
                  )}
                </div>
              </div>

              {/* Sharing Section */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Shared With</p>
                  <button className="text-xs text-indigo-600 dark:text-indigo-400 hover:underline">
                    Manage Sharing
                  </button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {sharedWith[selectedDocument.id] ? (
                    sharedWith[selectedDocument.id].map((group, index) => (
                      <span 
                        key={index}
                        className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-xs"
                      >
                        {group}
                      </span>
                    ))
                  ) : (
                    <span className="text-sm text-gray-500 dark:text-gray-400">Not shared</span>
                  )}
                </div>
              </div>

              {/* Public Link */}
              <div className="mb-6 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
                    <Link className="w-4 h-4" />
                    Public Link
                  </p>
                  <div className="flex items-center gap-2">
                    <button className="text-xs text-indigo-600 dark:text-indigo-400 hover:underline">
                      Generate Link
                    </button>
                    <div className="relative inline-block w-8 h-4">
                      <input type="checkbox" className="opacity-0 w-0 h-0" />
                      <span className="absolute cursor-pointer top-0 left-0 right-0 bottom-0 bg-gray-300 dark:bg-gray-600 rounded-full transition-colors"></span>
                    </div>
                  </div>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Create a public link to share this document with anyone
                </p>
              </div>

              <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-2">
                  {selectedDocument.downloadUrl && (
                    <a
                      href={selectedDocument.downloadUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-2 px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
                    >
                      <Download className="w-4 h-4" />
                      <span>Download</span>
                    </a>
                  )}
                  {selectedDocument.previewUrl && (
                    <a
                      href={selectedDocument.previewUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                    >
                      <ExternalLink className="w-4 h-4" />
                      <span>Preview</span>
                    </a>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <button className="p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
                    <Share2 className="w-4 h-4" />
                  </button>
                  <button className="p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
                    <Edit className="w-4 h-4" />
                  </button>
                  <button 
                    onClick={() => {
                      handleDeleteDocument(selectedDocument.id);
                      setShowDocumentModal(false);
                    }}
                    className="p-2 text-red-500 hover:text-red-700 transition-colors"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};