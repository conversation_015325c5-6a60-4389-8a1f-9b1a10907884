import React from 'react';
import { Link } from 'react-router-dom';
import { 
  Zap, 
  TrendingUp, 
  Target, 
  Users, 
  CheckCircle, 
  ArrowRight,
  Megaphone,
  BarChart3,
  Globe,
  Rocket,
  DollarSign,
  MessageSquare
} from 'lucide-react';

export const GoToMarket: React.FC = () => {
  const strategies = [
    {
      icon: Target,
      title: 'Market Positioning',
      description: 'Define your unique value proposition and competitive positioning'
    },
    {
      icon: Users,
      title: 'Customer Segmentation',
      description: 'Identify and target your ideal customer segments'
    },
    {
      icon: Megaphone,
      title: 'Marketing Channels',
      description: 'Choose the right channels to reach your target audience'
    },
    {
      icon: DollarSign,
      title: 'Pricing Strategy',
      description: 'Optimize pricing for market penetration and profitability'
    }
  ];

  const phases = [
    {
      phase: 'Phase 1',
      title: 'Market Analysis',
      description: 'Research your market, competitors, and identify opportunities',
      icon: BarChart3,
      color: 'from-purple-500 to-indigo-600'
    },
    {
      phase: 'Phase 2',
      title: 'Strategy Development',
      description: 'Create your go-to-market strategy and messaging framework',
      icon: Target,
      color: 'from-blue-500 to-cyan-600'
    },
    {
      phase: 'Phase 3',
      title: 'Channel Selection',
      description: 'Choose and optimize your marketing and sales channels',
      icon: Globe,
      color: 'from-green-500 to-emerald-600'
    },
    {
      phase: 'Phase 4',
      title: 'Launch & Scale',
      description: 'Execute your strategy and scale based on performance data',
      icon: Rocket,
      color: 'from-orange-500 to-red-600'
    }
  ];

  const outcomes = [
    'Faster time to market with proven strategies',
    'Higher conversion rates through targeted messaging',
    'Reduced customer acquisition costs',
    'Improved product-market fit validation',
    'Scalable growth frameworks',
    'Data-driven optimization processes',
    'Competitive advantage in your market',
    'Clear metrics and success indicators'
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <span className="font-bold text-gray-900 dark:text-white">AI Co-founder</span>
              </Link>
              <span className="mx-4 text-gray-300 dark:text-gray-600">|</span>
              <span className="text-gray-600 dark:text-gray-400">Go-to-Market Strategy</span>
            </div>
            <Link 
              to="/dashboard" 
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
            >
              Get Started
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              Go-to-Market Strategy That Converts
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 mb-8 max-w-3xl mx-auto">
              Partner with our AI Marketing Co-founder to develop winning go-to-market strategies 
              that drive customer acquisition and accelerate growth.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                to="/dashboard" 
                className="px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-xl font-semibold text-lg transition-all hover:shadow-lg flex items-center gap-2 justify-center"
              >
                Launch Your Strategy
                <ArrowRight size={20} />
              </Link>
              <Link 
                to="/ai-cofounders" 
                className="px-8 py-4 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-700 hover:border-gray-400 dark:hover:border-gray-600 text-gray-900 dark:text-white rounded-xl font-semibold text-lg transition-all hover:shadow-lg"
              >
                Meet the Team
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Key Strategies Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Core Go-to-Market Strategies
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Our AI helps you develop comprehensive strategies that cover all aspects of bringing your product to market successfully.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {strategies.map((strategy, index) => {
              const IconComponent = strategy.icon;
              return (
                <div key={index} className="bg-gray-50 dark:bg-gray-900 rounded-2xl p-6 hover:shadow-lg transition-shadow">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mb-4">
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                    {strategy.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    {strategy.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Process Phases */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Our 4-Phase GTM Process
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Follow our systematic approach to develop and execute a go-to-market strategy that drives results.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {phases.map((phase, index) => {
              const IconComponent = phase.icon;
              return (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow">
                  <div className="flex items-start gap-6">
                    <div className={`w-16 h-16 bg-gradient-to-br ${phase.color} rounded-2xl flex items-center justify-center flex-shrink-0`}>
                      <IconComponent className="w-8 h-8 text-white" />
                    </div>
                    <div>
                      <div className="text-sm font-semibold text-purple-600 dark:text-purple-400 mb-2">
                        {phase.phase}
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                        {phase.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400">
                        {phase.description}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Outcomes Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
                Expected Outcomes
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-400 mb-8">
                Working with our AI Marketing Co-founder delivers measurable results that accelerate your startup's growth and market success.
              </p>
              <div className="space-y-4">
                {outcomes.map((outcome, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700 dark:text-gray-300">{outcome}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg">
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <TrendingUp className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Ready to Scale Your Growth?
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Start developing your go-to-market strategy with our AI Marketing Co-founder today.
                </p>
                <Link 
                  to="/dashboard" 
                  className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-lg font-semibold transition-all hover:shadow-lg"
                >
                  Start Planning
                  <ArrowRight size={16} />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              What Makes Our GTM Strategy Different
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Our AI-powered approach combines proven frameworks with real-time market insights to create strategies that actually work.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <BarChart3 className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                Data-Driven Insights
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Make decisions based on real market data, competitor analysis, and customer behavior patterns.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <MessageSquare className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                Personalized Guidance
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Get tailored strategies that fit your specific industry, target market, and business model.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Rocket className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                Rapid Execution
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Move from strategy to execution quickly with actionable plans and clear next steps.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};
