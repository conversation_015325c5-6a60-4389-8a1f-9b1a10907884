import React from 'react';
import { Link } from 'react-router-dom';
import { 
  Zap, 
  Presentation, 
  DollarSign, 
  Users, 
  CheckCircle, 
  ArrowRight,
  FileText,
  BarChart3,
  Target,
  Crown,
  TrendingUp,
  Lightbulb
} from 'lucide-react';

export const Fundraising: React.FC = () => {
  const services = [
    {
      icon: Presentation,
      title: 'Pitch Deck Creation',
      description: 'Craft compelling pitch decks that tell your story and capture investor attention'
    },
    {
      icon: FileText,
      title: 'Financial Modeling',
      description: 'Build robust financial models and projections that investors trust'
    },
    {
      icon: Target,
      title: 'Investor Targeting',
      description: 'Identify and approach the right investors for your stage and industry'
    },
    {
      icon: Users,
      title: 'Pitch Practice',
      description: 'Perfect your pitch delivery with AI-powered feedback and coaching'
    }
  ];

  const pitchElements = [
    {
      slide: '01',
      title: 'Problem & Solution',
      description: 'Clearly articulate the problem you solve and your unique solution',
      icon: Lightbulb
    },
    {
      slide: '02',
      title: 'Market Opportunity',
      description: 'Demonstrate the size and potential of your target market',
      icon: BarChart3
    },
    {
      slide: '03',
      title: 'Business Model',
      description: 'Show how you make money and your path to profitability',
      icon: DollarSign
    },
    {
      slide: '04',
      title: 'Traction & Growth',
      description: 'Present your achievements and growth trajectory',
      icon: TrendingUp
    }
  ];

  const fundingStages = [
    {
      stage: 'Pre-Seed',
      amount: '$50K - $500K',
      focus: 'Validate idea and build MVP',
      investors: 'Angels, Friends & Family'
    },
    {
      stage: 'Seed',
      amount: '$500K - $2M',
      focus: 'Product-market fit and early traction',
      investors: 'Seed VCs, Angel Groups'
    },
    {
      stage: 'Series A',
      amount: '$2M - $15M',
      focus: 'Scale proven business model',
      investors: 'VCs, Growth Funds'
    },
    {
      stage: 'Series B+',
      amount: '$15M+',
      focus: 'Expand markets and accelerate growth',
      investors: 'Growth VCs, PE Firms'
    }
  ];

  const outcomes = [
    'Higher success rate in fundraising rounds',
    'Stronger investor relationships and networks',
    'Better valuation through compelling storytelling',
    'Reduced time to close funding rounds',
    'Professional-grade pitch materials',
    'Improved financial planning and projections',
    'Strategic guidance on funding timing',
    'Due diligence preparation and support'
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <span className="font-bold text-gray-900 dark:text-white">AI Co-founder</span>
              </Link>
              <span className="mx-4 text-gray-300 dark:text-gray-600">|</span>
              <span className="text-gray-600 dark:text-gray-400">Fundraising & Pitch</span>
            </div>
            <Link 
              to="/dashboard" 
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
            >
              Get Started
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              Fundraising & Pitch Mastery
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 mb-8 max-w-3xl mx-auto">
              Work with our AI Strategic Co-founder to create compelling pitch decks, 
              build investor relationships, and successfully raise funding for your startup.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                to="/dashboard" 
                className="px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-xl font-semibold text-lg transition-all hover:shadow-lg flex items-center gap-2 justify-center"
              >
                Start Your Pitch
                <ArrowRight size={20} />
              </Link>
              <Link 
                to="/ai-cofounders" 
                className="px-8 py-4 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-700 hover:border-gray-400 dark:hover:border-gray-600 text-gray-900 dark:text-white rounded-xl font-semibold text-lg transition-all hover:shadow-lg"
              >
                Meet the Team
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Complete Fundraising Support
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              From pitch deck creation to investor outreach, we provide comprehensive support for every aspect of your fundraising journey.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {services.map((service, index) => {
              const IconComponent = service.icon;
              return (
                <div key={index} className="bg-gray-50 dark:bg-gray-900 rounded-2xl p-6 hover:shadow-lg transition-shadow">
                  <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center mb-4">
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                    {service.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    {service.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Pitch Elements */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Essential Pitch Deck Elements
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Our AI helps you craft each slide of your pitch deck to maximize impact and investor engagement.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {pitchElements.map((element, index) => {
              const IconComponent = element.icon;
              return (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow">
                  <div className="flex items-start gap-6">
                    <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center flex-shrink-0">
                      <IconComponent className="w-8 h-8 text-white" />
                    </div>
                    <div>
                      <div className="text-sm font-semibold text-indigo-600 dark:text-indigo-400 mb-2">
                        Slide {element.slide}
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                        {element.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400">
                        {element.description}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Funding Stages */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Funding Stages & Strategy
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Understand the different funding stages and develop the right strategy for your current phase.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {fundingStages.map((stage, index) => (
              <div key={index} className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <Crown className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                    {stage.stage}
                  </h3>
                  <div className="text-2xl font-bold text-indigo-600 dark:text-indigo-400 mb-3">
                    {stage.amount}
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    <strong>Focus:</strong> {stage.focus}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    <strong>Investors:</strong> {stage.investors}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Outcomes Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
                Fundraising Success Outcomes
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-400 mb-8">
                Our AI-powered fundraising support helps startups achieve better outcomes and build stronger investor relationships.
              </p>
              <div className="space-y-4">
                {outcomes.map((outcome, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700 dark:text-gray-300">{outcome}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-2xl p-8">
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <DollarSign className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Ready to Raise Funding?
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Start building your pitch deck and fundraising strategy with our AI Strategic Co-founder.
                </p>
                <Link 
                  to="/dashboard" 
                  className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-lg font-semibold transition-all hover:shadow-lg"
                >
                  Start Fundraising
                  <ArrowRight size={16} />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};
