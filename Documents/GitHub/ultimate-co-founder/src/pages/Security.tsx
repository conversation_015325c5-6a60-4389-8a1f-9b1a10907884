import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  Shield, 
  Lock, 
  Key, 
  Eye, 
  Server, 
  FileCheck, 
  AlertTriangle, 
  CheckCircle,
  Globe,
  Database,
  Users,
  Mail
} from 'lucide-react';

export const Security: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
      {/* Header */}
      <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-600 rounded-xl flex items-center justify-center">
                <Shield className="w-6 h-6 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-gray-900 dark:text-white">Security</h1>
            </div>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Enterprise-grade security measures protecting your business data and intellectual property.
            </p>
            <div className="mt-4 text-sm text-gray-500 dark:text-gray-400">
              Last updated: January 1, 2025
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        
        {/* Security Overview */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
            <Shield className="w-6 h-6 text-blue-600" />
            Security Overview
          </h2>
          
          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">SOC 2 Type II</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">Independently audited security controls and compliance</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <Lock className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">End-to-End Encryption</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">AES-256 encryption for data at rest and in transit</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <Eye className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">24/7 Monitoring</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">Continuous security monitoring and threat detection</p>
            </div>
          </div>
        </div>

        {/* Data Protection */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
            <Database className="w-6 h-6 text-green-600" />
            Data Protection
          </h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Encryption Standards</h3>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Data at Rest</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">AES-256 encryption for all stored data including databases, files, and backups</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Data in Transit</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">TLS 1.3 encryption for all data transmission between clients and servers</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Key Management</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Hardware Security Modules (HSM) for encryption key generation and storage</p>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Data Handling</h3>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <CheckCircle className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Data Minimization</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">We only collect and store data necessary for service delivery</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <CheckCircle className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Data Retention</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Automated data deletion based on retention policies and user preferences</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <CheckCircle className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Data Portability</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Export your data in standard formats anytime</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Access Control */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
            <Key className="w-6 h-6 text-purple-600" />
            Access Control & Authentication
          </h2>
          
          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-6">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mb-4">
                <Users className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-purple-900 dark:text-purple-200 mb-2">Multi-Factor Authentication</h3>
              <ul className="text-sm text-purple-800 dark:text-purple-300 space-y-1">
                <li>• TOTP authenticator apps</li>
                <li>• SMS verification</li>
                <li>• Hardware security keys</li>
                <li>• Biometric authentication</li>
              </ul>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4">
                <Shield className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-blue-900 dark:text-blue-200 mb-2">Role-Based Access Control</h3>
              <ul className="text-sm text-blue-800 dark:text-blue-300 space-y-1">
                <li>• Granular permission system</li>
                <li>• Team member roles</li>
                <li>• Resource-level access</li>
                <li>• Audit trail logging</li>
              </ul>
            </div>

            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-6">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mb-4">
                <Lock className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-green-900 dark:text-green-200 mb-2">Session Management</h3>
              <ul className="text-sm text-green-800 dark:text-green-300 space-y-1">
                <li>• Automatic session timeout</li>
                <li>• Concurrent session limits</li>
                <li>• Device management</li>
                <li>• Suspicious activity detection</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Infrastructure Security */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
            <Server className="w-6 h-6 text-orange-600" />
            Infrastructure Security
          </h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Cloud Security</h3>
              <div className="space-y-4">
                <div className="border-l-4 border-orange-500 pl-4">
                  <h4 className="font-medium text-gray-900 dark:text-white">AWS Infrastructure</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Hosted on AWS with SOC 2, ISO 27001, and FedRAMP compliance</p>
                </div>

                <div className="border-l-4 border-blue-500 pl-4">
                  <h4 className="font-medium text-gray-900 dark:text-white">Network Security</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">VPC isolation, WAF protection, and DDoS mitigation</p>
                </div>

                <div className="border-l-4 border-green-500 pl-4">
                  <h4 className="font-medium text-gray-900 dark:text-white">Container Security</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Kubernetes with security policies and image scanning</p>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Monitoring & Response</h3>
              <div className="space-y-4">
                <div className="border-l-4 border-red-500 pl-4">
                  <h4 className="font-medium text-gray-900 dark:text-white">24/7 Monitoring</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Real-time security monitoring and alerting systems</p>
                </div>

                <div className="border-l-4 border-purple-500 pl-4">
                  <h4 className="font-medium text-gray-900 dark:text-white">Incident Response</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Dedicated security team with 15-minute response time</p>
                </div>

                <div className="border-l-4 border-indigo-500 pl-4">
                  <h4 className="font-medium text-gray-900 dark:text-white">Threat Intelligence</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">AI-powered threat detection and prevention</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Compliance */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
            <FileCheck className="w-6 h-6 text-indigo-600" />
            Compliance & Certifications
          </h2>
          
          <div className="grid md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center mx-auto mb-3">
                <FileCheck className="w-8 h-8 text-indigo-600" />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white text-sm">SOC 2 Type II</h3>
              <p className="text-xs text-gray-600 dark:text-gray-400">Security & Availability</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-3">
                <Globe className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white text-sm">GDPR</h3>
              <p className="text-xs text-gray-600 dark:text-gray-400">EU Data Protection</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-3">
                <Shield className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white text-sm">CCPA</h3>
              <p className="text-xs text-gray-600 dark:text-gray-400">California Privacy</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-3">
                <Lock className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white text-sm">ISO 27001</h3>
              <p className="text-xs text-gray-600 dark:text-gray-400">Information Security</p>
            </div>
          </div>
        </div>

        {/* Security Best Practices */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
            <AlertTriangle className="w-6 h-6 text-yellow-600" />
            Security Best Practices for Users
          </h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Account Security</h3>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white text-sm">Use Strong Passwords</h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400">Minimum 12 characters with mixed case, numbers, and symbols</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white text-sm">Enable 2FA</h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400">Use authenticator apps or hardware keys for additional security</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white text-sm">Regular Password Updates</h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400">Change passwords every 90 days or immediately if compromised</p>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Data Protection</h3>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white text-sm">Secure Networks</h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400">Avoid public Wi-Fi for sensitive business activities</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <CheckCircle className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white text-sm">Device Security</h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400">Keep devices updated and use endpoint protection</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <CheckCircle className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white text-sm">Regular Backups</h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400">Export and backup important business data regularly</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Security Contact */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
            <Mail className="w-6 h-6 text-red-600" />
            Security Contact
          </h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-6">
              <h3 className="font-semibold text-red-900 dark:text-red-200 mb-4">Report Security Issues</h3>
              <p className="text-red-800 dark:text-red-300 text-sm mb-4">
                If you discover a security vulnerability, please report it immediately:
              </p>
              <div className="space-y-2">
                <p className="text-red-900 dark:text-red-200 text-sm">
                  <strong>Email:</strong> <a href="mailto:<EMAIL>" className="underline"><EMAIL></a>
                </p>
                <p className="text-red-900 dark:text-red-200 text-sm">
                  <strong>Response Time:</strong> Within 24 hours
                </p>
                <p className="text-red-900 dark:text-red-200 text-sm">
                  <strong>Bug Bounty:</strong> Rewards for valid security reports
                </p>
              </div>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
              <h3 className="font-semibold text-blue-900 dark:text-blue-200 mb-4">Security Questions</h3>
              <p className="text-blue-800 dark:text-blue-300 text-sm mb-4">
                For general security questions or compliance inquiries:
              </p>
              <div className="space-y-2">
                <p className="text-blue-900 dark:text-blue-200 text-sm">
                  <strong>Email:</strong> <a href="mailto:<EMAIL>" className="underline"><EMAIL></a>
                </p>
                <p className="text-blue-900 dark:text-blue-200 text-sm">
                  <strong>Documentation:</strong> Available upon request
                </p>
                <p className="text-blue-900 dark:text-blue-200 text-sm">
                  <strong>Audits:</strong> Annual third-party security audits
                </p>
              </div>
            </div>
          </div>

          {/* Back to Home */}
          <div className="pt-8 border-t border-gray-200 dark:border-gray-700 mt-8">
            <Link 
              to="/" 
              className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-red-600 to-orange-600 text-white rounded-lg hover:from-red-700 hover:to-orange-700 transition-all duration-200 font-medium"
            >
              ← Back to Home
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};
