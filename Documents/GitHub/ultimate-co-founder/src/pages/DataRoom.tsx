import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { FolderOpen, FileText, Download, Upload, Search, Filter, Plus, MoreHorizontal, Trash2, Edit, Share2, Eye, CheckCircle, AlertCircle, Zap, ArrowLeft, File, FileImage, FileSpreadsheet, File as FilePdf, FileCode, Lock, Users, X, ChevronDown, ChevronUp, Calendar, Clock, Info, ExternalLink, Shield, RefreshCw, Loader2 } from 'lucide-react';

interface DataRoomFile {
  id: string;
  name: string;
  type: 'pdf' | 'xlsx' | 'docx' | 'pptx' | 'jpg' | 'png' | 'txt' | 'md';
  size: number;
  folder: string;
  lastModified: string;
  status: 'present' | 'missing' | 'outdated';
  sharedWith: string[];
  createdBy?: string;
  version?: string;
  description?: string;
}

interface DataRoomFolder {
  id: string;
  name: string;
  description: string;
  fileCount: number;
  lastModified: string;
  icon?: React.ElementType;
  color?: string;
}

export const DataRoom: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<DataRoomFile | null>(null);
  const [showFileDetails, setShowFileDetails] = useState(false);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showNewFolderModal, setShowNewFolderModal] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const [newFolderDescription, setNewFolderDescription] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'size'>('date');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [filterStatus, setFilterStatus] = useState<'all' | 'present' | 'missing' | 'outdated'>('all');
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');

  // Mock data
  const [folders, setFolders] = useState<DataRoomFolder[]>([
    {
      id: 'executive-summary',
      name: 'Executive Summary',
      description: 'High-level overview of the business',
      fileCount: 3,
      lastModified: '2025-06-28',
      icon: FileText,
      color: 'bg-blue-500'
    },
    {
      id: 'financial-projections',
      name: 'Financial Projections',
      description: 'Financial models and forecasts',
      fileCount: 5,
      lastModified: '2025-06-27',
      icon: FileSpreadsheet,
      color: 'bg-green-500'
    },
    {
      id: 'legal-documents',
      name: 'Legal Documents',
      description: 'Contracts, agreements, and legal paperwork',
      fileCount: 7,
      lastModified: '2025-06-25',
      icon: FilePdf,
      color: 'bg-red-500'
    },
    {
      id: 'technical-documentation',
      name: 'Technical Documentation',
      description: 'Architecture, code, and technical specs',
      fileCount: 8,
      lastModified: '2025-06-29',
      icon: FileCode,
      color: 'bg-purple-500'
    },
    {
      id: 'market-research',
      name: 'Market Research',
      description: 'Market analysis and competitive research',
      fileCount: 4,
      lastModified: '2025-06-26',
      icon: FileText,
      color: 'bg-orange-500'
    },
    {
      id: 'team-information',
      name: 'Team Information',
      description: 'Team structure, bios, and roles',
      fileCount: 2,
      lastModified: '2025-06-24',
      icon: Users,
      color: 'bg-indigo-500'
    }
  ]);

  const [files, setFiles] = useState<DataRoomFile[]>([
    {
      id: 'exec-summary',
      name: 'Executive Summary.pdf',
      type: 'pdf',
      size: 2500000,
      folder: 'executive-summary',
      lastModified: '2025-06-28T14:30:00Z',
      status: 'present',
      sharedWith: ['Investors', 'Team'],
      createdBy: 'Strategic Co-founder',
      version: '1.2',
      description: 'Comprehensive overview of the business concept, market opportunity, and execution strategy.'
    },
    {
      id: 'pitch-deck',
      name: 'Investor Pitch Deck.pptx',
      type: 'pptx',
      size: 8500000,
      folder: 'executive-summary',
      lastModified: '2025-06-28T10:15:00Z',
      status: 'present',
      sharedWith: ['Investors'],
      createdBy: 'Strategic Co-founder',
      version: '2.1',
      description: 'Presentation deck for investor meetings with key metrics and growth projections.'
    },
    {
      id: 'one-pager',
      name: 'One-Page Overview.pdf',
      type: 'pdf',
      size: 1200000,
      folder: 'executive-summary',
      lastModified: '2025-06-27T16:45:00Z',
      status: 'present',
      sharedWith: ['Investors', 'Team', 'Partners'],
      createdBy: 'Marketing Co-founder',
      version: '1.0',
      description: 'Concise one-page summary of the business for quick reference.'
    },
    {
      id: 'financial-model',
      name: 'Financial Model.xlsx',
      type: 'xlsx',
      size: 4500000,
      folder: 'financial-projections',
      lastModified: '2025-06-27T09:20:00Z',
      status: 'present',
      sharedWith: ['Investors'],
      createdBy: 'Operations Co-founder',
      version: '3.5',
      description: 'Detailed 3-year financial model with revenue projections, expenses, and cash flow analysis.'
    },
    {
      id: 'cash-flow',
      name: 'Cash Flow Projections.xlsx',
      type: 'xlsx',
      size: 3200000,
      folder: 'financial-projections',
      lastModified: '2025-06-26T11:10:00Z',
      status: 'present',
      sharedWith: ['Investors', 'Team'],
      createdBy: 'Operations Co-founder',
      version: '2.2',
      description: 'Monthly cash flow projections for the next 24 months.'
    },
    {
      id: 'cap-table',
      name: 'Cap Table.xlsx',
      type: 'xlsx',
      size: 1800000,
      folder: 'financial-projections',
      lastModified: '2025-06-25T15:30:00Z',
      status: 'present',
      sharedWith: ['Investors'],
      createdBy: 'Operations Co-founder',
      version: '1.4',
      description: 'Capitalization table showing equity distribution and ownership structure.'
    },
    {
      id: 'unit-economics',
      name: 'Unit Economics.xlsx',
      type: 'xlsx',
      size: 2100000,
      folder: 'financial-projections',
      lastModified: '2025-06-24T13:45:00Z',
      status: 'present',
      sharedWith: ['Investors', 'Team'],
      createdBy: 'Operations Co-founder',
      version: '1.1',
      description: 'Analysis of customer acquisition cost, lifetime value, and other key unit economics metrics.'
    },
    {
      id: 'budget',
      name: 'Operating Budget.xlsx',
      type: 'xlsx',
      size: 2800000,
      folder: 'financial-projections',
      lastModified: '2025-06-23T10:20:00Z',
      status: 'present',
      sharedWith: ['Investors', 'Team'],
      createdBy: 'Operations Co-founder',
      version: '2.0',
      description: 'Detailed operating budget for the next 12 months.'
    },
    {
      id: 'incorporation',
      name: 'Certificate of Incorporation.pdf',
      type: 'pdf',
      size: 3500000,
      folder: 'legal-documents',
      lastModified: '2025-06-25T09:15:00Z',
      status: 'present',
      sharedWith: ['Investors', 'Team'],
      createdBy: 'Operations Co-founder',
      version: '1.0',
      description: 'Official certificate of incorporation document.'
    },
    {
      id: 'bylaws',
      name: 'Company Bylaws.pdf',
      type: 'pdf',
      size: 4200000,
      folder: 'legal-documents',
      lastModified: '2025-06-24T14:30:00Z',
      status: 'present',
      sharedWith: ['Investors', 'Team'],
      createdBy: 'Operations Co-founder',
      version: '1.0',
      description: 'Company bylaws outlining governance structure and procedures.'
    },
    {
      id: 'ip-assignment',
      name: 'IP Assignment Agreements.pdf',
      type: 'pdf',
      size: 3800000,
      folder: 'legal-documents',
      lastModified: '2025-06-23T11:45:00Z',
      status: 'present',
      sharedWith: ['Investors'],
      createdBy: 'Operations Co-founder',
      version: '1.0',
      description: 'Intellectual property assignment agreements for all team members and contractors.'
    },
    {
      id: 'testimonials',
      name: 'Customer Testimonials.pdf',
      type: 'pdf',
      size: 0,
      folder: 'market-research',
      lastModified: '2025-06-26T00:00:00Z',
      status: 'missing',
      sharedWith: [],
      description: 'Collection of customer testimonials and case studies.'
    },
    {
      id: 'market-analysis',
      name: 'Market Analysis Report.pdf',
      type: 'pdf',
      size: 5200000,
      folder: 'market-research',
      lastModified: '2025-06-25T16:20:00Z',
      status: 'present',
      sharedWith: ['Investors', 'Team'],
      createdBy: 'Strategic Co-founder',
      version: '2.3',
      description: 'Comprehensive market analysis including TAM/SAM/SOM calculations and competitive landscape.'
    },
    {
      id: 'user-research',
      name: 'User Research Findings.pdf',
      type: 'pdf',
      size: 4100000,
      folder: 'market-research',
      lastModified: '2025-06-24T11:30:00Z',
      status: 'outdated',
      sharedWith: ['Team'],
      createdBy: 'Product Co-founder',
      version: '1.0',
      description: 'Findings from user interviews and surveys. Needs updating with recent data.'
    }
  ]);

  useEffect(() => {
    // Simulate loading data
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);
    
    return () => clearTimeout(timer);
  }, []);

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'pdf':
        return <FilePdf className="w-6 h-6 text-red-500" />;
      case 'xlsx':
        return <FileSpreadsheet className="w-6 h-6 text-green-500" />;
      case 'docx':
        return <FileText className="w-6 h-6 text-blue-500" />;
      case 'pptx':
        return <FileText className="w-6 h-6 text-orange-500" />;
      case 'jpg':
      case 'png':
        return <FileImage className="w-6 h-6 text-purple-500" />;
      case 'txt':
      case 'md':
        return <File className="w-6 h-6 text-gray-500" />;
      default:
        return <File className="w-6 h-6 text-gray-500" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleViewFile = (file: DataRoomFile) => {
    setSelectedFile(file);
    setShowFileDetails(true);
  };

  const handleCreateFolder = () => {
    if (!newFolderName.trim()) return;
    
    const newFolder: DataRoomFolder = {
      id: `folder-${Date.now()}`,
      name: newFolderName,
      description: newFolderDescription,
      fileCount: 0,
      lastModified: new Date().toISOString().split('T')[0],
      icon: FolderOpen,
      color: 'bg-blue-500'
    };
    
    setFolders([...folders, newFolder]);
    setNewFolderName('');
    setNewFolderDescription('');
    setShowNewFolderModal(false);
  };

  const handleUploadFile = () => {
    // Simulate file upload
    setShowUploadModal(false);
    
    // If the missing file is being uploaded, update its status
    if (selectedFile && selectedFile.status === 'missing') {
      const updatedFiles = files.map(file => 
        file.id === selectedFile.id 
          ? { 
              ...file, 
              status: 'present', 
              size: 3200000, 
              lastModified: new Date().toISOString(),
              version: '1.0',
              createdBy: 'You'
            } 
          : file
      );
      setFiles(updatedFiles);
      setSelectedFile(null);
    }
  };

  const handleDeleteFile = (fileId: string) => {
    setFiles(files.filter(file => file.id !== fileId));
    if (selectedFile && selectedFile.id === fileId) {
      setSelectedFile(null);
      setShowFileDetails(false);
    }
  };

  const handleDeleteFolder = (folderId: string) => {
    setFolders(folders.filter(folder => folder.id !== folderId));
    if (selectedFolder === folderId) {
      setSelectedFolder(null);
    }
  };

  const toggleSortDirection = () => {
    setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
  };

  const handleSort = (column: 'name' | 'date' | 'size') => {
    if (sortBy === column) {
      toggleSortDirection();
    } else {
      setSortBy(column);
      setSortDirection('asc');
    }
  };

  // Filter and sort files
  const filteredFiles = files
    .filter(file => 
      (selectedFolder ? file.folder === selectedFolder : true) &&
      (filterStatus === 'all' ? true : file.status === filterStatus) &&
      (searchQuery ? file.name.toLowerCase().includes(searchQuery.toLowerCase()) : true)
    )
    .sort((a, b) => {
      if (sortBy === 'name') {
        return sortDirection === 'asc' 
          ? a.name.localeCompare(b.name)
          : b.name.localeCompare(a.name);
      } else if (sortBy === 'date') {
        return sortDirection === 'asc'
          ? new Date(a.lastModified).getTime() - new Date(b.lastModified).getTime()
          : new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime();
      } else if (sortBy === 'size') {
        return sortDirection === 'asc'
          ? a.size - b.size
          : b.size - a.size;
      }
      return 0;
    });

  // Filter folders based on search
  const filteredFolders = folders.filter(folder => 
    searchQuery ? folder.name.toLowerCase().includes(searchQuery.toLowerCase()) : true
  );

  // Calculate completion metrics
  const totalFiles = files.length;
  const presentFiles = files.filter(f => f.status === 'present').length;
  const missingFiles = files.filter(f => f.status === 'missing').length;
  const outdatedFiles = files.filter(f => f.status === 'outdated').length;
  const completionPercentage = Math.round((presentFiles / totalFiles) * 100);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <span className="font-bold text-gray-900 dark:text-white">Ultimate Co-founder</span>
              </Link>
              <span className="mx-4 text-gray-300 dark:text-gray-600">|</span>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">Data Room</h1>
            </div>
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search files..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 w-64 bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
              <Link to="/" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                <ArrowLeft className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Data Room Overview */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
          <div className="flex items-start justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Startup Data Room</h2>
              <p className="text-gray-600 dark:text-gray-400">
                Comprehensive repository of all critical business documents
              </p>
            </div>
            <div className="flex items-center gap-3">
              <button 
                onClick={() => setShowUploadModal(true)}
                className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
              >
                <Upload className="w-4 h-4" />
                <span>Upload</span>
              </button>
              <button 
                onClick={() => setShowNewFolderModal(true)}
                className="flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
              >
                <Plus className="w-4 h-4" />
                <span>New Folder</span>
              </button>
            </div>
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <Loader2 className="w-12 h-12 text-purple-600 mx-auto mb-4 animate-spin" />
                <p className="text-gray-600 dark:text-gray-400">Loading data room...</p>
              </div>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 flex items-center gap-4">
                  <div className="w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center">
                    <FolderOpen className="w-6 h-6 text-purple-600 dark:text-purple-300" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">{folders.length}</p>
                    <p className="text-sm text-purple-600 dark:text-purple-400">Folders</p>
                  </div>
                </div>
                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 flex items-center gap-4">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
                    <FileText className="w-6 h-6 text-blue-600 dark:text-blue-300" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">{files.length}</p>
                    <p className="text-sm text-blue-600 dark:text-blue-400">Files</p>
                  </div>
                </div>
                <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 flex items-center gap-4">
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-300" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-green-700 dark:text-green-300">
                      {completionPercentage}%
                    </p>
                    <p className="text-sm text-green-600 dark:text-green-400">Completion</p>
                  </div>
                </div>
                <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4 flex items-center gap-4">
                  <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-800 rounded-full flex items-center justify-center">
                    <AlertCircle className="w-6 h-6 text-yellow-600 dark:text-yellow-300" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-yellow-700 dark:text-yellow-300">
                      {missingFiles + outdatedFiles}
                    </p>
                    <p className="text-sm text-yellow-600 dark:text-yellow-400">Attention Needed</p>
                  </div>
                </div>
              </div>

              {/* Progress Bar */}
              <div className="mb-8">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-gray-900 dark:text-white">Data Room Completion</h3>
                  <div className="flex items-center gap-2 text-sm">
                    <span className="text-gray-600 dark:text-gray-400">{presentFiles} of {totalFiles} files</span>
                  </div>
                </div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-gradient-to-r from-green-500 to-green-600 rounded-full"
                    style={{ width: `${completionPercentage}%` }}
                  ></div>
                </div>
                <div className="flex items-center justify-between mt-2 text-xs text-gray-500 dark:text-gray-400">
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Present: {presentFiles}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span>Missing: {missingFiles}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <span>Outdated: {outdatedFiles}</span>
                  </div>
                </div>
              </div>

              {/* View Controls */}
              <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 rounded-lg transition-colors ${
                      viewMode === 'list'
                        ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                    }`}
                  >
                    <FileText className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 rounded-lg transition-colors ${
                      viewMode === 'grid'
                        ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                    }`}
                  >
                    <div className="w-5 h-5 grid grid-cols-2 gap-0.5">
                      <div className="bg-current rounded-sm"></div>
                      <div className="bg-current rounded-sm"></div>
                      <div className="bg-current rounded-sm"></div>
                      <div className="bg-current rounded-sm"></div>
                    </div>
                  </button>
                  
                  <div className="h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1"></div>
                  
                  <select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value as any)}
                    className="p-2 bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    <option value="all">All Files</option>
                    <option value="present">Present</option>
                    <option value="missing">Missing</option>
                    <option value="outdated">Outdated</option>
                  </select>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <span>Sort by:</span>
                    <button
                      onClick={() => handleSort('name')}
                      className={`flex items-center gap-1 px-2 py-1 rounded ${
                        sortBy === 'name' ? 'bg-gray-200 dark:bg-gray-700 font-medium' : ''
                      }`}
                    >
                      <span>Name</span>
                      {sortBy === 'name' && (
                        sortDirection === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                      )}
                    </button>
                    <button
                      onClick={() => handleSort('date')}
                      className={`flex items-center gap-1 px-2 py-1 rounded ${
                        sortBy === 'date' ? 'bg-gray-200 dark:bg-gray-700 font-medium' : ''
                      }`}
                    >
                      <span>Date</span>
                      {sortBy === 'date' && (
                        sortDirection === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                      )}
                    </button>
                    <button
                      onClick={() => handleSort('size')}
                      className={`flex items-center gap-1 px-2 py-1 rounded ${
                        sortBy === 'size' ? 'bg-gray-200 dark:bg-gray-700 font-medium' : ''
                      }`}
                    >
                      <span>Size</span>
                      {sortBy === 'size' && (
                        sortDirection === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                      )}
                    </button>
                  </div>
                  
                  <button className="p-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors">
                    <RefreshCw className="w-5 h-5" />
                  </button>
                </div>
              </div>

              {/* Folder Navigation */}
              {!selectedFolder && (
                <div className="mb-8">
                  <h3 className="font-bold text-gray-900 dark:text-white mb-4">Folders</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {filteredFolders.map((folder) => {
                      const FolderIcon = folder.icon || FolderOpen;
                      return (
                        <button
                          key={folder.id}
                          onClick={() => setSelectedFolder(folder.id)}
                          className="p-4 rounded-lg text-left transition-colors bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 border border-gray-200 dark:border-gray-600 shadow-sm hover:shadow-md"
                        >
                          <div className="flex items-start gap-3">
                            <div className={`w-10 h-10 ${folder.color || 'bg-blue-500'} rounded-lg flex items-center justify-center text-white`}>
                              <FolderIcon className="w-5 h-5" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <h4 className="font-medium text-gray-900 dark:text-white mb-1 truncate">{folder.name}</h4>
                              <p className="text-xs text-gray-600 dark:text-gray-400 mb-2 line-clamp-2">{folder.description}</p>
                              <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                                <span>{folder.fileCount} files</span>
                                <span>Updated {new Date(folder.lastModified).toLocaleDateString()}</span>
                              </div>
                            </div>
                          </div>
                        </button>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Files Section */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-bold text-gray-900 dark:text-white">
                    {selectedFolder 
                      ? `Files in ${folders.find(f => f.id === selectedFolder)?.name}` 
                      : 'All Files'}
                  </h3>
                  <div className="flex items-center gap-3">
                    {selectedFolder && (
                      <button 
                        onClick={() => setSelectedFolder(null)}
                        className="text-sm text-purple-600 dark:text-purple-400 hover:underline flex items-center gap-1"
                      >
                        <ArrowLeft className="w-4 h-4" />
                        Back to All
                      </button>
                    )}
                  </div>
                </div>

                {filteredFiles.length === 0 ? (
                  <div className="bg-white dark:bg-gray-700 rounded-lg p-8 text-center">
                    <FolderOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">No files found</h3>
                    <p className="text-gray-600 dark:text-gray-400 mb-6">
                      {searchQuery 
                        ? 'Try adjusting your search or filters' 
                        : selectedFolder 
                          ? `This folder is empty. Upload files to the ${folders.find(f => f.id === selectedFolder)?.name} folder.`
                          : 'No files match the current filters.'}
                    </p>
                    <button
                      onClick={() => setShowUploadModal(true)}
                      className="inline-flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                    >
                      <Upload className="w-4 h-4" />
                      <span>Upload Files</span>
                    </button>
                  </div>
                ) : viewMode === 'list' ? (
                  <div className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700">
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead className="bg-gray-50 dark:bg-gray-700">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              <button 
                                onClick={() => handleSort('name')}
                                className="flex items-center gap-1"
                              >
                                Name
                                {sortBy === 'name' && (
                                  sortDirection === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                                )}
                              </button>
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Folder
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              <button 
                                onClick={() => handleSort('size')}
                                className="flex items-center gap-1"
                              >
                                Size
                                {sortBy === 'size' && (
                                  sortDirection === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                                )}
                              </button>
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              <button 
                                onClick={() => handleSort('date')}
                                className="flex items-center gap-1"
                              >
                                Last Modified
                                {sortBy === 'date' && (
                                  sortDirection === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                                )}
                              </button>
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Status
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Actions
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                          {filteredFiles.map((file) => (
                            <tr 
                              key={file.id}
                              className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer"
                              onClick={() => handleViewFile(file)}
                            >
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex items-center gap-3">
                                  {getFileIcon(file.type)}
                                  <div>
                                    <div className="text-sm font-medium text-gray-900 dark:text-white">{file.name}</div>
                                    {file.version && (
                                      <div className="text-xs text-gray-500 dark:text-gray-400">v{file.version}</div>
                                    )}
                                  </div>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className="text-sm text-gray-600 dark:text-gray-400">
                                  {folders.find(f => f.id === file.folder)?.name}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className="text-sm text-gray-600 dark:text-gray-400">
                                  {file.status === 'missing' ? '-' : formatFileSize(file.size)}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className="text-sm text-gray-600 dark:text-gray-400">
                                  {file.status === 'missing' ? '-' : new Date(file.lastModified).toLocaleDateString()}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                {file.status === 'present' ? (
                                  <span className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-xs font-medium flex items-center gap-1 w-fit">
                                    <CheckCircle className="w-3 h-3" />
                                    Present
                                  </span>
                                ) : file.status === 'missing' ? (
                                  <span className="px-2 py-1 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-full text-xs font-medium flex items-center gap-1 w-fit">
                                    <AlertCircle className="w-3 h-3" />
                                    Missing
                                  </span>
                                ) : (
                                  <span className="px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 rounded-full text-xs font-medium flex items-center gap-1 w-fit">
                                    <Clock className="w-3 h-3" />
                                    Outdated
                                  </span>
                                )}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex items-center gap-2" onClick={e => e.stopPropagation()}>
                                  {file.status === 'present' && (
                                    <button className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
                                      <Download className="w-4 h-4" />
                                    </button>
                                  )}
                                  {file.status === 'missing' && (
                                    <button 
                                      onClick={() => {
                                        setSelectedFile(file);
                                        setShowUploadModal(true);
                                      }}
                                      className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                                    >
                                      <Upload className="w-4 h-4" />
                                    </button>
                                  )}
                                  <button className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
                                    <Share2 className="w-4 h-4" />
                                  </button>
                                  <button 
                                    onClick={() => handleDeleteFile(file.id)}
                                    className="p-1 text-red-500 hover:text-red-700 transition-colors"
                                  >
                                    <Trash2 className="w-4 h-4" />
                                  </button>
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    {filteredFiles.map((file) => (
                      <div
                        key={file.id}
                        onClick={() => handleViewFile(file)}
                        className="bg-white dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600 hover:shadow-md transition-shadow cursor-pointer"
                      >
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-3">
                            {getFileIcon(file.type)}
                            <div className="flex-1 min-w-0">
                              <h4 className="font-medium text-gray-900 dark:text-white text-sm truncate">{file.name}</h4>
                              {file.version && (
                                <p className="text-xs text-gray-500 dark:text-gray-400">v{file.version}</p>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center">
                            {file.status === 'present' ? (
                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            ) : file.status === 'missing' ? (
                              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                            ) : (
                              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-3">
                          <span>{folders.find(f => f.id === file.folder)?.name}</span>
                          <span>{file.status === 'missing' ? '-' : formatFileSize(file.size)}</span>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {file.status === 'missing' ? '-' : new Date(file.lastModified).toLocaleDateString()}
                          </span>
                          <div className="flex items-center gap-1" onClick={e => e.stopPropagation()}>
                            {file.status === 'present' && (
                              <button className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
                                <Download className="w-4 h-4" />
                              </button>
                            )}
                            {file.status === 'missing' && (
                              <button 
                                onClick={() => {
                                  setSelectedFile(file);
                                  setShowUploadModal(true);
                                }}
                                className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                              >
                                <Upload className="w-4 h-4" />
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </div>

      {/* File Details Modal */}
      {showFileDetails && selectedFile && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4 overflow-y-auto">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-3xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-4">
                  {getFileIcon(selectedFile.type)}
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white">{selectedFile.name}</h3>
                    {selectedFile.version && (
                      <p className="text-sm text-gray-600 dark:text-gray-400">Version {selectedFile.version}</p>
                    )}
                  </div>
                </div>
                <button 
                  onClick={() => setShowFileDetails(false)}
                  className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">File Information</h4>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  {selectedFile.description || 'No description available.'}
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Folder</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {folders.find(f => f.id === selectedFile.folder)?.name}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Size</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {selectedFile.status === 'missing' ? '-' : formatFileSize(selectedFile.size)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Created By</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {selectedFile.createdBy || '-'}
                    </p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Status</p>
                    <p className="font-medium text-gray-900 dark:text-white flex items-center gap-2">
                      {selectedFile.status === 'present' ? (
                        <>
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <span>Present</span>
                        </>
                      ) : selectedFile.status === 'missing' ? (
                        <>
                          <AlertCircle className="w-4 h-4 text-red-500" />
                          <span>Missing</span>
                        </>
                      ) : (
                        <>
                          <AlertCircle className="w-4 h-4 text-yellow-500" />
                          <span>Outdated</span>
                        </>
                      )}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Last Modified</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {selectedFile.status === 'missing' ? '-' : new Date(selectedFile.lastModified).toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">File Type</p>
                    <p className="font-medium text-gray-900 dark:text-white uppercase">
                      {selectedFile.type}
                    </p>
                  </div>
                </div>
              </div>

              <div className="mb-6">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Shared With</h4>
                <div className="flex flex-wrap gap-2">
                  {selectedFile.sharedWith.length > 0 ? (
                    selectedFile.sharedWith.map((group, index) => (
                      <span 
                        key={index}
                        className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-xs font-medium"
                      >
                        {group}
                      </span>
                    ))
                  ) : (
                    <span className="text-gray-500 dark:text-gray-400 text-sm">Not shared</span>
                  )}
                </div>
              </div>

              <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-2">
                  {selectedFile.status === 'present' ? (
                    <button className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                      <Download className="w-4 h-4" />
                      <span>Download</span>
                    </button>
                  ) : (
                    <button 
                      onClick={() => {
                        setShowFileDetails(false);
                        setShowUploadModal(true);
                      }}
                      className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                    >
                      <Upload className="w-4 h-4" />
                      <span>Upload</span>
                    </button>
                  )}
                  <button className="flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors">
                    <Share2 className="w-4 h-4" />
                    <span>Share</span>
                  </button>
                </div>
                <div className="flex items-center gap-2">
                  <button className="p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
                    <Edit className="w-4 h-4" />
                  </button>
                  <button 
                    onClick={() => {
                      handleDeleteFile(selectedFile.id);
                      setShowFileDetails(false);
                    }}
                    className="p-2 text-red-500 hover:text-red-700 transition-colors"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* New Folder Modal */}
      {showNewFolderModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">Create New Folder</h3>
                <button 
                  onClick={() => setShowNewFolderModal(false)}
                  className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Folder Name
                  </label>
                  <input
                    type="text"
                    value={newFolderName}
                    onChange={(e) => setNewFolderName(e.target.value)}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="Enter folder name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Description (Optional)
                  </label>
                  <textarea
                    value={newFolderDescription}
                    onChange={(e) => setNewFolderDescription(e.target.value)}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="Enter folder description"
                    rows={3}
                  />
                </div>
              </div>

              <div className="flex gap-3 mt-6">
                <button
                  onClick={handleCreateFolder}
                  disabled={!newFolderName.trim()}
                  className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white rounded-lg transition-colors disabled:cursor-not-allowed"
                >
                  <Plus className="w-4 h-4" />
                  Create Folder
                </button>
                <button
                  onClick={() => setShowNewFolderModal(false)}
                  className="px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Upload Modal */}
      {showUploadModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                  {selectedFile && selectedFile.status === 'missing' 
                    ? `Upload ${selectedFile.name}` 
                    : 'Upload Files'}
                </h3>
                <button 
                  onClick={() => {
                    setShowUploadModal(false);
                    if (selectedFile && selectedFile.status === 'missing') {
                      setSelectedFile(null);
                    }
                  }}
                  className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center mb-6">
                <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-400 mb-2">
                  Drag and drop files here, or click to browse
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-500">
                  Supports PDF, DOCX, XLSX, PPTX, JPG, PNG (max 50MB)
                </p>
                <button className="mt-4 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                  Browse Files
                </button>
              </div>

              {!selectedFile && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Destination Folder
                  </label>
                  <select
                    defaultValue={selectedFolder || ''}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    <option value="" disabled>Select a folder</option>
                    {folders.map((folder) => (
                      <option key={folder.id} value={folder.id}>{folder.name}</option>
                    ))}
                  </select>
                </div>
              )}

              <div className="flex gap-3">
                <button
                  onClick={handleUploadFile}
                  className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                >
                  <Upload className="w-4 h-4" />
                  Upload
                </button>
                <button
                  onClick={() => {
                    setShowUploadModal(false);
                    if (selectedFile && selectedFile.status === 'missing') {
                      setSelectedFile(null);
                    }
                  }}
                  className="px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};