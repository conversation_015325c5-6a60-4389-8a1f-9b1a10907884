import React from 'react';
import { Link } from 'react-router-dom';
import { Shield, Lock, Eye, Database, Users, FileText, Mail, Calendar } from 'lucide-react';

export const PrivacyPolicy: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
      {/* Header */}
      <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                <Shield className="w-6 h-6 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-gray-900 dark:text-white">Privacy Policy</h1>
            </div>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Your privacy is important to us. This policy explains how we collect, use, and protect your information.
            </p>
            <div className="mt-4 text-sm text-gray-500 dark:text-gray-400">
              Last updated: January 1, 2025
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 space-y-8">
          
          {/* Introduction */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <FileText className="w-6 h-6 text-blue-600" />
              Introduction
            </h2>
            <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
              AI Co-Founder ("we," "our," or "us") is committed to protecting your privacy. This Privacy Policy explains how we collect, 
              use, disclose, and safeguard your information when you use our AI-powered startup platform and services. By using our 
              services, you agree to the collection and use of information in accordance with this policy.
            </p>
          </section>

          {/* Information We Collect */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Database className="w-6 h-6 text-green-600" />
              Information We Collect
            </h2>
            
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Personal Information</h3>
                <ul className="list-disc list-inside text-gray-600 dark:text-gray-400 space-y-1">
                  <li>Name, email address, and contact information</li>
                  <li>Account credentials and authentication data</li>
                  <li>Profile information and preferences</li>
                  <li>Payment and billing information</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Business Information</h3>
                <ul className="list-disc list-inside text-gray-600 dark:text-gray-400 space-y-1">
                  <li>Startup ideas, business plans, and strategic documents</li>
                  <li>Financial projections and business models</li>
                  <li>Market research and competitive analysis data</li>
                  <li>Technical specifications and code repositories</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Usage Data</h3>
                <ul className="list-disc list-inside text-gray-600 dark:text-gray-400 space-y-1">
                  <li>AI agent interactions and conversation history</li>
                  <li>Platform usage patterns and feature utilization</li>
                  <li>Device information and browser data</li>
                  <li>IP addresses and location data</li>
                </ul>
              </div>
            </div>
          </section>

          {/* How We Use Information */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Users className="w-6 h-6 text-purple-600" />
              How We Use Your Information
            </h2>
            
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                <h3 className="font-semibold text-blue-900 dark:text-blue-200 mb-2">Service Delivery</h3>
                <ul className="text-sm text-blue-800 dark:text-blue-300 space-y-1">
                  <li>• Provide AI co-founder services</li>
                  <li>• Generate business documents</li>
                  <li>• Enable real-time collaboration</li>
                  <li>• Process payments and billing</li>
                </ul>
              </div>

              <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                <h3 className="font-semibold text-green-900 dark:text-green-200 mb-2">Platform Improvement</h3>
                <ul className="text-sm text-green-800 dark:text-green-300 space-y-1">
                  <li>• Enhance AI agent capabilities</li>
                  <li>• Improve user experience</li>
                  <li>• Develop new features</li>
                  <li>• Analyze usage patterns</li>
                </ul>
              </div>

              <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
                <h3 className="font-semibold text-purple-900 dark:text-purple-200 mb-2">Communication</h3>
                <ul className="text-sm text-purple-800 dark:text-purple-300 space-y-1">
                  <li>• Send service updates</li>
                  <li>• Provide customer support</li>
                  <li>• Share relevant content</li>
                  <li>• Notify about new features</li>
                </ul>
              </div>

              <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4">
                <h3 className="font-semibold text-orange-900 dark:text-orange-200 mb-2">Legal Compliance</h3>
                <ul className="text-sm text-orange-800 dark:text-orange-300 space-y-1">
                  <li>• Comply with regulations</li>
                  <li>• Prevent fraud and abuse</li>
                  <li>• Protect user safety</li>
                  <li>• Enforce terms of service</li>
                </ul>
              </div>
            </div>
          </section>

          {/* Data Security */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Lock className="w-6 h-6 text-red-600" />
              Data Security
            </h2>
            
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                We implement industry-standard security measures to protect your information:
              </p>
              
              <div className="grid md:grid-cols-2 gap-4">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Shield className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Encryption</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">AES-256 encryption for data at rest and TLS 1.3 for data in transit</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Lock className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Access Control</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Role-based access control and multi-factor authentication</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Eye className="w-4 h-4 text-purple-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Monitoring</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">24/7 security monitoring and threat detection</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Database className="w-4 h-4 text-orange-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Backup</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Regular automated backups with disaster recovery</p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Your Rights */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Users className="w-6 h-6 text-indigo-600" />
              Your Rights
            </h2>
            
            <div className="space-y-4">
              <div className="border-l-4 border-blue-500 pl-4">
                <h3 className="font-semibold text-gray-900 dark:text-white">Access & Portability</h3>
                <p className="text-gray-600 dark:text-gray-400">Request access to your personal data and receive a copy in a portable format.</p>
              </div>

              <div className="border-l-4 border-green-500 pl-4">
                <h3 className="font-semibold text-gray-900 dark:text-white">Correction & Update</h3>
                <p className="text-gray-600 dark:text-gray-400">Request correction of inaccurate or incomplete personal information.</p>
              </div>

              <div className="border-l-4 border-red-500 pl-4">
                <h3 className="font-semibold text-gray-900 dark:text-white">Deletion</h3>
                <p className="text-gray-600 dark:text-gray-400">Request deletion of your personal data, subject to legal and contractual obligations.</p>
              </div>

              <div className="border-l-4 border-purple-500 pl-4">
                <h3 className="font-semibold text-gray-900 dark:text-white">Opt-out</h3>
                <p className="text-gray-600 dark:text-gray-400">Opt-out of marketing communications and certain data processing activities.</p>
              </div>
            </div>
          </section>

          {/* Contact Information */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Mail className="w-6 h-6 text-blue-600" />
              Contact Us
            </h2>
            
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                If you have questions about this Privacy Policy or want to exercise your rights, contact us:
              </p>
              
              <div className="space-y-2">
                <p className="text-gray-900 dark:text-white">
                  <strong>Email:</strong> <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-700"><EMAIL></a>
                </p>
                <p className="text-gray-900 dark:text-white">
                  <strong>Address:</strong> AI Co-Founder Privacy Team, 123 Innovation Drive, San Francisco, CA 94105
                </p>
                <p className="text-gray-900 dark:text-white">
                  <strong>Response Time:</strong> We respond to privacy requests within 30 days
                </p>
              </div>
            </div>
          </section>

          {/* Updates */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Calendar className="w-6 h-6 text-green-600" />
              Policy Updates
            </h2>
            
            <p className="text-gray-600 dark:text-gray-400">
              We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new 
              Privacy Policy on this page and updating the "Last updated" date. We encourage you to review this Privacy 
              Policy periodically for any changes.
            </p>
          </section>

          {/* Back to Home */}
          <div className="pt-8 border-t border-gray-200 dark:border-gray-700">
            <Link 
              to="/" 
              className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 font-medium"
            >
              ← Back to Home
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};
