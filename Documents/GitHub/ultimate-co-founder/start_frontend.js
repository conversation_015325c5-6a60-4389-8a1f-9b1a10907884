#!/usr/bin/env node
/**
 * Frontend Startup Script for Ultimate Co-founder Platform
 * Handles dependency checking, environment setup, and development server startup
 */

const fs = require('fs');
const path = require('path');
const { spawn, exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

class FrontendStarter {
    constructor() {
        this.projectRoot = process.cwd();
        this.packageJsonPath = path.join(this.projectRoot, 'package.json');
        this.envPath = path.join(this.projectRoot, '.env');
    }

    async checkNodeVersion() {
        console.log('Checking Node.js version...');
        try {
            const { stdout } = await execAsync('node --version');
            const version = stdout.trim();
            const majorVersion = parseInt(version.substring(1).split('.')[0]);
            
            if (majorVersion < 16) {
                console.error('ERROR: Node.js 16 or higher is required');
                console.error(`Current version: ${version}`);
                return false;
            }
            
            console.log(`Node.js version: ${version} - OK`);
            return true;
        } catch (error) {
            console.error('ERROR: Node.js not found');
            return false;
        }
    }

    async checkPackageManager() {
        console.log('Checking package manager...');
        
        // Check for npm
        try {
            const { stdout } = await execAsync('npm --version');
            console.log(`npm version: ${stdout.trim()} - OK`);
            return 'npm';
        } catch (error) {
            console.error('ERROR: npm not found');
            return null;
        }
    }

    checkPackageJson() {
        console.log('Checking package.json...');
        
        if (!fs.existsSync(this.packageJsonPath)) {
            console.error('ERROR: package.json not found');
            return false;
        }
        
        try {
            const packageJson = JSON.parse(fs.readFileSync(this.packageJsonPath, 'utf8'));
            
            // Check for required scripts
            const requiredScripts = ['dev', 'build'];
            const missingScripts = requiredScripts.filter(script => !packageJson.scripts[script]);
            
            if (missingScripts.length > 0) {
                console.warn(`WARNING: Missing scripts: ${missingScripts.join(', ')}`);
            }
            
            console.log('package.json: OK');
            return true;
        } catch (error) {
            console.error('ERROR: Invalid package.json');
            return false;
        }
    }

    async checkDependencies(packageManager) {
        console.log('Checking dependencies...');
        
        const nodeModulesPath = path.join(this.projectRoot, 'node_modules');
        
        if (!fs.existsSync(nodeModulesPath)) {
            console.log('node_modules not found. Installing dependencies...');
            return await this.installDependencies(packageManager);
        }
        
        // Check if package-lock.json is newer than node_modules
        const packageLockPath = path.join(this.projectRoot, 'package-lock.json');
        
        if (fs.existsSync(packageLockPath)) {
            const packageLockStats = fs.statSync(packageLockPath);
            const nodeModulesStats = fs.statSync(nodeModulesPath);
            
            if (packageLockStats.mtime > nodeModulesStats.mtime) {
                console.log('Dependencies may be outdated. Updating...');
                return await this.installDependencies(packageManager);
            }
        }
        
        console.log('Dependencies: OK');
        return true;
    }

    async installDependencies(packageManager) {
        console.log(`Installing dependencies with ${packageManager}...`);
        
        return new Promise((resolve) => {
            const installProcess = spawn(packageManager, ['install'], {
                stdio: 'inherit',
                shell: true
            });
            
            installProcess.on('close', (code) => {
                if (code === 0) {
                    console.log('Dependencies installed successfully!');
                    resolve(true);
                } else {
                    console.error('ERROR: Failed to install dependencies');
                    resolve(false);
                }
            });
        });
    }

    checkEnvironmentFile() {
        console.log('Checking environment configuration...');
        
        if (!fs.existsSync(this.envPath)) {
            console.log('WARNING: .env file not found');
            console.log('Creating basic .env file...');
            this.createBasicEnvFile();
            return true;
        }
        
        // Check for critical environment variables
        const envContent = fs.readFileSync(this.envPath, 'utf8');
        const requiredVars = ['VITE_API_URL'];
        const missingVars = requiredVars.filter(varName => !envContent.includes(`${varName}=`));
        
        if (missingVars.length > 0) {
            console.warn(`WARNING: Missing environment variables: ${missingVars.join(', ')}`);
            console.log('Please check your .env file');
        }
        
        console.log('Environment file: OK');
        return true;
    }

    createBasicEnvFile() {
        const envContent = `# Backend API
VITE_API_URL=http://localhost:8000

# AI Services
VITE_OPENAI_API_KEY=

# Video/Voice Services
VITE_LIVEKIT_URL=
VITE_LIVEKIT_API_KEY=
VITE_LIVEKIT_API_SECRET=

# Database
VITE_SUPABASE_URL=
VITE_SUPABASE_ANON_KEY=

# Optional Enhanced Features
VITE_COMPOSIO_API_KEY=
VITE_TAVUS_API_KEY=
VITE_TOPVIEW_API_KEY=
VITE_TOPVIEW_UID=
VITE_ADOBE_PDF_CLIENT_ID=
VITE_APOLLO_API_KEY=
VITE_SENDGRID_API_KEY=
VITE_STRIPE_PUBLISHABLE_KEY=
VITE_GOOGLE_ANALYTICS_ID=
`;
        
        fs.writeFileSync(this.envPath, envContent);
        console.log('Basic .env file created!');
    }

    async checkBackendConnection() {
        console.log('Checking backend connection...');
        
        try {
            const response = await fetch('http://localhost:8000/health');
            if (response.ok) {
                console.log('Backend connection: OK');
                return true;
            } else {
                console.warn('WARNING: Backend not responding');
                console.log('Make sure to start the backend server first');
                return true; // Don't fail, just warn
            }
        } catch (error) {
            console.warn('WARNING: Cannot connect to backend');
            console.log('Make sure to start the backend server first');
            return true; // Don't fail, just warn
        }
    }

    async startDevelopmentServer(packageManager) {
        console.log('Starting Ultimate Co-founder frontend development server...');
        console.log('Server will be available at: http://localhost:5173');
        console.log('Press Ctrl+C to stop the server');
        console.log('-'.repeat(50));
        
        const devProcess = spawn(packageManager, ['run', 'dev'], {
            stdio: 'inherit',
            shell: true
        });
        
        process.on('SIGINT', () => {
            console.log('\nStopping development server...');
            devProcess.kill('SIGINT');
            process.exit(0);
        });
        
        devProcess.on('close', (code) => {
            console.log(`Development server exited with code ${code}`);
        });
    }

    async run() {
        console.log('Ultimate Co-founder Frontend Startup');
        console.log('='.repeat(50));
        
        const checks = [
            { name: 'Node.js Version', func: () => this.checkNodeVersion() },
            { name: 'Package Manager', func: () => this.checkPackageManager() },
            { name: 'Package.json', func: () => this.checkPackageJson() },
            { name: 'Environment File', func: () => this.checkEnvironmentFile() },
            { name: 'Backend Connection', func: () => this.checkBackendConnection() }
        ];
        
        let packageManager = null;
        
        for (const check of checks) {
            console.log(`\n--- ${check.name} ---`);
            const result = await check.func();
            
            if (check.name === 'Package Manager') {
                packageManager = result;
                if (!packageManager) {
                    console.error('FAILED: Package Manager');
                    process.exit(1);
                }
            } else if (!result) {
                console.error(`FAILED: ${check.name}`);
                process.exit(1);
            }
        }
        
        // Check dependencies after we have a package manager
        console.log('\n--- Dependencies ---');
        if (!await this.checkDependencies(packageManager)) {
            console.error('FAILED: Dependencies');
            process.exit(1);
        }
        
        console.log('\n' + '='.repeat(50));
        console.log('All checks passed! Starting development server...');
        console.log('='.repeat(50));
        
        await this.startDevelopmentServer(packageManager);
    }
}

// Run the frontend starter
if (require.main === module) {
    const starter = new FrontendStarter();
    starter.run().catch(error => {
        console.error('Startup error:', error);
        process.exit(1);
    });
}

module.exports = FrontendStarter;
