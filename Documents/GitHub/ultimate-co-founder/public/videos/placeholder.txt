# Video Placeholder Files

This directory should contain the following video files for agent avatars:

- agent_strategic.mp4
- agent_product.mp4
- agent_technical.mp4
- agent_operations.mp4
- agent_marketing.mp4
- agent_default.mp4

Since we cannot include actual video files in this environment, you'll need to:

1. Add actual MP4 video files to this directory
2. Or replace the video URLs in the services with working external URLs that don't have CORS restrictions
3. Or implement a fallback mechanism in the TavusAvatarView component to handle missing videos gracefully

For now, the application will attempt to load these local video files, which will prevent CORS errors but may result in 404 errors until actual video files are provided.