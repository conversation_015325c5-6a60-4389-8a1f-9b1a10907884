#!/usr/bin/env python3
"""
Production Readiness Test for Ultimate Co-founder Application
Tests both authentication and CrewAI functionality
"""

import asyncio
import aiohttp
import json
import sys
from datetime import datetime

# Test Configuration
BASE_URL = "http://localhost:8000"
DEMO_EMAIL = "<EMAIL>"
DEMO_PASSWORD = "demo123"

class ProductionReadinessTest:
    def __init__(self):
        self.session = None
        self.auth_token = None
        self.test_results = []

    async def setup(self):
        """Setup test session"""
        self.session = aiohttp.ClientSession()
        print("🚀 Starting Production Readiness Test")
        print("=" * 60)

    async def cleanup(self):
        """Cleanup test session"""
        if self.session:
            await self.session.close()

    def log_result(self, test_name: str, success: bool, details: str = ""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        self.test_results.append({
            "test": test_name,
            "success": success,
            "details": details,
            "timestamp": datetime.now().isoformat()
        })
        print(f"{status} {test_name}")
        if details:
            print(f"    {details}")

    async def test_authentication(self):
        """Test authentication functionality"""
        print("\n📋 Testing Authentication System")
        print("-" * 40)

        # Test 1: Login with demo credentials
        try:
            login_data = aiohttp.FormData()
            login_data.add_field('username', DEMO_EMAIL)
            login_data.add_field('password', DEMO_PASSWORD)
            
            async with self.session.post(f"{BASE_URL}/api/v1/auth/login", data=login_data) as response:
                if response.status == 200:
                    auth_data = await response.json()
                    self.auth_token = auth_data.get('access_token')
                    self.log_result("Authentication Login", True, f"Token received: {self.auth_token[:20]}...")
                else:
                    error_text = await response.text()
                    self.log_result("Authentication Login", False, f"Status: {response.status}, Error: {error_text}")
                    return False
        except Exception as e:
            self.log_result("Authentication Login", False, f"Exception: {str(e)}")
            return False

        # Test 2: Verify token with protected endpoint
        try:
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            async with self.session.get(f"{BASE_URL}/api/v1/auth/me", headers=headers) as response:
                if response.status == 200:
                    user_data = await response.json()
                    self.log_result("Token Verification", True, f"User: {user_data.get('email', 'Unknown')}")
                else:
                    error_text = await response.text()
                    self.log_result("Token Verification", False, f"Status: {response.status}, Error: {error_text}")
        except Exception as e:
            self.log_result("Token Verification", False, f"Exception: {str(e)}")

        return True

    async def test_crewai_functionality(self):
        """Test CrewAI functionality"""
        print("\n🤖 Testing CrewAI Multi-Agent System")
        print("-" * 40)

        # Test 1: Get available agents
        try:
            async with self.session.get(f"{BASE_URL}/api/v1/agents/") as response:
                if response.status == 200:
                    agents_data = await response.json()
                    agents = agents_data.get('agents', [])
                    agent_count = len(agents)
                    self.log_result("Agent Discovery", True, f"Found {agent_count} agents: {[a['id'] for a in agents]}")
                else:
                    error_text = await response.text()
                    self.log_result("Agent Discovery", False, f"Status: {response.status}, Error: {error_text}")
                    return False
        except Exception as e:
            self.log_result("Agent Discovery", False, f"Exception: {str(e)}")
            return False

        # Test 2: Single agent task execution
        try:
            task_data = {
                "description": "Analyze the market opportunity for an AI-powered project management tool",
                "context": "Targeting small to medium businesses in the tech sector"
            }
            
            async with self.session.post(
                f"{BASE_URL}/api/v1/agents/execute?agent_id=strategic",
                json=task_data
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    task_id = result.get('task_id', 'Unknown')
                    agent_id = result.get('agent_id', 'Unknown')
                    mock_mode = result.get('metadata', {}).get('mock_mode', False)
                    self.log_result("Single Agent Execution", True, 
                                  f"Agent: {agent_id}, Task ID: {task_id}, Mock Mode: {mock_mode}")
                else:
                    error_text = await response.text()
                    self.log_result("Single Agent Execution", False, f"Status: {response.status}, Error: {error_text}")
        except Exception as e:
            self.log_result("Single Agent Execution", False, f"Exception: {str(e)}")

        # Test 3: Multi-agent collaboration
        try:
            multi_task_data = {
                "description": "Create a comprehensive go-to-market strategy for an AI startup",
                "agent_ids": ["strategic", "product", "marketing"]
            }
            
            async with self.session.post(
                f"{BASE_URL}/api/v1/agents/execute-multi",
                json=multi_task_data
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    results = result.get('results', [])
                    agent_count = len(results)
                    agent_ids = [r.get('agent_id') for r in results]
                    self.log_result("Multi-Agent Collaboration", True, 
                                  f"Executed with {agent_count} agents: {agent_ids}")
                else:
                    error_text = await response.text()
                    self.log_result("Multi-Agent Collaboration", False, f"Status: {response.status}, Error: {error_text}")
        except Exception as e:
            self.log_result("Multi-Agent Collaboration", False, f"Exception: {str(e)}")

        # Test 4: Individual agent details
        try:
            async with self.session.get(f"{BASE_URL}/api/v1/agents/strategic") as response:
                if response.status == 200:
                    agent_data = await response.json()
                    role = agent_data.get('role', 'Unknown')
                    self.log_result("Agent Details", True, f"Strategic Agent Role: {role}")
                else:
                    error_text = await response.text()
                    self.log_result("Agent Details", False, f"Status: {response.status}, Error: {error_text}")
        except Exception as e:
            self.log_result("Agent Details", False, f"Exception: {str(e)}")

        return True

    async def generate_report(self):
        """Generate final test report"""
        print("\n📊 Production Readiness Report")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['test']}: {result['details']}")
        
        print("\n🎯 Production Readiness Status:")
        if failed_tests == 0:
            print("✅ PRODUCTION READY - All systems operational")
        elif failed_tests <= 2:
            print("⚠️  MOSTLY READY - Minor issues detected")
        else:
            print("❌ NOT READY - Critical issues need resolution")
        
        return failed_tests == 0

    async def run_all_tests(self):
        """Run all production readiness tests"""
        await self.setup()
        
        try:
            # Run authentication tests
            auth_success = await self.test_authentication()
            
            # Run CrewAI tests
            crewai_success = await self.test_crewai_functionality()
            
            # Generate final report
            is_production_ready = await self.generate_report()
            
            return is_production_ready
            
        finally:
            await self.cleanup()

async def main():
    """Main test execution"""
    tester = ProductionReadinessTest()
    is_ready = await tester.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if is_ready else 1)

if __name__ == "__main__":
    asyncio.run(main())
